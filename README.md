# Astro Platform - 低代码微前端配置平台

> 业务全配置低代码平台，支持可视化页面编辑、实时协作、组件管理和设计稿转代码

## 🚀 快速开始

### 环境要求
- Node.js >= 16
- pnpm >= 7

### 安装和运行
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建项目
pnpm build

# 代码检查
pnpm lint
```

访问 https://localhost:3311

## ✨ 核心功能

- 🎨 **可视化编辑**：拖拽式页面构建，所见即所得
- 👥 **实时协作**：多人同时编辑，实时同步状态
- 🧩 **组件生态**：丰富的物料库和组件管理系统
- 🎯 **设计稿转代码**：支持 Figma 设计稿自动转换
- 📱 **多端预览**：支持 H5、PC、内嵌页预览
- 🔐 **权限管理**：基于角色的访问控制
- 📊 **数据统计**：项目使用情况和效率分析

## 🏗️ 技术架构

- **前端框架**：React 18 + TypeScript
- **构建工具**：EMP (微前端框架)
- **状态管理**：Valtio (响应式状态管理)
- **UI 组件库**：@astro/ui (内部组件库)
- **实时通信**：WebSocket
- **样式方案**：SCSS + CSS Modules + Emotion

## 📖 开发指南

### 功能开发流程
1. 从 master 分支创建 feature-xxx 分支
2. 合并到 test 分支进行测试验证（自动发布）
3. 合并到 master 分支上线（自动构建，手动发布）

⚠️ **注意**：不要将 test 分支直接合并到 master 分支

### 本地组件调试映射
创建 `public/local.dev.json` 文件进行本地组件映射：

```json
{
    "https://unpkg.yy.com/@astro-ui/act_layout@0.32.0/dist/emp.js": {
        "scope": "astro_ui_act_layout_0_0_1",
        "url": "https://127.0.0.1:8001/emp.js"
    },
    "https://unpkg.yy.com/@astro-ui/act_layout@0.32.0/dist/astro/Layout.json": "https://127.0.0.1:8001/astro/Layout.json"
}
```

添加配置后需要重新启动项目：`pnpm dev`

## 🔗 相关链接

- **部署地址**：https://s.sysop.yy.com/service/overview/3@friend@astro-platform/release/container
- **详细文档**：[PROJECT_DOCUMENTATION.md](./PROJECT_DOCUMENTATION.md)
- **测试环境**：https://astro-platform-test.yy.com
- **生产环境**：https://astro-platform.yy.com

## 📋 项目结构

```
src/
├── components/          # 组件目录
│   ├── common/         # 通用组件
│   ├── dashboard/      # 仪表板
│   ├── playload/       # 项目编辑器
│   ├── Material/       # 物料库管理
│   └── Statistics/     # 数据统计
├── services/           # API 服务层
├── config/            # 配置文件
├── utils/             # 工具函数
└── assets/            # 静态资源
```

## 🤝 贡献指南

1. **代码规范**：使用 Biome 进行代码格式化
2. **提交规范**：使用语义化提交信息
3. **测试要求**：确保功能测试通过
4. **文档更新**：及时更新相关文档

## 📞 联系方式

- **项目维护**：Astro 团队
- **技术支持**：内部技术群
- **问题反馈**：项目 Issue 系统

---

*更多详细信息请查看 [完整项目文档](./PROJECT_DOCUMENTATION.md)*