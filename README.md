# Astro Platfrom 
> 业务全配置低代码平台

### 功能开发流程
从master分支fork一个功能分支feature-xxx分支  
验证：合并feature-xxx分支到test分支自动发布  
上线：合并feature-xxx分支到master分支自动构建，并到服务治理手动点击发布  
注意不要将test分支直接合并到master分支

### 部署地址
https://s.sysop.yy.com/service/overview/3@friend@astro-platform/release/container

### 本地组件调试映射
public/local.dev.json
```json public/local.dev.json
{
    "https://unpkg.yy.com/@astro-ui/act_layout@0.32.0/dist/emp.js": {
        "scope": "astro_ui_act_layout_0_0_1",
        "url": "https://127.0.0.1:8001/emp.js"
    },
    "https://unpkg.yy.com/@astro-ui/act_layout@0.32.0/dist/astro/Layout.json": "https://127.0.0.1:8001/astro/Layout.json",
    "https://unpkg.yy.com/@astro-ui/act_layout@0.32.0/dist/astro/Layout.slot.json": "https://127.0.0.1:8001/astro/Layout.slot.json",
    "https://unpkg.yy.com/@astro-ui/spring-festival-2025-components@0.6.0/dist/emp.js": {
        "scope": "astro_ui_spring_festival_2025_components_0_0_1",
        "url": "https://127.0.0.1:8000/emp.js"
    }
}
```
添加`public/local.dev.json`后，需要重新启动项目。`pnpm dev`
`emp.js`资源需要配置`url`与`scope`，其余JSON资源，只需要直接配置相对应的本地映射地址即可。