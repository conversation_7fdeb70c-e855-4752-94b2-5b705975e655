{"name": "astro-platfrom", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "emp dev", "build": "emp build", "bos": "emp build -ev deploy=true", "start": "emp serve", "stat": "emp build --analyze", "build:doctor": "emp build --doctor", "dev:doctor": "emp dev --doctor", "lint": "biome check . --fix"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@empjs/biome-config": "^0.7.2", "@empjs/cli": "^3.9.0", "@empjs/plugin-lightningcss": "^3.3.3", "@empjs/plugin-react": "^3.8.2", "@empjs/share": "^3.8.2", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-responsive-masonry": "^2.6.0", "@types/react-window": "^1.8.8", "rc-field-form": "^2.7.0", "rc-upload": "^4.8.1", "typescript": "^5.7.2", "typescript-plugin-css-modules": "^5.1.0"}, "dependencies": {"@astro/utils": "^0.0.67", "@baiducloud/sdk": "^1.0.2", "@emotion/css": "^11.13.4", "@monaco-editor/react": "^4.6.0", "classnames": "^2.3.1", "color-name": "^2.0.0", "dayjs": "^1.11.13", "dnd-core": "16.0.1", "echarts": "^5.6.0", "immutability-helper": "^3.1.1", "lodash.debounce": "^4.0.8", "parchment": "^3.0.0", "prop-types": "^15.8.1", "rc-pagination": "^4.3.0", "react-countup": "^6.5.3", "react-diff-viewer": "^3.1.1", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-responsive-masonry": "^2.7.1", "react-router-dom": "^6.27.0", "react-window": "^1.8.11", "valtio": "2.1.2", "wangeditor": "4"}, "resolutions": {"stylus": "0.0.1-security"}}