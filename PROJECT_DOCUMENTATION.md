# Astro Platform - 低代码微前端配置平台

## 项目概述

Astro Platform 是一个业务全配置低代码平台，作为 Astro 生态系统中的配置平台，与渲染器配合使用。该平台允许用户通过可视化界面创建、编辑和管理活动页面，支持组件化开发、实时协作编辑、设计稿转代码等功能。

### 核心价值
- **低代码开发**：通过拖拽组件和配置表单快速构建页面
- **微前端架构**：基于 EMP 微前端框架，支持组件独立开发和部署
- **实时协作**：多人同时编辑，实时同步状态
- **设计稿转代码**：支持 Figma 设计稿自动转换为代码
- **组件生态**：丰富的物料库和组件管理系统

## 技术架构

### 技术栈
- **前端框架**：React 18 + TypeScript
- **构建工具**：EMP (Enterprise Micro-frontend Platform)
- **状态管理**：Valtio (响应式状态管理)
- **路由管理**：React Router v6
- **UI 组件库**：@astro/ui (内部组件库)
- **样式方案**：SCSS + CSS Modules + Emotion
- **代码编辑器**：Monaco Editor
- **实时通信**：WebSocket
- **开发工具**：Biome (代码格式化和检查)

### 微前端架构
项目基于 EMP 微前端框架构建，支持：
- **模块联邦**：组件和页面可独立开发和部署
- **远程模块**：
  - `@astro/ui`: UI 组件库
  - `@astro/adminwebmaster`: 管理后台模块
- **本地开发映射**：支持本地组件调试映射

### 核心模块

#### 1. 页面编辑器 (EditPanel)
- **左侧工具面板**：页面管理、组件库、书签配置
- **中间画布面板**：实时预览和编辑
- **右侧配置面板**：组件属性配置、版本管理、F2C 功能

#### 2. 状态管理系统
- **PageStore**：页面和组件状态管理
- **UserStore**：用户信息和权限管理
- **ComponentStore**：组件库管理
- **F2CStore**：设计稿转代码功能

#### 3. 实时协作系统
- **WebSocket 连接**：基于房间的实时通信
- **操作同步**：页面编辑、组件操作实时同步
- **用户状态**：在线用户显示和状态同步
- **冲突处理**：编辑冲突检测和处理

#### 4. 物料库系统
- **组件管理**：组件的增删改查和分类管理
- **标签系统**：组件标签化管理和搜索
- **版本控制**：组件版本管理和发布
- **批量操作**：支持批量添加和管理组件

## 项目结构

```
src/
├── components/           # 组件目录
│   ├── common/          # 通用组件
│   │   ├── layout/      # 布局组件
│   │   ├── Editor/      # 编辑器组件
│   │   └── FrontEndAi/  # AI 辅助功能
│   ├── dashboard/       # 仪表板
│   ├── playload/        # 项目编辑器
│   │   ├── editPanel/   # 编辑面板
│   │   ├── UserSocket/  # 实时协作
│   │   └── FontEndAgent/# 前端助手
│   ├── Material/        # 物料库管理
│   ├── Statistics/      # 数据统计
│   └── AdminTable/      # 管理表格
├── services/            # API 服务层
│   ├── api.ts          # API 统一导出
│   ├── astro.ts        # Astro 平台接口
│   ├── material.ts     # 物料库接口
│   ├── user.ts         # 用户接口
│   ├── ws.ts           # WebSocket 接口
│   └── f2cFigma.ts     # F2C 接口
├── config/             # 配置文件
│   ├── index.ts        # 主配置
│   ├── project.ts      # 项目配置
│   └── types.ts        # 类型定义
├── utils/              # 工具函数
│   ├── index.ts        # 通用工具
│   ├── f2c.ts          # F2C 相关工具
│   ├── colors.ts       # 颜色工具
│   └── eventBus.ts     # 事件总线
├── assets/             # 静态资源
│   ├── css/            # 样式文件
│   └── img/            # 图片资源
├── Router.tsx          # 路由配置
├── bootstrap.tsx       # 应用启动
├── store.ts            # 状态管理基类
└── global.d.ts         # 全局类型声明
```

## 核心功能

### 1. 项目管理
- **项目列表**：支持搜索、筛选、分页
- **项目创建**：活动信息配置、标签分类
- **项目复制**：快速复制现有项目
- **权限管理**：基于角色的访问控制

### 2. 页面编辑
- **可视化编辑**：拖拽式页面构建
- **组件配置**：表单化组件属性配置
- **实时预览**：多端预览（H5、PC、内嵌页）
- **版本管理**：页面版本控制和回滚

### 3. 组件系统
- **组件库**：丰富的预制组件
- **自定义组件**：支持自定义组件开发
- **组件配置**：动态表单配置
- **插槽系统**：组件嵌套和布局

### 4. 实时协作
- **多人编辑**：支持多人同时编辑
- **状态同步**：实时同步编辑状态
- **操作历史**：操作记录和历史追踪
- **冲突解决**：编辑冲突检测和处理

### 5. 设计稿转代码 (F2C)
- **Figma 集成**：支持 Figma 设计稿导入
- **自动转换**：设计稿自动转换为代码
- **配置管理**：F2C 配置版本管理
- **图片处理**：自动上传和转换图片资源

### 6. 发布部署
- **环境管理**：测试环境和生产环境
- **版本发布**：支持版本发布和回滚
- **预览功能**：发布前预览和测试
- **CDN 集成**：静态资源 CDN 部署

## 开发指南

### 环境要求
- Node.js >= 16
- pnpm >= 7

### 本地开发

1. **安装依赖**
```bash
pnpm install
```

2. **启动开发服务器**
```bash
pnpm dev
```
访问 https://localhost:3311

3. **构建项目**
```bash
pnpm build
```

4. **代码检查**
```bash
pnpm lint
```

### 本地组件调试

创建 `public/local.dev.json` 文件进行本地组件映射：

```json
{
  "https://unpkg.yy.com/@astro-ui/act_layout@0.32.0/dist/emp.js": {
    "scope": "astro_ui_act_layout_0_0_1",
    "url": "https://127.0.0.1:8001/emp.js"
  }
}
```

### 开发流程

1. **功能开发**：从 master 分支创建 feature-xxx 分支
2. **测试验证**：合并到 test 分支自动发布测试环境
3. **生产上线**：合并到 master 分支自动构建，手动发布生产环境

⚠️ **注意**：不要将 test 分支直接合并到 master 分支

## API 接口

### 项目管理接口
- `GET /actProject/list` - 获取项目列表
- `POST /actProject/save` - 保存项目
- `POST /actProject/copy` - 复制项目
- `GET /actProject/projectConfig` - 获取项目配置

### 页面管理接口
- `GET /actProject/pageConfig` - 获取页面配置
- `POST /actProject/saveProjectConfig` - 保存页面配置
- `POST /actProject/publish` - 发布页面

### 物料库接口
- `GET /material/queryMaterial` - 查询组件
- `POST /material/upsertMaterial` - 新增/修改组件
- `DELETE /material/deleteMaterial` - 删除组件
- `GET /material/listTag` - 获取标签列表

### WebSocket 接口
- `wss://astro-ws.yy.com/room/{roomId}` - 实时协作连接
- `GET /roomHistory/{roomId}/{env}` - 获取操作历史

## 部署配置

### 环境配置
- **测试环境**：https://astro-platform-test.yy.com
- **生产环境**：https://astro-platform.yy.com
- **部署地址**：https://s.sysop.yy.com/service/overview/3@friend@astro-platform

### 构建配置
- **开发端口**：3311
- **HTTPS**：启用
- **源码映射**：启用
- **代码分割**：支持懒加载

## 常见问题

### 1. 本地开发问题
- **端口冲突**：修改 `emp.config.ts` 中的端口配置
- **HTTPS 证书**：信任本地开发证书
- **组件映射**：确保 `local.dev.json` 配置正确

### 2. 组件开发问题
- **组件不显示**：检查组件 URL 和 scope 配置
- **表单配置**：确保组件提供正确的配置 JSON
- **样式问题**：检查 CSS 作用域和样式隔离

### 3. 协作编辑问题
- **连接失败**：检查 WebSocket 连接和网络状态
- **状态不同步**：刷新页面重新建立连接
- **编辑冲突**：按照提示解决冲突

## 贡献指南

1. **代码规范**：使用 Biome 进行代码格式化
2. **提交规范**：使用语义化提交信息
3. **测试要求**：确保功能测试通过
4. **文档更新**：及时更新相关文档

## 技术实现细节

### 状态管理架构

项目使用 Valtio 作为状态管理方案，提供响应式状态更新：

```typescript
// 基础 Store 类
export class BaseStore {
  constructor() {
    const state = bind(proxy(deepClone(this)))
    devtools(state, {name: this.constructor.name, enabled: true})
    return state
  }
  get state() {
    return useSnapshot(this) as typeof this
  }
}
```

### 微前端集成

#### EMP 配置
```typescript
// emp.config.ts
export default defineConfig(store => ({
  plugins: [
    pluginRspackEmpShare({
      name: 'HD_Astro_Platform',
      remotes: {
        '@astro/ui': 'Astro_UI@https://unpkg.yy.com/@astro/ui@0.0.1/dist/emp.json',
        '@astro/adminwebmaster': 'AdminWebmaster@https://astro-admin-test.yy.com/emp.json',
      }
    })
  ]
}))
```

#### 组件动态加载
```typescript
// 组件表单配置动态加载
async getComponentForm(component?: Component) {
  const { url, path } = component || {}
  let f_url = url.replace('/emp.js', '/astro/') + path.replace('./', '') + '.json'

  // 本地开发映射
  if (isDev && localDevEmpConfig?.empMap?.[f_url]) {
    f_url = localDevEmpConfig.empMap[f_url]
  }

  const form = await fetch(f_url).then(d => d.json())
  return form
}
```

### 实时协作实现

#### WebSocket 连接管理
```typescript
export class USocket {
  connect() {
    const ws = new WebSocket(config.getSocketByRoomId(this.roomId))
    ws.onopen = this.onopen
    ws.onmessage = this.onmessage
    ws.onclose = this.onclose
  }

  sendPages(data: any) {
    this.send({
      action: 'pages',
      clientId: this.clientId,
      ...data,
    })
  }
}
```

#### 操作同步机制
```typescript
// 页面中心化管理
export class PageCentral {
  addComponent(component: any, pk: string) {
    pageStore.setFormOp(pk)
    this.send({op: [component, pk], act: 'addComponent'})
  }

  editPage(k: string, v: any, oldKey?: string, extend = false) {
    pageStore.setFormOp(k)
    this.send({op: [k, v, oldKey, extend], act: 'editPage'})
  }
}
```

### F2C 设计稿转代码

#### Figma API 集成
```typescript
export const getCode = async (query: any) => {
  const params = new URLSearchParams(query)
  return window.fetch(`https://f2c-figma-api.yy.com/api/nodes?${params}`, {
    method: 'GET',
    headers: {
      'F2c-Api-Platform': 'astro',
    },
  })
}
```

#### 图片资源转换
```typescript
// Figma 图片转 BOS 存储
export const convertUrlInfo = async (originText: string) => {
  const urlRegex = /https?:\/\/[^\s'"]+/g
  const urls = originText.match(urlRegex) || []
  const urlMap = await transformUrls(urls)

  let convertedText = originText
  urlMap.forEach((newUrl, oldUrl) => {
    convertedText = convertedText.replace(new RegExp(oldUrl, 'g'), newUrl)
  })

  return convertedText
}
```

### 组件配置系统

#### 动态表单生成
组件配置通过 JSON Schema 定义，支持：
- 基础类型：文本、数字、布尔值
- 复合类型：对象、数组、选择器
- 自定义组件：颜色选择器、图片上传、代码编辑器

#### 配置数据流
```
组件定义 → JSON Schema → 动态表单 → 配置数据 → 组件渲染
```

### 权限控制系统

#### 角色定义
- **T (技术人员)**：完整开发权限
- **P (产品人员)**：产品配置权限
- **C (普通人员)**：基础使用权限
- **A (管理员)**：系统管理权限

#### 权限检查
```typescript
export class UserStore extends BaseStore {
  get canAccess() {
    return this.isLogin && ['A', 'P', 'T'].includes(this.user.role)
  }
}
```

## 性能优化

### 代码分割
- **路由级分割**：每个页面独立打包
- **组件级分割**：大型组件按需加载
- **第三方库分割**：公共依赖独立打包

### 缓存策略
- **组件配置缓存**：避免重复请求组件配置
- **用户状态缓存**：本地存储用户信息
- **静态资源缓存**：CDN 缓存策略

### 内存管理
- **状态清理**：页面切换时清理无用状态
- **事件解绑**：组件卸载时清理事件监听
- **WebSocket 管理**：连接池和自动重连

## 安全考虑

### 数据安全
- **输入验证**：前后端双重验证
- **XSS 防护**：内容转义和 CSP 策略
- **CSRF 防护**：Token 验证机制

### 权限安全
- **接口鉴权**：基于 Cookie 的身份验证
- **操作审计**：关键操作记录和追踪
- **资源隔离**：项目级权限隔离

## 监控和日志

### 错误监控
- **前端错误捕获**：全局错误处理
- **API 错误监控**：接口调用监控
- **性能监控**：页面加载和渲染性能

### 日志系统
```typescript
export class Logger {
  debug(message: string, ...args: any[]) {
    if (this.level === 'debug') {
      console.log(`[${this.channel}] ${message}`, ...args)
    }
  }
}
```

## 扩展开发

### 自定义组件开发
1. **组件实现**：基于 React 开发组件
2. **配置定义**：提供 JSON Schema 配置
3. **打包发布**：使用 EMP 打包为微前端模块
4. **注册使用**：在物料库中注册组件

### 插件系统
- **编辑器插件**：扩展编辑器功能
- **渲染插件**：自定义渲染逻辑
- **工具插件**：开发辅助工具

### API 扩展
- **自定义接口**：扩展业务接口
- **中间件**：请求/响应处理
- **数据适配**：数据格式转换

## 配置示例

### 环境配置
```typescript
// src/config/index.ts
class Config {
  env: EnvType = 'test'
  isDev = !!process.env.isDev

  get unpkg() {
    return 'https://unpkg.yy.com'
  }

  get renderHost() {
    return this.env === 'prod'
      ? `https://hd-activity.yy.com`
      : `https://hd-activity-test.yy.com`
  }

  get socket() {
    if (this.env === 'test') return `wss://astro-ws-test.yy.com/room`
    return `wss://astro-ws.yy.com/room`
  }
}
```

### 组件配置示例
```json
// 组件配置 JSON Schema
[
  {
    "key": "title",
    "label": "标题",
    "type": "input",
    "defaultValue": "默认标题",
    "rules": [{"required": true, "message": "请输入标题"}]
  },
  {
    "key": "color",
    "label": "颜色",
    "type": "color",
    "defaultValue": "#1890ff"
  },
  {
    "key": "items",
    "label": "列表项",
    "type": "array",
    "itemType": "object",
    "properties": [
      {"key": "text", "label": "文本", "type": "input"},
      {"key": "link", "label": "链接", "type": "input"}
    ]
  }
]
```

### 页面配置示例
```json
// 页面配置结构
{
  "pages": {
    "home": {
      "name": "首页",
      "sort": 1,
      "url": "https://unpkg.yy.com/@astro-ui/layout@1.0.0/dist/emp.js",
      "path": "./Layout",
      "scope": "astro_ui_layout_1_0_0",
      "dataConfig": {},
      "components": [
        {
          "id": 1001,
          "name": "轮播图",
          "uniqueNum": "home_1001_1640995200000",
          "url": "https://unpkg.yy.com/@astro-ui/carousel@1.0.0/dist/emp.js",
          "path": "./Carousel",
          "scope": "astro_ui_carousel_1_0_0",
          "dataConfig": {
            "images": ["image1.jpg", "image2.jpg"],
            "autoplay": true,
            "interval": 3000
          }
        }
      ]
    }
  }
}
```

## 最佳实践

### 开发规范
1. **组件命名**：使用 PascalCase，语义化命名
2. **文件组织**：按功能模块组织，避免深层嵌套
3. **类型定义**：充分利用 TypeScript 类型系统
4. **错误处理**：统一错误处理和用户提示
5. **性能优化**：合理使用 memo、useMemo、useCallback

### 状态管理最佳实践
```typescript
// 推荐的 Store 结构
export class FeatureStore extends BaseStore {
  // 状态定义
  loading = false
  data: DataType[] = []

  // 计算属性
  get filteredData() {
    return this.data.filter(item => item.active)
  }

  // 异步操作
  async fetchData() {
    this.loading = true
    try {
      const result = await api.getData()
      this.data = result
    } finally {
      this.loading = false
    }
  }

  // 状态更新
  updateItem(id: string, updates: Partial<DataType>) {
    const index = this.data.findIndex(item => item.id === id)
    if (index !== -1) {
      this.data[index] = { ...this.data[index], ...updates }
    }
  }
}
```

### 组件开发最佳实践
```typescript
// 推荐的组件结构
interface ComponentProps {
  title: string
  onAction?: (data: any) => void
}

const Component: React.FC<ComponentProps> = memo(({ title, onAction }) => {
  const [state, setState] = useState(initialState)

  const handleAction = useCallback((data: any) => {
    onAction?.(data)
  }, [onAction])

  return (
    <div className={styles.container}>
      <h2>{title}</h2>
      {/* 组件内容 */}
    </div>
  )
})
```

### 错误处理最佳实践
```typescript
// 统一错误处理
export const handleApiError = (error: any) => {
  if (error.response?.status === 401) {
    // 未授权，跳转登录
    showLoginBox()
  } else if (error.response?.status >= 500) {
    // 服务器错误
    message.error('服务器错误，请稍后重试')
  } else {
    // 其他错误
    message.error(error.message || '操作失败')
  }
}

// 在组件中使用
const handleSubmit = async (data: any) => {
  try {
    await api.submitData(data)
    message.success('提交成功')
  } catch (error) {
    handleApiError(error)
  }
}
```

## 故障排查

### 常见问题诊断

#### 1. 组件加载失败
```bash
# 检查组件 URL 是否可访问
curl -I https://unpkg.yy.com/@astro-ui/component@1.0.0/dist/emp.js

# 检查本地映射配置
cat public/local.dev.json
```

#### 2. WebSocket 连接问题
```javascript
// 在浏览器控制台检查连接状态
console.log('WebSocket状态:', uSocket.isConnected)
console.log('连接地址:', config.getSocketByRoomId(projectId))
```

#### 3. 状态同步异常
```javascript
// 检查页面状态
console.log('当前页面:', pageStore.pageKey)
console.log('页面数据:', pageStore.pages)
console.log('组件状态:', pageStore.componentKey)
```

### 调试工具
- **Redux DevTools**：状态变化追踪
- **Network 面板**：API 请求监控
- **Console 日志**：详细日志输出
- **Performance 面板**：性能分析

## 版本信息

### 当前版本
- **版本号**：v1.0.0
- **发布时间**：2024年
- **Node.js 要求**：>= 16.0.0
- **浏览器支持**：Chrome >= 88, Firefox >= 78, Safari >= 14

### 主要依赖版本
- **React**：18.x
- **TypeScript**：5.7.2
- **EMP CLI**：3.9.0
- **Valtio**：2.1.2
- **React Router**：6.27.0

### 更新日志

#### v1.0.0 (2024-12)
- ✨ 初始版本发布
- 🎉 完整的低代码编辑功能
- 🚀 微前端架构支持
- 👥 实时协作编辑
- 🎨 Figma 设计稿转代码
- 📦 物料库管理系统
- 📊 数据统计面板
- 🔐 权限管理系统

#### 计划功能
- 🔄 组件版本管理优化
- 🎯 更多组件类型支持
- 📱 移动端编辑体验优化
- 🤖 AI 辅助开发功能
- 🔍 高级搜索和筛选
- 📈 性能监控和分析

## 相关资源

### 文档链接
- **API 文档**：内部 API 文档系统
- **组件文档**：@astro/ui 组件库文档
- **设计规范**：Astro 设计系统
- **开发指南**：内部开发文档

### 工具链接
- **EMP 官网**：https://emp2.netlify.app/
- **Valtio 文档**：https://github.com/pmndrs/valtio
- **React Router**：https://reactrouter.com/
- **Biome**：https://biomejs.dev/

### 相关项目
- **Astro Renderer**：页面渲染引擎
- **Astro UI**：组件库项目
- **Astro Admin**：管理后台系统
- **Astro Materials**：物料库服务

## 贡献者

感谢所有为 Astro Platform 项目做出贡献的开发者和设计师。

### 核心团队
- **架构设计**：技术架构团队
- **前端开发**：前端开发团队
- **后端开发**：后端开发团队
- **UI/UX 设计**：设计团队
- **产品规划**：产品团队

### 特别感谢
- EMP 微前端框架团队
- 内部组件库维护团队
- 测试和质量保证团队
- 运维和部署团队

## 许可证

本项目为内部项目，版权归公司所有。未经授权不得外传或用于其他用途。

## 联系方式

- **项目维护**：Astro 团队
- **技术支持**：内部技术群
- **问题反馈**：项目 Issue 系统
- **文档更新**：定期维护和更新

---

*最后更新时间：2024年12月*
*文档版本：v1.0.0*
