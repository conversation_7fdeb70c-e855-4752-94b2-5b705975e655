import fs from 'fs'
import path from 'path'
import {defineConfig} from '@empjs/cli'
import pluginlightningcss from '@empjs/plugin-lightningcss'
import pluginReact from '@empjs/plugin-react'
import {externalReact, pluginRspackEmpShare} from '@empjs/share'
export default defineConfig(store => {
  return {
    base: '/',
    server: {
      port: 3311,
      https: true,
      open: false,
    },
    define: {
      isDev: store.mode === 'development',
      isDevLocalFile: fs.existsSync(path.resolve(__dirname, 'public/local.dev.json')),
    },
    build: {
      sourcemap: true,
    },
    html: {
      title: 'Astro',
      meta: {
        charset: {charset: 'utf-8'},
        'http-equiv': {'http-equiv': 'X-UA-Compatible', content: 'IE=edge'},
        viewport: {
          name: 'viewport',
          content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=0',
        },
        keywords: {keywords: 'Astro 业务快配活动平台'},
        description: {description: 'Astro 业务快配活动平台'},
      },
      tags: [
        {
          pos: 'head',
          tagName: 'link',
          attributes: {
            rel: 'dns-prefetch',
            href: 'https://unpkg.yy.com',
          },
        },
      ],
    },
    plugins: [
      pluginReact(),
      pluginlightningcss({}),
      pluginRspackEmpShare({
        name: 'HD_Astro_Platform',
        empRuntime: {
          runtime: {
            lib: `https://unpkg.yy.com/@empjs/share@3.6.0/output/sdk.js`,
          },
          framework: {
            libs: [`https://unpkg.yy.com/@empjs/cdn-react@0.18.0/dist/reactRouter.${store.mode}.umd.js`],
            global: 'EMP_ADAPTER_REACT',
          },
          setExternals: externalReact,
        },
        remotes: {
          '@astro/ui': 'Astro_UI@https://unpkg.yy.com/@astro/ui@0.0.1/dist/emp.json',
          '@astro/adminwebmaster': 'AdminWebmaster@https://astro-admin-test.yy.com/emp.json',
          // '@astro/adminwebmaster': 'AdminWebmaster@https://localhost:8303/emp.json',
        },
        dts: {
          consumeTypes: true,
          generateTypes: false,
        },
      }),
    ],
    chain(config) {
      config.module
        .rule('sass')
        .use('sassLoader')
        .tap(options => {
          return {
            ...options,
            additionalData: `@import '~@/assets/css/lib/mixin';`,
          }
        })
    },
    // cache: 'memory' as any,
  }
})
