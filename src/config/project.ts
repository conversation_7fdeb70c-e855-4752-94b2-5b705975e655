export const astroTags = [
  {label: '活动', value: 1},
  {label: '玩法', value: 2},
  {label: '功能', value: 3},
  {label: '后台', value: 4},
  {label: '协议', value: 5},
]

export const astroTagsMap: any = {}
astroTags.map(v => {
  astroTagsMap[v.value] = v.label
})

//
export const astroBusiness = [
  {label: '交友', value: 1},
  {label: '语音房', value: 2},
  {label: '手Y', value: 3},
  {label: '娱乐', value: 4},
  {label: '营收', value: 5},
  {label: '服务治理', value: 6},
]
export const renderOptions = [
  {label: '最新', value: 'latest'},
  {label: '稳定', value: 'stable'},
]

export const astroBusinessMap: any = {}
astroBusiness.map(v => {
  astroBusinessMap[v.value] = v.label
})

export type ProjectTagsType = 1 | 2 | 3 | 4 | 5 // 1-活动 2-玩法 3-功能 4-后台 5-协议
export type ProjectBusinessType = 1 | 2 // 1-交友 2-语音
export type ProjectStatusType = 0 | 1 | 2 //0-未开始 1-进行中 2-已结束

export type editorType = {
  avatar: string
  uid: number
  passport: string
}
export type RenderType = 'stable' | 'latest'
export type ProjectType = {
  id: number
  actId: number
  svcAppId: number // 活动service通道appid，跟actId，服务端自动生成，随测试/正式 环境切换而不同
  actName: string
  actDocumentUrl?: string // 需求文档链接
  uiUrl?: string // 设计稿链接
  reduceDays?: number // 提效减少人天
  workDays?: number // 排期人天
  beginTime: number
  endTime: number
  recommend: boolean // 是否被推荐
  status: ProjectStatusType // 0-未开始 1-进行中 2-已结束
  tags: ProjectTagsType[]
  business: ProjectBusinessType[]
  creator: string
  createUid: number
  createAvatar: string
  createTime: number
  editor: string
  editTime: number
  description: string // 项目描述
  versionId: number
  lockVersion: number | null
  previewUrl: string // 预览地址
  avatar: string // 项目封面
  editors: editorType[]
  renderType: RenderType // 渲染器版本
  authUsers?: AuthUser[] // 有项目权限的用户名单
  projectConfig?: ProjectConfig // 其它项目相关配置信息
  cdnStatus?: number // 是否需要预热cdn
  statTags?: [] // 数据统计使用，分类标签
  stat?: boolean // 是否纳入统计
  serverKey?: string
  isFavorite?: boolean // 是否被收藏
  bookmarks?: {
    // 书签列表
    label: string
    link: string
  }[]
}
export type AuthUser = {
  uid: number
  passport: string
  avatar?: string
}

export type ProjectConfig = {
  // 页面参数
  urlParams?: UrlParams // 页面特定参数
  projectUrlParams?: IUrlParams // 项目通用参数
  envOpen?: boolean // 是否开启环境变量配置
  envInfo?: IEnvInfo // 环境变量信息
}

export type UrlParams = {
  [key: string]: IUrlParams
}

export type IUrlParams = {
  [key: string]: ParmasData
}
export type IEnvInfo = {
  envKey?: string // 环境变量key
  envValueList?: EnvValue[] // 环境变量值列表
}
export type EnvValue = {
  value: string
  desc: string
}

export type ParmasData = {
  optionLabels: any // 参数值备注
  label?: string
  select?: string[]
  options?: string[]
}

// ProjectConfig示例
// {
//   projectConfig: {
//     pageParams: {
//       index: { // 页面路径
//         pageType: { // 参数key值
//           label: '页面类型', // 参数备注
//           select: ['type1', 'type2'], // 参数选中值
//           options: ['type1', 'type2', 'type3'], // 参数可选值
//         },
//         goto: {
//           label: '滚动定位',
//           select: ['award'],
//           options: ['award', 'rule', 'play'],
//         },
//       },
//       rule: {},
//     },
//   },
// }

// 统计面板项目标签预设选项
export const statTagsOptions = [
  {label: '大型活动', value: '大型活动', color: '#FFA500'}, // 中橙色
  {label: '常规活动', value: '常规活动', color: '#4682B4'}, // 钢蓝色
  {label: '玩法需求', value: '玩法需求', color: '#DB7093'}, // 中粉色
  {label: '零测试需求', value: '零测试需求', color: '#5F9EA0'}, // 灰绿色
  {label: '定制化需求', value: '定制化需求', color: '#A0522D'}, // 土褐色
  {label: '运营后台', value: '运营后台', color: '#DB70DB'}, // 淡紫色
  {label: '管理后台', value: '管理后台', color: '#9370DB'}, // 中紫色
]

export const statBackendTagsOptions = [
  {label: 'SQL', value: 'SQL', color: '#5F9EA0'}, // 灰绿色
  {label: 'HTTP', value: 'HTTP', color: '#9370DB'}, // 中紫色
  {label: 'Thrift', value: 'Thrift', color: '#DB7093'}, // 中粉色
  {label: 'LxApi', value: 'LxApi', color: '#A0522D'}, // 土褐色
]
