import {getParam} from 'src/utils/user'
import type {EnvType, ILocalDevEmpConfig} from './types'
class Config {
  env: EnvType = 'test'
  isDev = !!process.env.isDev
  isDevLocalFile = process.env.isDevLocalFile
  isTest = false
  isOpenSocket = false
  //手动开启 dev 模式
  useDevSocket = false
  useDevRender = false
  //
  projectTagOption = [
    {value: 1, label: '活动'},
    {value: 2, label: '玩法'},
    {value: 3, label: '功能'},
    {value: 4, label: '后台'},
    {value: 5, label: '协议'},
  ]
  localDevEmpConfig: ILocalDevEmpConfig = {
    empMap: {},
  }
  renderType: 'stable' | 'latest' = 'stable'
  constructor() {
    const host = window.location.host
    this.isTest = host.indexOf('-test') !== -1 || !!getParam('test')
    this.env = this.isTest ? 'test' : 'prod'
    // this.isOpenSocket = this.isDev || this.isTest
    this.isOpenSocket = true // 默认开启socket
    this.loadEmpMap()
    // console.log(`isDevLocalFile`, this.isDevLocalFile)
  }
  async loadEmpMap() {
    try {
      if (this.isDev && this.isDevLocalFile) {
        this.localDevEmpConfig.empMap = await fetch('/local.dev.json').then(res => res.json())
      }
    } catch (e) {
      this.localDevEmpConfig.empMap = {}
    }
  }
  get unpkg() {
    return 'https://unpkg.yy.com'
  }
  get f2c() {
    return 'https://f2c-api.yy.com'
  }
  get f2cFigma() {
    return `//f2c-figma-api.yy.com`
  }
  get upload() {
    if (this.env == 'prod') return `https://manager-hdzt.yy.com/hdzxfile/uploadFiles`
    return `https://test-manager-hdzt.yy.com/hdzxfile/uploadFiles`
  }
  get material() {
    if (this.env === 'prod') return '//materials-hdpt.yy.com'
    return '//test-materials-hdpt.yy.com'
  }
  get adminWeb() {
    if (this.env === 'prod') return '//zhuiya.yy.com/admin-web'
    return '//zhuiya-test.yy.com/admin-web'
  }
  get mlAdmin() {
    if (this.env === 'prod') return '//ml-admin.yy.com'
    return '//ml-admin-test.yy.com'
  }
  get yyembed() {
    return '//webdb.yyembed.yy.com'
  }
  get lx() {
    if (this.env === 'prod') return '//lx.yy.com'
    return '//lx-test.yy.com'
  }
  getMaterial(env: EnvType = this.env) {
    return env === 'prod' ? '//materials-hdpt.yy.com' : '//test-materials-hdpt.yy.com'
  }
  get wsInfo() {
    if (this.useDevSocket) return `//dev-test.yy.com:3302`
    return `//astro-ws.yy.com`
  }
  get socket() {
    if (this.useDevSocket) return `wss://dev-test.yy.com:3302/room`
    if (this.env === 'test') return `wss://astro-ws-test.yy.com/room`
    return `wss://astro-ws.yy.com/room`
  }
  getSocketByRoomId(roomId: number) {
    // return `${this.socket}/${roomId}?env=${this.env}`
    // return `${this.socket}/${roomId}/${this.env}`
    /**
     * ${this.socket}/${roomId}/${this.env} 切换回=> ${this.socket}/${roomId}
     * 可以用过 ?env=${this.env} 进行环境切换
     * astro-ws.yy.com 默认为 prod 环境、其他为 test 环境
     */
    return `${this.socket}/${roomId}`
  }
  get renderHost() {
    // console.log('config this.renderType', this.renderType)
    if (this.renderType === 'stable') {
      return this.env === 'prod' ? `https://hd-activity.yy.com` : `https://hd-activity-test.yy.com`
    }
    return this.env === 'prod' ? `https://hd-yo.yy.com` : `https://hd-yo-test.yy.com`
  }
  get renderOnline() {
    return `${this.renderHost}/astro`
  }
  get render() {
    if (this.env === 'test' && this.useDevRender) {
      return 'https://dev-test.yy.com:8000/astro/preview'
    }
    return `${this.renderHost}/astro/preview`
  }
  // get bosconfig() {
  //   return {
  //     endpoint: 'https://gz.bcebos.com',
  //     bucket: 'hd-static',
  //     ak: 'e8b0fd881fe04d789b3a32132d404d9a',
  //     sk: 'bfc95a66ba5946f1a9873a13b25364db',
  //     origin: 'https://hd-static.yystatic.com/astro/',
  //   }
  // }
  get bosUploadConfig() {
    if (this.env === 'prod') return '//bos-sentry-gate.yy.com'
    return '//bos-sentry-gate-test.yy.com'
  }
  /**
   * 根节点字号
   */
  get h5FontSizeInfo(): {[key: string]: string} {
    return {
      180: 'calc(100vw / 1.8)',
      223: 'calc(100vw / 2.23)',
      375: 'calc(100vw / 3.75)',
      750: 'calc(100vw / 7.5)',
    }
  }

  /**
   * PC根节点字号
   */
  get pcFontSizeInfo(): {[key: string]: string} {
    return {
      50: '50px',
      60: '60px',
      100: '100px',
    }
  }

  /**
   * 内嵌页根节点字号
   */
  get innerFontSizeInfo(): {[key: string]: string} {
    return {
      50: '50px',
      60: '60px',
      100: '100px',
    }
  }

  // 为了保持向后兼容，保留原有的fontSizeInfo
  get fontSizeInfo(): {[key: string]: any} {
    const result: {[key: string]: any} = {}
    Object.keys(this.h5FontSizeInfo).forEach(key => {
      result[key] = {
        h5: this.h5FontSizeInfo[key],
        pc: this.pcFontSizeInfo[key],
        inner: this.innerFontSizeInfo[key],
      }
    })
    return result
  }
}
export default new Config()
