@charset "UTF-8";

$gold: #FEE77D;
$desc: #dcdcdc;
$strong: #ff7b97;

$title: #3f281b;
$baseColor: #ffe7b4;
$title-hover: #98A4FF;
$name: #ffffff;
$value: #FFE897;
$value-rich: #6EDAFF;
$value-strong: #fcd87e;
$value-strong-fans: #c2934e;
$warn: #9e302a;

$placeWidth: 26;
$cp-avatar-bg: 304;
$cp-avatar-width: 70;
$avatar-width: 176;
$rank-item-height: 88;

// Clearfix
// --------
// For clearing floats like a boss h5bp.com/q
@mixin clearfix() {
  *zoom: 1;

  &:before,
  &:after {
    display: table;
    content: '';
  }

  &:after {
    clear: both;
  }
}

@mixin ir() {
  background-color: transparent;
  background-repeat: no-repeat;
  border: 0;
  direction: ltr;
  display: block;
  overflow: hidden;
  text-align: left;
  text-indent: -999em;
  outline: none;

  br {
    display: none;
  }
}

// text overflow
// -------------------------
// requires inline-block or block for proper styling
@mixin text-overflow() {
  word-wrap: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin bg($url){
  background: url($url);
  background-size: 100%;
  background-repeat: no-repeat;
}

@mixin flexCenter(){
  display: flex;
  justify-content: center;
  align-items: center;
}
// @mixin text-overflow($row:1) {
//     width:auto;
//     display: -webkit-box;
//     overflow: hidden;
//     text-overflow: ellipsis;
//     -webkit-line-clamp: $row;
//     -webkit-box-orient: vertical;
// }

@mixin scroll-bar(
  $width: 7px,
  $color: #000,
  $thumbOpacity: 0.5,
  $thumbHoverOpacity: 0.8
) {
  overflow: auto;

  &::-webkit-scrollbar-track {
    border-radius: $width;
    background-color: transparent;
  }

  &::-webkit-scrollbar {
    width: $width;
    height: $width;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: $width;
    background-color: rgba($color, $thumbOpacity);

    &:hover {
      background-color: rgba($color, $thumbHoverOpacity);
    }
  }
}

@mixin triangle($direction, $size, $borderColor) {
  content: '';
  height: 0;
  width: 0;

  @if $direction==top {
    border-bottom: $size solid $borderColor;
    border-left: $size dashed transparent;
    border-right: $size dashed transparent;
  } @else if $direction==right {
    border-left: $size solid $borderColor;
    border-top: $size dashed transparent;
    border-bottom: $size dashed transparent;
  } @else if $direction==bottom {
    border-top: $size solid $borderColor;
    border-left: $size dashed transparent;
    border-right: $size dashed transparent;
  } @else if $direction==left {
    border-right: $size solid $borderColor;
    border-top: $size dashed transparent;
    border-bottom: $size dashed transparent;
  }
}

@mixin table {
  width: 100%;
  margin: 10px 0;

  tr th,
  tr td {
    height: 26px;
    line-height: 26px;
    text-align: center;
    font-size: 12px;
    color: #989088;
    border: 1px solid #624d3a;
    font-weight: 400;
  }

  td:first-child {
    color: #febd5f;
  }
  tr th {
    color: #989088;
    background-color: #322a24;
  }

  tr td {
    background-color: transparent;
  }
}

@mixin radius-table(
  $bordercolor,
  $borderwidth,
  $borderradius,
  $thbgcolor,
  $tdbgcolor
) {
  border-radius: $borderradius;
  table-layout: fixed;
  width: 100%;
  border-collapse: separate;
  // border-spacing: 1px;
  border-spacing: 0px;
  background: $bordercolor;
  // border-right: $borderwidth/2 solid $bordercolor;
  thead {
    tr:first-child {
      .first {
        border-top-left-radius: $borderradius;
      }
      .last {
        border-top-right-radius: $borderradius;
      }
    }
    // th:first-child {
    //     border-top-left-radius: $borderradius;
    // }
    // th:last-child {
    //     border-top-right-radius: $borderradius;
    // }
  }
  tbody {
    tr:last-child {
      .first {
        border-bottom-left-radius: $borderradius;
      }
      .last {
        border-bottom-right-radius: $borderradius;
      }
    }
  }
  &.all {
    tr:first-child {
      .first {
        border-top-left-radius: $borderradius;
      }
      th:last-child {
        border-top-right-radius: $borderradius;
      }
      td:last-child {
        border-top-right-radius: $borderradius;
      }
    }
    tr:last-child {
      .first {
        border-bottom-left-radius: $borderradius;
      }
      td:last-child {
        border-bottom-right-radius: $borderradius;
      }
    }
  }

  th {
    background: $thbgcolor;
  }
  td {
    background: $tdbgcolor;
  }
}

@mixin setFont($font) {
  font-family: $font, "PingFang SC", -apple-system, BlinkMacSystemFont,
  "Helvetica Neue", STHeiti, "Microsoft Yahei", Tahoma, Simsun, sans-serif;
}

// 渐变相关
@function is-direction($value) {
  $is-keyword: index((to top, to top right, to right top, to right, to bottom right, to right bottom, to bottom, to bottom left, to left bottom, to left, to left top, to top left), $value);
  $is-angle: type-of($value) == 'number' and index('deg' 'grad' 'turn' 'rad', unit($value));

  @return $is-keyword or $is-angle;
}

@function legacy-direction($value) {
  @if is-direction($value) == false {
    @warn "Cannot convert `#{$value}` to legacy syntax because it doesn't seem to be an angle or a direction";
  }

  $conversion-map: (
      to top          : bottom,
      to top right    : bottom left,
      to right top    : left bottom,
      to right        : left,
      to bottom right : top left,
      to right bottom : left top,
      to bottom       : top,
      to bottom left  : top right,
      to left bottom  : right top,
      to left         : right,
      to left top     : right bottom,
      to top left     : bottom right
  );

  @if map-has-key($conversion-map, $value) {
    @return map-get($conversion-map, $value);
  }

  @return 90deg - convert-angle($value, 'deg');
}

// 渐变
@mixin linear-gradient($direction, $color-stops...) {
  // Direction has been omitted and happens to be a color-stop
  @if is-direction($direction) == false {
    $color-stops: $color-stops;
    $direction: 180deg;
  }

  background: nth(nth($color-stops, 1), 1);
  background: -webkit-linear-gradient(legacy-direction($direction), $color-stops);
  background: linear-gradient($direction, $color-stops);
}

// 渐变文字
@mixin linearGradientFont($fontSize: 24px, $direction, $color-stops...) {
  font-size: $fontSize;
  font-weight: normal;
  font-stretch: normal;
  line-height: 1.5;
  // background-image: linear-gradient($deg, $colors);
  @include linear-gradient($direction, $color-stops);
  background-size: 100%;
  background-repeat: repeat;
  -webkit-background-clip: text;
  /*必需加前缀 -webkit- 才支持这个text值 */
  -ms-background-clip: text;
  /*必需加前缀 -webkit- 才支持这个text值 */
  -moz-background-clip: text;
  /*必需加前缀 -webkit- 才支持这个text值 */
  -o-background-clip: text;
  /*必需加前缀 -webkit- 才支持这个text值 */
  -webkit-text-fill-color: transparent;
  /*text-fill-color会覆盖color所定义的字体颜色： */
  -ms-text-fill-color: transparent;
  /*text-fill-color会覆盖color所定义的字体颜色： */
  -moz-text-fill-color: transparent;
  /*text-fill-color会覆盖color所定义的字体颜色： */
  -o-text-fill-color: transparent;
  /*text-fill-color会覆盖color所定义的字体颜色： */
  font-weight: bolder;
}

@mixin imgClear {
  image-rendering:-moz-crisp-edges;
  image-rendering:-o-crisp-edges;
  image-rendering:-webkit-optimize-contrast;
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor;
  -webkit-font-smooting:  antialiased;
}
