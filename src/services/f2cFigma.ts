import {f2cFigmaHost} from 'src/components/common/FrontEndAi/config'
import {httpF2cFigma} from './http'

export const getCode = async (query: any) => {
  // return httpF2cFigma.get(`/api/nodes`, query)
  const params = new URLSearchParams(query)
  return window.fetch(`https://${f2cFigmaHost}/api/nodes?${params}`, {
    method: 'GET',
    headers: {
      'F2c-Api-Platform': 'astro',
    },
  })
}

export const getPreviewImage = async (query: any) => {
  return httpF2cFigma.get(`/api/images`, query)
  // const params = new URLSearchParams(query)
  // return window.fetch(`https://${f2cFigmaHost}/api/images?${params}`)
}
export const getCodeSign = async (query: any) => {
  // return httpF2cFigma.get(`/api/nodes`, query)
  const params = new URLSearchParams(query)
  return window.fetch(`https://${f2cFigmaHost}/api/nodes?${params}&plugin_data=shared`)
}
export const getSearchParams = (figmaLink: string, acessToken: string, type?: 'html' | 'png' | undefined) => {
  // const currentNodeId = getUrlParamByUrl('node-id', figmaUrl)
  // const fileKey = String(figmaUrl.match(/design\/([^\/]+)\//)?.[1])

  const params: any = {
    // fileKey: fileKey,
    // nodeIds: currentNodeId,
    figma_link: figmaLink,
    access_token: acessToken,
  }
  if (type) {
    params.format = type
  }

  if (type === 'html') {
    params.option = JSON.stringify({
      useInlineStyle: true,
      cssUnit: 'rem',
      rootValue: 100,
    })
  }

  return params
}