import {httpMaterial, httpMlApiBaseUrl, httpYyembed} from './http'
import type {PosterUser, YYUserRes} from './types'
// import {request} from './common'
export const getUserRole = async (query?: any) => {
  return httpMaterial.get(`/material/getUserRole`, query)
}
export const dologinoutMaterial = async (query?: any) => {
  return httpMaterial.get(`/async/logout`, query)
}

export async function getUserInfo(query?: any) {
  const res = await httpMaterial.get(`/material/getUserRole`, query)
  if (res && res.data?.result == 200) {
    return res.data.data
  }
  return {}
}
export async function logout(query?: any) {
  await httpMaterial.get(`/async/logout`, query)
}

// 查找用户信息
export const posterUser = (key: string): Promise<PosterUser[]> => {
  return httpYyembed.get('/webdb/query_userinfo', {type: 1, value: key})
}
// 查询用户详细信息
export const getYYUser = (dwName: string): Promise<YYUserRes> => {
  return httpMlApiBaseUrl.get('/admin/yyuser/queryUserinfo', {dwName})
}
