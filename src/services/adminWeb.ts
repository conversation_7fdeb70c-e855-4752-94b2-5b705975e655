import {httpAdminWeb} from './http'

const accessKey = 'astro'
//申请查看角色列表（集团安全）
// https://tagee.yy.com/#/project/706/test/interface/3c145e32b85676bd52535e940ce4846a
export const getApplyViewAuth = async () => {
  return httpAdminWeb.get(`/permission/ambiguous/applyViewAuth`, {accessKey})
}

// 获取权限列表
// https://tagee.yy.com/#/project/706/test/interface/51f41057940fb081a79165e059dac616
export const getPermissions = async (uid: number) => {
  return httpAdminWeb.get(`/permission/internal/getPermissions`, {accessKey, uid})
}

// 提交权限申请
// https://tagee.yy.com/#/project/706/test/interface/47fc28d6b67649e8d5b87efbfe71d8c8
export const submitPermissions = async (params: any) => {
  return httpAdminWeb.post(`/permission/internal/submit`, {accessKey, ...params})
}
