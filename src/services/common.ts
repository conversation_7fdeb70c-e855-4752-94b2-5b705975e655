import config from 'src/config'
import {httpBos, httpF2c} from './http'
// 上传图片接口
export const uploadFiles = () => {
  return config.upload
}
// 获取f2c配置信息接口
export const getF2CConfig = async (configId: number) => {
  return httpF2c.get(`/manage/config/configList`, {
    configId,
    businessId: 10000,
  })
}
// 按configId查询
export const configVersionList = async (configId: number) => {
  return httpF2c.get(`/consume/config/configVersionList`, {configId})
}
// 按groupId查询
export const listGroupLatestVersion = async (groupId: string, withValue = true) => {
  return httpF2c.get(`/consume/config/listGroupLatestVersion`, {groupId, withValue})
}

// 获取上传token
export const getUploadToken = async (fileName: string) => {
  const params = {
    busiId: 'astro', // required 业务标识，找服务负责人分配
    scene: 1101, // required 场景标识，用于做数据统计 找服务负责人分配
    fileName: fileName, // required 上传文件名[上传单个文件]，一般就是客户端本地文件名，注意带上后缀之类的，服务端会生成 bos 的objectKey    source: 'H5', // optional 上传来源，就是一个标识, 建议填写上，比如APP，Server 等
    referer: location.href, // optional 上传来源referer，只是用来做数据统计，建议带上
    refererDesc: 'Astro平台', // optional 上传来源referer说明信息，只是用来做数据统计，建议带上
  }
  return httpBos.post(`/bosSentryGate/getUploadToken`, params)
}
