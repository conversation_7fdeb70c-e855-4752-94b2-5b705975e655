import config from 'src/config'
import {httpMaterial} from './http'
// import {request} from './common'
// Astro 平台新接口

// 获取项目配置
// https://tagee.yy.com/#/project/939/feat-ultimate/interface/5f7956351be8895f9194f406ee6fae82
export const getProjectConfig = async (query: {projectId: number; sourceEnv?: 'test' | 'prod'; versionId?: number}) => {
  // 用于拷贝配置时查询测试/正式环境的项目信息
  if (query.sourceEnv) {
    return httpMaterial.get(`${config.getMaterial(query.sourceEnv)}/actProject/projectConfig`, query)
  }
  return httpMaterial.get(`/actProject/projectConfig`, query)
}

// 获取页面配置，加载器调用
// https://tagee.yy.com/#/project/939/feat-ultimate/interface/b70f107c3d808d676076be59cc0fba6e
export const getPageConfig = async (query: {
  projectId: number
  versionId?: number
  page: string
  env?: string
}) => {
  return httpMaterial.get(
    `/actProject/pageConfig/${query.projectId}/${query.env}/${query.page}/${query.versionId}`,
    query,
  )
}

// 获取组件服务器端配置
// https://tagee.yy.com/#/project/939/feat-ultimate/interface/f682e53c99c6770e65db0a7c6148d060
export const getDataConfig = async (query: {
  projectId: number // 项目id
  page: string // 页面
  materialId: number // 物料库id
  uniqueNum: string // 组件唯一id
  url: string
  path: string
}) => {
  return httpMaterial.get(`/actProject/dataConfig`, query)
}

// 保存组件后端配置
// https://tagee.yy.com/#/project/939/feat-ultimate/interface/92d5247ac0975fec4aa717d32e1a2c2b
export const saveDataConfig = async (query: {
  projectId: number // 项目id
  page: string // 页面
  materialId: number // 物料库id
  uniqueNum: string // 组件唯一id
  config: Record<string, any>
  url: string
  path: string
}) => {
  return httpMaterial.post(`/actProject/saveDataConfig`, query)
}

// 保存项目配置
export const saveProjectConfig = async (query: {
  projectId: number
  config: Record<string, any>
}) => {
  return httpMaterial.post(`/actProject/saveProjectConfig`, query)
}

// 发布项目
export const publish = async (query: {
  projectId: number
  env: string
  publishRemark?: string
}) => {
  return httpMaterial.post(`/actProject/publish`, query)
}
/**
 * 保存组件后端配置
 * @param projectId 项目id
 * @param page 页面key
 * @param materialId 组件id
 * @param uniqueNum 组件序号
 * @param config 数据配置
 */
export function saveConfig(projectId: any, page: any, materialId: any, uniqueNum: any, config: any) {
  return httpMaterial.post(`/actProject/saveDataConfig`, {
    projectId,
    page,
    materialId,
    uniqueNum,
    config,
  })
}

// 获取项目版本列表
// https://tagee.yy.com/#/project/939/master/interface/a1e88fd5aebfcb493012b646f5c34ac5
export const getVersionList = async (query: {sourceEnv?: 'test' | 'prod'; projectId: number}) => {
  // 用于拷贝配置时查询测试/正式环境的项目版本列表
  if (query.sourceEnv) {
    return httpMaterial.get(`${config.getMaterial(query.sourceEnv)}/actProject/versionList`, query)
  }
  return httpMaterial.get(`/actProject/versionList`, query)
}

// 锁定项目默认版本
// https://tagee.yy.com/#/project/939/master/interface/1142cd3283f4c582e49a2d80847ce25b
export const lockVersion = async (query: {
  projectId: number
  versionId: number
}) => {
  return httpMaterial.post(`/actProject/lockVersion`, query)
}

// 获取版本锁定记录
// https://tagee.yy.com/#/project/939/master/interface/52d1406b994751d420e9a2c0b090811f
export const versionLockLogs = async (query: {
  projectId: number
}) => {
  return httpMaterial.get(`/actProject/versionLockLogs`, query)
}

/**
 * TODO server type 数据保存接口
 * https://tagee.yy.com/#/project/939/feat-ultimate/interface/92d5247ac0975fec4aa717d32e1a2c2b
 */

/**
projectId	
请求参数
Long		
项目id

page	
请求参数
String		
页面key

materialId	
请求参数
Long		
组件id

uniqueNum	
请求参数
String		
组件序号
 */
