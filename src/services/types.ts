export interface PosterUser {
  concat: string
  first_name: string
  is_active: boolean
  username: string
  nick: string
  passport: string
}
export interface YYUser {
  imid: string
  passport: string
  uid: string
  nick: string
  jifen: string
  sign: string
  intro: string
  nickExt: string
}

// result 0 未找到，1 找到用户信息
export interface YYUserRes {
  data: {
    result: 1 | 0
    info: YYUser | string
  }
}
