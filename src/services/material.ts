import config from 'src/config'
import type {ProjectBusinessType, ProjectTagsType} from 'src/config/project'
import {transformComponent} from 'src/utils'
import {httpLx, httpMaterial} from './http'
// 项目列表
export interface IListParams {
  search?: string // 搜索: 活动ID, 活动名, 创建人等
  scope?: number // 范围: 0-全部 1-我的
  recommend?: boolean
  page?: number // 页码
}
export const getProjectList = async (query?: IListParams) => {
  return httpMaterial.get(`/actProject/list`, query)
}
// 推荐列表，游客可以访问
export const getProjectPublicList = async () => {
  return httpMaterial.get(`/actProject/publicList`)
}

// 新增/编辑项目
export interface ISaveProjectParams {
  id?: number // 新增时为空, 编辑时回传
  actId?: number // 活动id
  actName?: string
  beginTime?: number
  endTime?: number
  tags?: ProjectTagsType[]
  business?: ProjectBusinessType[]
  serverKey?: string
}
export const saveProject = async (query: ISaveProjectParams) => {
  return httpMaterial.post(`/actProject/save`, query)
}

// 新增/编辑项目
export const copyProject = async (id: number) => {
  return httpMaterial.post(`/actProject/copy`, {id})
}

// 推荐项目
export const recommendProject = async (id: number) => {
  return httpMaterial.post(`/actProject/recommend`, {
    id,
  })
}

// 隐藏项目
export const hideProject = async (id: number) => {
  return httpMaterial.post(`/actProject/hideProject`, {id})
}

//收藏项目
export const collectProject = async (projectId: number) => {
  return httpMaterial.post(`/actProject/favorite`, {projectId})
}
//取消收藏项目
export const uncollectProject = async (projectId: number) => {
  return httpMaterial.post(`/actProject/unfavorite`, {projectId})
}

export interface QueryMaterialParams {
  componentType?: number //组件类型, 1为常规组件 2为Layout组件
  keyword?: string // 搜索关键字，搜索物料名称字段
  tagTypeId?: string // 业务id，0或空返回全部类别
  tabId?: number // 类型id，0或空返回全部类别
  tagId?: number // 标签id，0或空表示不搜索标签
  tagIds?: string // 标签id数组，空表示不搜索标签
  pageNo?: number
  pageSize?: number
  type?: number // 物料类型，不传:查询全部 0:普通物料 1:套餐 2:容器框架布局 3:列表
  extQuery?: string // 额外查询字段,以key=value的形式上传,多个字段逗号分隔,如：sortKey=utime,sort=desc
  project?: string //查询包名，判断是更新还是新增
  projectTag?: string
}
export interface UpsertMaterialParams {
  type: number
  extJson: string
  materialUrl?: string
  materialName: string
  project: string
  tagList: string[]
  materialId?: number // 可选字段
  stateUrl?: string
}
// 获取标签列表 生产测试都使用生产环境
export const getListTag = () => {
  return httpMaterial.get(`//materials-hdpt.yy.com/material/listTag`)
}

//搜索物料 生产测试都使用生产环境
export const queryMaterial = (query: QueryMaterialParams) => {
  return httpMaterial.get(`//materials-hdpt.yy.com/material/queryMaterial`, query)
  return httpMaterial.get(`//test-materials-hdpt.yy.com/material/queryMaterial`, query)
}
// 新增/修改物料
export const upsertMaterial = (query: UpsertMaterialParams) => {
  return httpMaterial.post(`/material/upsertMaterial`, query)
}
//批量新增、修改物料
export const batchUpsertMaterial = (query: UpsertMaterialParams[]) => {
  return httpMaterial.post(`/material/batchUpsertMaterial`, query)
}
//获取物料详情
export const getMaterialDetail = (materialId: number) => {
  return httpMaterial.get(`/material/getMaterialDetail`, {materialId})
}
//删除
export const deleteMaterial = (query: object) => {
  return httpMaterial.post(`/material/deleteMaterial`, query)
}
//更新业务类型
export const updateTagType = (query: object) => {
  return httpMaterial.post(`/material/updateTagType`, query)
}

// 物料模板 ext 数据转换成json
const mComponentRes = (res: any) => {
  if (res && res.result === 200) {
    let list = res.data?.list || []
    list = list.map((item: any) => {
      return transformComponent(item)
    })
    return list
  } else {
    return []
  }
}
const mTagsRes = (res: any) => {
  if (res && res.result === 200) {
    const list = res.data || []
    return list
  } else {
    return []
  }
}
/**
 * 获取模版清单
 * @returns []
 */
export const getMLayout = async () => {
  const {data: res}: any = await queryMaterial({
    type: 4,
    tagIds: config.isTest ? '816' : '830',
  })
  return mComponentRes(res)
}
/**
 * 获取组件清单
 * @returns []
 */
// export const getMComponent = async (keyword: string) => {
//   const {data: res}: any = await queryMaterial({
//     type: 4,
//     tagIds: config.isTest ? '818' : '826',
//     keyword,
//   })
//   return mComponentRes(res)
// }
export interface searchKey {
  componentType?: number
  tagTypeId?: string
  tagIds?: string
  keyword?: string
  pageSize?: number
  pageNo?: number
  project?: string
  projectTag?: string
}
//搜索组件
export const searchMComponent = async (searchKey: searchKey, pageSize: number = 100) => {
  const {data: res}: any = await queryMaterial({
    ...searchKey,
    extQuery: 'sortKey=utime,sort=desc',
    type: 4,
    pageSize,
  })
  const total = res?.data?.total || 0
  return {list: mComponentRes(res), total}
}

/**
 * 获取tags清单
 * @returns []
 */
export const getMTags = async () => {
  const {data: res}: any = await getListTag()
  return mTagsRes(res)
}

export const getPermission = async (projectId: number | string) => {
  return httpMaterial.get('/actProject/hasPermission', {
    projectId,
  })
}
//* 物料统计
const stats = async (query?: any) => {
  return httpMaterial.get(`/stat/stats`, query)
}

// https://tagee.yy.com/#/project/939/master/interface/3b22769e16288acac51c5721ae82dd98
// 项目统计信息查询接口
export const queryProjectStat = async (query: {year: string; quarter: string}) => {
  return httpMaterial.get(`/api/project/stat/query`, query)
}

// 后台统计查询
export const queryBackendStat = async (query: {year: string; quarter: string}) => {
  return httpLx.get(`/api/stat/summary`, query)
}

export interface IAppConfigPush {
  projectId: number // 项目id
  pageKey: string // 页面key
  push?: boolean // 是否推送
  all?: boolean // 是否全量
  urls?: string[] // 附带前端公共资源URL列表
}

// https://tagee.yy.com/#/project/939/test/interface/cdf9de0391a6d8371a2f05676a527146
// 离线包配置
export const appConfigPush = async (query: IAppConfigPush) => {
  return httpMaterial.post(`/appConfig/push`, query)
}
