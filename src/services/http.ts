import {Http} from '@astro/utils'
import config from 'src/config'

export const httpF2c = new Http({
  base: config.f2c,
  credentials: 'include',
})
httpF2c.fetchResponse = data => {
  return {data}
}

export const httpMaterial = new Http({
  base: config.material,
  credentials: 'include',
})
httpMaterial.fetchResponse = data => {
  return {data}
}

export const httpUnpkg = new Http({
  base: config.unpkg,
  credentials: 'omit',
})

httpUnpkg.fetchResponse = data => {
  return {data}
}

export const httpAdminWeb = new Http({
  base: config.adminWeb,
  credentials: 'include',
})
httpAdminWeb.fetchResponse = data => {
  return {data}
}

export const httpMlApiBaseUrl = new Http({
  base: config.mlAdmin,
  credentials: 'include',
})
httpMlApiBaseUrl.fetchResponse = data => {
  return {data}
}

export const httpYyembed = new Http({
  base: config.yyembed,
  // credentials: 'include', // 会报跨域
  credentials: 'omit',
})
httpYyembed.fetchResponse = data => {
  return {data}
}

export const httpWsInfo = new Http({
  base: config.wsInfo,
  credentials: 'include',
})
httpWsInfo.fetchResponse = data => {
  return {data}
}

export const httpBos = new Http({
  base: config.bosUploadConfig,
  credentials: 'include',
})
httpWsInfo.fetchResponse = data => {
  return {data}
}

export const httpF2cFigma = new Http({
  base: config.f2cFigma,
  // credentials: 'include', // 会报跨域
  credentials: 'omit',
})
httpF2cFigma.fetchResponse = data => {
  return {data}
}

export const httpLx = new Http({
  base: config.lx,
  credentials: 'include',
})
httpLx.fetchResponse = data => {
  return {data}
}
