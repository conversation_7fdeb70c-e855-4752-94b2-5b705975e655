// import config from 'src/config'
import {httpUnpkg} from './http'
// import {request} from './common'
// https://unpkg.yy.com/version/@astro-ui/barrage
// 通过包名或者包的版本列表
export const getDeployVersionByName = async (pkgName: string) => {
  return httpUnpkg.get(`/version/${pkgName}`)
}

export const getEMPJsonByNameAndVersion = async (pkgName: string, version: string) => {
  return httpUnpkg.get(`/${pkgName}@${version}/dist/emp.json`)
}
