import {
  Outlet,
  RouterProvider,
  // createHashRouter as createRouter
  createBrowserRouter as createRouter,
} from 'react-router-dom'
import Layout from 'src/components/common/layout/AstroLayout'

const router = createRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        async loader({request}) {
          const url = new URL(request.url)
          const channel = url.searchParams.get('channel') || ''
          return {channel}
        },
        async lazy() {
          const {default: Component} = await import('src/components/dashboard/AppList/CardList')
          // const {default: Component} = await import('src/components/dashboard/AppList')
          // const {default: Component} = await import('src/components/dashboard/SearchList')
          return {Component}
        },
      },
      {
        path: 'projects/recomend',
        async lazy() {
          const {default: Component} = await import('src/components/Recomend/Waterfall')
          return {Component}
        },
      },
      {
        path: 'projects/playload/:id',
        async lazy() {
          const {default: Component} = await import('src/components/playload')
          return {Component}
        },
      },
      {
        path: 'material',
        async lazy() {
          const {default: Component} = await import('src/components/Material')
          return {Component}
        },
      },
      {
        path: 'admin-table',
        async lazy() {
          const {default: Component} = await import('src/components/AdminTable')
          return {Component}
        },
      },
      {
        path: 'statistics',
        async lazy() {
          const {default: Component} = await import('src/components/Statistics')
          return {Component}
        },
      },
    ],
  },
])
export const RouterApp = () => <RouterProvider router={router} />

export const RouterContent = () => <Outlet />
