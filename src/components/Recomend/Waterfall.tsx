import {CopyOutlined} from '@astro/ui/icon'
import {Popconfirm, Tooltip} from '@astro/ui/ui'
import {useEffect} from 'react'
import Masonry, {ResponsiveMasonry} from 'react-responsive-masonry'
import {useNavigate} from 'react-router-dom'
import userStore from 'src/components/common/user/userStore'
import appStore from 'src/components/dashboard/AppStore'
import projectStore from 'src/components/dashboard/AppStore'
import type {ProjectType} from 'src/config/project'
import css from './waterfall.module.scss'
const zipImg = (url?: string, size = 500) => {
  if (!url) return
  const sp = url.indexOf('?') > -1 ? ':' : '?'
  return `${url}${sp}x-bce-process=image/resize,m_lfit,w_${size}/format,f_webp/quality,q_80`
}
const WaterfallItem = ({item, access}: {item: ProjectType; access: boolean}) => {
  return (
    <div className={css.waterfallItem}>
      {access && (
        <span className={css.copy}>
          <CpoyBtn data={item} />
        </span>
      )}
      <a href={item.previewUrl} target="_blank" rel="noreferrer">
        <img
          className={css.cover}
          key={`item-${item.id}`}
          src={zipImg(item.avatar) || require('src/assets/img/default.png')}
        />
      </a>
      <div className={css.info}>
        <img className={css.avatar} src={zipImg(item.createAvatar, 50) || '//s1.yy.com/guild/header/10001.jpg'} />
        <div className={css.info__title}>
          {item.actName}
          <p className={css.info__title__p}>{item.creator}</p>
        </div>
      </div>
    </div>
  )
}
export const Waterfall = () => {
  const userState = userStore.state
  const projectState = appStore.state
  useEffect(() => {
    projectState.queryProjectList({recommend: true})
  }, [])
  return (
    <>
      <ResponsiveMasonry columnsCountBreakPoints={{350: 1, 750: 2, 800: 3, 1000: 4, 1200: 5, 1400: 6}}>
        <Masonry style={{gap: `15px`}}>
          {projectState.list
            .slice()
            .reverse()
            .map((item, i) => (
              <WaterfallItem access={userState.canAccess} item={item} key={`waterfallItem-${item.id}`} />
            ))}
        </Masonry>
      </ResponsiveMasonry>
    </>
  )
}

export default Waterfall

function CpoyBtn({data}: {data: ProjectType}) {
  const navigate = useNavigate()
  const store = projectStore.state

  return (
    <Popconfirm
      title="复制项目"
      description={() => {
        return (
          <>
            <p>点击确定将直接创建复制项目。</p>
          </>
        )
      }}
      onConfirm={async (event: any) => {
        event.stopPropagation()
        const res = await store.doCopyProject(data.id)
        if (res) {
          navigate(`/projects/playload/${res}`)
        }
      }}
      onCancel={(event: any) => {
        event.stopPropagation()
      }}
      okText="确定"
      cancelText="取消"
    >
      <Tooltip title={'复制当前项目'}>
        <CopyOutlined
          onClick={event => {
            event.stopPropagation()
          }}
        />
      </Tooltip>
    </Popconfirm>
  )
}
