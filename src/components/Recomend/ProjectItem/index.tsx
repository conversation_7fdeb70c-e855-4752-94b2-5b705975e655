import {CopyOutlined, <PERSON>TwoTone, ZoomInOutlined} from '@astro/ui/icon'
import {Avatar, Badge, Card, Col, Flex, Popconfirm, Progress, Tooltip} from '@astro/ui/ui'
import dayjs from 'dayjs'
import {useNavigate} from 'react-router-dom'
import userStore from 'src/components/common/user/userStore'
// import type {ProjectBusinessType, ProjectType} from 'src/components/dashboard/projectStore'
import projectStore from 'src/components/dashboard/AppStore'
import {astroBusinessMap, type ProjectBusinessType, type ProjectType} from 'src/config/project'
import styles from './index.module.scss'

export const getBizName = (business: any[]) => {
  let cb: any = ''
  if (business && business.length > 0) {
    cb = business.map((item: number) => astroBusinessMap[item as ProjectBusinessType])
    cb = cb.join('、')
  }
  return cb
}

function PreviewBtn({data}: {data: ProjectType}) {
  return (
    <Tooltip title={'预览'}>
      <ZoomInOutlined
        onClick={(event: {stopPropagation: () => void}) => {
          event.stopPropagation()
          window.open(data.previewUrl, '_blank')
        }}
      />
    </Tooltip>
  )
}
function CpoyBtn({data}: {data: ProjectType}) {
  const navigate = useNavigate()
  const store = projectStore.state

  return (
    <Popconfirm
      title="复制项目"
      description={() => {
        return (
          <>
            <p>点击确定将直接创建复制项目。</p>
          </>
        )
      }}
      onConfirm={async (event: any) => {
        event.stopPropagation()
        const res = await store.doCopyProject(data.id)
        if (res) {
          navigate(`/projects/playload/${res}`)
        }
      }}
      onCancel={(event: any) => {
        event.stopPropagation()
      }}
      okText="确定"
      cancelText="取消"
    >
      <Tooltip title={'复制'}>
        <CopyOutlined
          onClick={event => {
            event.stopPropagation()
          }}
        />
      </Tooltip>
    </Popconfirm>
  )
}

function ProjectItem({data, recommend}: {data: ProjectType; recommend: boolean}) {
  const navigate = useNavigate()
  const ustore = userStore.state
  const store = projectStore.state

  const curTime = Date.now()
  const percent =
    curTime < data.beginTime
      ? 0
      : curTime > data.endTime
        ? 100
        : Math.floor(((curTime - data.beginTime) / (data.endTime - data.beginTime)) * 100)

  return (
    <Col
      className="gutter-row"
      xs={{span: 24}}
      sm={{span: 12}}
      md={{span: 8}}
      lg={{span: 8}}
      xl={{span: 6}}
      xxl={{span: 4}}
    >
      <Card
        hoverable
        cover={
          !recommend && data.business && data.business.length > 0 ? (
            <Badge.Ribbon text={<span style={{fontSize: '12px'}}>{getBizName(data.business)}</span>}>
              <div className={styles.cardImg}>
                <img alt={data.actName} src={data.avatar || require('src/assets/img/default.png')} />
              </div>
            </Badge.Ribbon>
          ) : (
            <div className={styles.cardImg}>
              {/* <img alt={data.actName} src={data.avatar || 'https://hd-static.yystatic.com/9486980927669479.png'} /> */}
              <img alt={data.actName} src={data.avatar || require('src/assets/img/default.png')} />
            </div>
          )
        }
        actions={
          ustore.user.role === 'A' || ustore.user.role === 'T' || ustore.user.role === 'P'
            ? recommend
              ? [<PreviewBtn key="ZoomInOutlined" data={data} />, <CpoyBtn key="CopyOutlined" data={data} />]
              : [
                  <PreviewBtn key="ZoomInOutlined" data={data} />,
                  <CpoyBtn key="CopyOutlined" data={data} />,
                  <Tooltip key="HeartTwoTone" title={'推荐'}>
                    <HeartTwoTone
                      twoToneColor={data.recommend ? '#eb2f96' : 'grey'}
                      onClick={(event: any) => {
                        event.stopPropagation()
                        store.doRecommend(data.id)
                      }}
                    />
                  </Tooltip>,
                ]
            : [<PreviewBtn key="ZoomInOutlined" data={data} />]
        }
        onClick={() => {
          if (ustore.user.role === 'A' || ustore.user.role === 'T' || ustore.user.role === 'P') {
            navigate(`/projects/playload/${data.id}`)
          }
        }}
      >
        <Card.Meta
          title={
            <Flex justify="space-between">
              <Flex align="center">
                {data.actName}
                {/* <div className={styles.tags}>
                  {!data.recommend && data.business && data.business.length > 0
                    ? data.business.map((item: number) => (
                      <Tag key={item} color={businessColor[item as projectBusinessType]}>
                        {businessMap[item as projectBusinessType]}
                      </Tag>
                    ))
                    : null}
                </div> */}
              </Flex>

              <Tooltip title={`创建人：${data.creator}`}>
                <Avatar size="small" src={data.createAvatar || '//s1.yy.com/guild/header/10001.jpg'} />
              </Tooltip>
            </Flex>
          }
          description={data.description ? data.description : '暂无描述'}
        />
        {recommend ? null : (
          <div className={styles.cardItemContent}>
            <div>
              <span
                style={{
                  display: 'inline-block',
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: data.status ? (data.status === 1 ? 'green' : 'red') : 'grey',
                  marginInlineEnd: 8,
                }}
              />
              {data.status ? (data.status === 1 ? '已上线' : '已下线') : '开发中'}
            </div>
            <Tooltip
              placement="topLeft"
              title={
                <div>
                  {`开始时间：${dayjs(data.beginTime).format('YYYY-MM-DD HH:mm')}`}
                  <br />
                  {`结束时间：${dayjs(data.endTime).format('YYYY-MM-DD HH:mm')}`}
                </div>
              }
            >
              <Progress style={{width: '40%'}} percent={percent} />
            </Tooltip>
          </div>
        )}
      </Card>
    </Col>
  )
}

export default ProjectItem
