$radiuSize: 10px;
$bgc: rgba(0, 0, 0, 0.8);

.waterfallItem {
    width: 100%;
    position: relative;
}


.cover {
    width: 100%;
    border-radius: $radiuSize;

}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 32px;
    margin-right: 10px;
}

.info {
    position: absolute;
    bottom: 0;
    padding: 10px;
    font-size: 12px;
    // background-color: $bgc;
    // border-radius: 0 0px $radiuSize $radiuSize;
    color: #fff;
    box-sizing: border-box;
    width: 100%;
    line-height: 16px;
    display: none;
}

.waterfallItem:hover {
    .info {
        display: flex;
    }

    .cover {
        filter: brightness(60%);
        transition: filter 0.3s ease;
    }

    .copy {
        display: flex;
    }
}

.info__title {
    font-size: 12px;
}

.info__title__p {
    padding: 0;
    margin: 0;
    font-size: 10px;
    flex: 2;
}

.copy {
    background-color: $bgc;
    border-radius: 30px;
    position: absolute;
    top: 3px;
    right: 3px;
    width: 30px;
    height: 30px;
    justify-content: center;
    align-self: center;
    text-align: center;
    color: #fff;
    z-index: 9;
    display: none;
}