import {ProCard} from '@astro/ui/pro'
import {Pagination, Row} from '@astro/ui/ui'
import {useEffect, useMemo, useState} from 'react'
import {AstroPageContainer} from 'src/components/common'
import {EmptyContent, Loading, NotFind} from 'src/components/common/status'
// import {businessColor, businessMap} from '../dashboard/SearchList'
import appStore, {} from 'src/components/dashboard/AppStore'
import ProjectItem from './ProjectItem'

const Recomend = () => {
  const [curPage, setCurPage] = useState(1)
  const projectState = appStore.state

  useEffect(() => {
    projectState.queryProjectList({recommend: true})
  }, [])

  const showList = useMemo(() => {
    if (curPage >= 1) {
      return projectState.list.slice((curPage - 1) * 10, (curPage - 1) * 10 + 12)
    } else {
      return projectState.list
    }
  }, [projectState.list, curPage])

  function onPageChange(current: number, size: number) {
    setCurPage(current)
  }

  return (
    <AstroPageContainer>
      {/* <ProCard> */}
      {/* <div className="astro-recomend"> */}
      {projectState.list && projectState.list.length > 0 ? (
        <>
          <Row gutter={[16, 24]}>
            {showList.map((v: any) => (
              <ProjectItem recommend data={v} key={`item-${v.id}`} />
            ))}
          </Row>
          {projectState.list.length > 12 ? (
            <>
              <br />
              <Pagination
                align="end"
                showQuickJumper
                pageSize={12}
                defaultCurrent={curPage}
                onChange={onPageChange}
                total={projectState.list.length}
              />
            </>
          ) : null}
        </>
      ) : projectState.projectLoad === false ? (
        <EmptyContent />
      ) : (
        <Loading />
      )}
      {/* </div> */}
      {/* </ProCard> */}
    </AstroPageContainer>
  )
}
export default Recomend
