.waterfall {
    column-count: 5;
    column-gap: 26px;
    padding: 16px;

    .material-item {
        break-inside: avoid;
        margin-bottom: 26px;
        overflow: hidden;
        border-radius: 8px;
    }

    .componentCard {
        width: 100%;

        .itemCardImg {
            width: 100%;
            height: auto;
            display: block;
            object-fit: contain;
            max-height: 400px;
        }

        .ant-card-body {
            padding: 12px;
        }

        .ant-card-actions {
            border-top: 1px solid #f0f0f0;

            li {
                margin: 8px 0;
            }
        }
    }
}

.search-header {
    position: sticky;
    top: 50px;
    z-index: 1000;
    transition:
        background-color 0.3s,
        box-shadow 0.3s;
    display: flex;
    justify-content: end;

    .search-container {
        width: 550px;
        padding: 16px;
        position: relative;

        &.scrolled {
            background-color: rgba(255, 255, 255, 0.95);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
    }
}

@media screen and (max-width: 2000px) {
    .waterfall {
        column-count: 5;
    }
}

@media screen and (max-width: 1600px) {
    .waterfall {
        column-count: 4;
    }
}

@media screen and (max-width: 1200px) {
    .waterfall {
        column-count: 3;
    }
}

@media screen and (max-width: 768px) {
    .waterfall {
        column-count: 2;
    }
}

@media screen and (max-width: 576px) {
    .waterfall {
        column-count: 1;
    }
}