import {DeleteOutlined, PlusOutlined, SettingOutlined} from '@astro/ui/icon'
import {<PERSON><PERSON>, Card, Flex, Pagination, Popconfirm, Tooltip, message} from '@astro/ui/ui'
import {useMemo, useState} from 'react'
import {AstroPageContainer} from 'src/components/common'
import SearchSelector from 'src/components/common/SearchSelector'
import userStore from 'src/components/common/user/userStore'
import {deleteMaterial, getMaterialDetail} from 'src/services/material'
import componentStore from '../playload/editPanel/toolPanel/DrawerComponent/componentStore'
import {defaultPng} from './common/utils'
import AddModal from './editPanel/AddModal'
import EditModla from './editPanel/EditModla'
import './index.scss'
const Material = () => {
  const componentState = componentStore.state
  const [curPage, setCurPage] = useState(1)
  // const [total, setTotal] = useState(0)
  // const [list, setList] = useState([])
  const [addStatus, setAddStatus] = useState(false)
  const [editStatus, setEditStatus] = useState(false)
  const [detailInfo, setDetailsInfo] = useState({})
  const pageSize = 100
  const userState = userStore.state
  const [reload, setReLoad] = useState(0)
  function onPageChange(current: number, size: number) {
    setCurPage(current)
  }
  function getList(page?: number) {
    if (page) {
      setCurPage(page)
      setReLoad(reload + 1) // 重新加载页面
    }
  }
  const Item = ({data}: any) => {
    const handleEditClick = () => {
      fetchData()
    }
    const handleDeleteClick = async () => {
      const delist = []
      delist.push(data.id)
      const res = await deleteMaterial({materialIds: delist})
      if (res?.data.result == 200) {
        getList(1)
      }
    }
    async function fetchData() {
      const res = await getMaterialDetail(data.id)
      console.log(res)
      if (res.data?.result == 200) {
        setDetailsInfo(res.data.data)
      } else {
        message.error(res.data?.reason ? res.data?.reason : '未知异常，请稍后再试')
      }
      setEditStatus(true)
    }
    function getActions() {
      const actions: any = [
        <Tooltip key="PlusCircleOutlined" title={'查看组件详情'}>
          <SettingOutlined onClick={handleEditClick} />
        </Tooltip>,
      ]
      if (userState.user.role === 'A') {
        actions.push(
          <Popconfirm
            title="删除组件"
            description="你确定执行删除操作吗"
            onConfirm={handleDeleteClick}
            okText="Yes"
            cancelText="No"
          >
            <DeleteOutlined />
          </Popconfirm>,
        )
      }
      return actions
    }
    return (
      <div className="material-item">
        <Card
          className="componentCard"
          hoverable
          cover={
            <img
              className="itemCardImg"
              alt={data?.title}
              src={data?.img || defaultPng}
              onError={e => {
                const target = e.target as HTMLImageElement
                if (!target.dataset.error) {
                  target.src = defaultPng
                  target.dataset.error = 'true' // 设置标记
                }
              }}
            />
          }
          actions={getActions()}
        >
          {data?.name || '组件标题'}
        </Card>
      </div>
    )
  }
  const renderList = useMemo(() => {
    return componentState?.componentList?.map((item: any, index: number) => (
      <Item data={item} key={`materialsitem-${item.id}`} />
    ))
  }, [componentState?.componentList])
  return (
    <AstroPageContainer>
      <div className="search-header">
        <div className="search-container">
          <Flex align="center" gap="middle">
            <SearchSelector open={true} defaultType={1} pageSize={pageSize} pageNumber={curPage} reload={reload} />
            <Button
              type="primary"
              onClick={() => {
                setAddStatus(true)
              }}
            >
              <Tooltip title="批量管理">
                <PlusOutlined />
              </Tooltip>
            </Button>
          </Flex>
        </div>
      </div>
      <div className="waterfall">{renderList}</div>
      <br />
      <Pagination
        align="end"
        showQuickJumper
        pageSize={pageSize}
        defaultCurrent={curPage}
        onChange={onPageChange}
        total={componentState.total}
      />
      <AddModal addStatus={addStatus} setAddStatus={setAddStatus} completeHandler={getList}></AddModal>
      <EditModla
        editStatus={editStatus}
        setEditStatus={setEditStatus}
        completeHandler={getList}
        detailInfo={detailInfo}
      ></EditModla>
    </AstroPageContainer>
  )
}
export default Material
