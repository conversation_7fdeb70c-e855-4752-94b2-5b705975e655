import {DownOutlined, PlusOutlined} from '@astro/ui/icon'
import {useToken} from '@astro/ui/pro'
import {Button, Divider, Dropdown, Input, type MenuProps, Select, Space, Tag, message} from '@astro/ui/ui'
import React, {useMemo} from 'react'
import {useEffect, useRef, useState} from 'react'
import {keepChineseEnglishNumbers} from 'src/utils'
import tagListStore from './tagListStore'

// 定义 TagList 组件的 props 类型
interface TagListProps {
  type?: string
}

const TagList = (props: TagListProps) => {
  const tagListState = tagListStore.state
  const tagsMap = tagListState.curTagsMap
  const {type = 'all'} = props
  // console.log('###TagList type', type)
  const {token} = useToken()
  const tagArr = tagsMap ? Array.from(tagsMap, ([key, value]) => value) : []
  const newTag = useRef<any>([])
  const [editInputIndex, setEditInputIndex] = useState(-1)
  const [editInputValue, setEditInputValue] = useState('')
  const [inputValue, setInputValue] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)
  const editInputRef = useRef<HTMLInputElement>(null)
  const contentStyle: React.CSSProperties = {
    backgroundColor: token.colorBgElevated,
    borderRadius: token.borderRadiusLG,
    boxShadow: token.boxShadowSecondary,
    maxHeight: '300px',
    overflow: 'auto',
  }
  const tagInputStyle = {
    width: '78px',
    margin: '0 8px',
  }

  const menuItems = useMemo(() => {
    const menu = []
    for (const tag of [...tagArr, ...newTag.current]) {
      menu.push({
        key: tag,
        label: tag,
      })
    }
    return menu
  }, [tagArr?.length, newTag.current?.length])
  const tags = useMemo(() => {
    return tagListState.getMaterialTag(type)
  }, [tagListState.editNum])

  const handleInputChange = (e: any) => {
    setInputValue(e.target.value)
  }
  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditInputValue(e.target.value)
  }

  const handleEditInputConfirm = () => {
    // const newTags = [...tags]
    // newTags[editInputIndex] = editInputValue
    // setEditInputIndex(-1)
  }
  const handleMenuClick: MenuProps['onClick'] = (e: any) => {
    const mapKey = e.key
    tagListState.dealMaterialTagList(mapKey, 'add', type)

    console.log('###handleMenuClick', e)
  }
  const menuProps = {
    items: menuItems,
    onClick: handleMenuClick,
  }
  const handleInput = async () => {
    if (inputValue) {
      const txt = keepChineseEnglishNumbers(inputValue)
      if (!tagArr.includes(txt) && !newTag.current.includes(txt)) {
        newTag.current.push(txt)
        setInputValue('')
      } else {
        message.error('该标签已经存在，请重新输入~')
      }
    }
  }

  const handleClose = (removedTag: string) => {
    tagListState.dealMaterialTagList(removedTag, 'del', type)
  }
  useEffect(() => {
    tagListState.setMapKey(type)
  }, [type])

  return (
    <div style={{display: 'flex', flexWrap: 'wrap', gap: '10px'}}>
      {tags?.map((tag, index) => {
        if (editInputIndex === index) {
          return (
            <Input
              ref={editInputRef}
              key={tag}
              size="small"
              style={tagInputStyle}
              value={editInputValue}
              onChange={handleEditInputChange}
              onBlur={handleEditInputConfirm}
              onPressEnter={handleEditInputConfirm}
            />
          )
        }

        const tagElem = (
          <Tag
            key={tag}
            closable={true} // 允许删除所有标签
            style={{userSelect: 'none', display: 'flex', alignItems: 'center'}}
            onClose={() => handleClose(tag)}
          >
            <span
              onDoubleClick={e => {
                // 双击标签进入编辑状态
                // setEditInputIndex(index)
                // setEditInputValue(tag)
                // e.preventDefault()
              }}
            >
              {tag}
            </span>
          </Tag>
        )
        return tagElem
      })}

      <Dropdown
        menu={menuProps}
        dropdownRender={menu => (
          <div style={contentStyle}>
            {React.cloneElement(
              menu as React.ReactElement<{
                style: React.CSSProperties
              }>,
              {style: {boxShadow: 'none'}},
            )}
            <Divider style={{margin: 0}} />
            <Space style={{padding: 8}}>
              <Input
                ref={inputRef}
                type="text"
                size="small"
                style={{
                  width: 78,
                }}
                value={inputValue}
                onChange={handleInputChange}
              />
              <Button type="primary" onClick={handleInput}>
                新增
              </Button>
            </Space>
          </div>
        )}
      >
        <Button variant="outlined" color="default">
          <Space>
            新增标签
            <DownOutlined />
          </Space>
        </Button>
      </Dropdown>
    </div>
  )
}

export default TagList
