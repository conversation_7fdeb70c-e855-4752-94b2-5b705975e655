import {message} from '@astro/ui/ui'
import {BaseStore} from 'src/store'
class TagListStore extends BaseStore {
  tagList: any[] = [] // 服务器存的tagList数据
  tpyeName = '' //当前业务
  materialTagMap = new Map()
  curTagsMap = new Map()
  editNum = 0
  clear() {
    this.materialTagMap.clear()
    this.curTagsMap.clear()
    this.editNum = 0
    this.tpyeName = ''
  }
  setMapKey(key: string) {
    if (!this.materialTagMap.has(key) && key !== 'all') {
      this.materialTagMap.set(key, '')
    }
  }
  setTagList(value: any) {
    this.tagList = value
  }
  setCurData(map: Map<string, any>, type: string) {
    this.curTagsMap = map
    this.tpyeName = type
  }
  dealMaterialTagList(tagTxt: string, type: 'add' | 'del', key: string) {
    // console.log('###dealMaterialTagList', tagTxt, type, key, this.materialTagMap)
    if (this.materialTagMap.size === 0) {
      message.error('请先输入emp地址再选择标签')
      return
    }
    if (key === 'all') {
      this.materialTagMap.forEach((value, mapkey) => {
        this.dealMaterialTag(tagTxt, type, mapkey)
      })
    } else {
      this.dealMaterialTag(tagTxt, type, key)
    }
    this.editNum++
  }
  dealMaterialTag(tagTxt: string, type: 'add' | 'del', key: string) {
    let mapValue = this.materialTagMap.get(key) || ''
    const arr = mapValue.split(',')
    const flg = arr?.includes(tagTxt)
    if (type === 'add') {
      !flg && (mapValue += `${mapValue && ','}${tagTxt}`)
    } else {
      flg && (mapValue = arr.filter((item: string) => item !== tagTxt).join(','))
    }
    this.materialTagMap.set(key, mapValue)
    console.log('###dealMaterialTag', tagTxt, type, key, this.materialTagMap)
  }
  //本地记录的标签列表，key为emp地址，value为标签字符串
  getMaterialTag(key: string) {
    let tagsStr = ''
    if (key === 'all') {
      this.materialTagMap?.forEach((value, key) => {
        value && (tagsStr += `${tagsStr && ','}${value}`)
      })
    } else {
      tagsStr = this.materialTagMap.get(key) || ''
    }
    return tagsStr ? Array.from(new Set(tagsStr.split(','))) : []
  }
  //提交给服务器的标签列表，key为emp地址
  getMaterialTagList(name: string) {
    const list: any[] = []
    const typeTagList = this.tagList?.filter((item: any) => item.typeName === this.tpyeName)
    const tagVoList = typeTagList[0]?.tagVoList
    const taglistLocal = this.getMaterialTag(name)
    // console.log('###getMaterialTagList', name, tagVoList, taglistLocal)
    taglistLocal.forEach((item: string) => {
      const tagVo = tagVoList?.find((tag: any) => tag.tagName === item)
      if (tagVo) {
        list.push({
          tagId: tagVo.tagId,
          tagName: tagVo.tagName,
          typeId: tagVo.typeId,
        })
      }
    })
    // console.log('###getMaterialTagList', list)
    return list
  }
}
export default new TagListStore()
