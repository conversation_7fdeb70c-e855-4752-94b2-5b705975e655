import {Button, Form, Input, Modal, Space, message} from '@astro/ui/ui'
import {useEffect, useRef, useState} from 'react'
import {getListTag, upsertMaterial} from 'src/services/material'
import {resetTagList} from '../common/CreateDialogUtils'
import type {DetailsParam, ResetTagList, SelectTagItem, TagListItem} from '../common/utils'
import {defaultPng} from '../common/utils'
import styles from './index.module.scss'
type Props = {
  editStatus: boolean
  setEditStatus: (status: boolean) => void
  completeHandler?: (value?: any) => void
  onClose?: (value?: any) => void
  detailInfo: DetailsParam
}
const EditModla = (props: Props) => {
  const {editStatus, setEditStatus, completeHandler, onClose, detailInfo} = props
  const {materialId = 0, extJson = ''} = detailInfo || {}
  const [formTagList, setFormTagList] = useState<ResetTagList>([])
  const [showTagList, setShowTagList] = useState(true)
  const [form] = Form.useForm()
  const materialUrl = Form.useWatch('materialUrl', form)
  const imgRef = useRef<HTMLImageElement>(null)
  function getExtJsonObj() {
    if (extJson) {
      try {
        const obj = JSON.parse(extJson)
        form.setFieldsValue({
          extJson: obj,
          materialName: detailInfo.materialName,
          materialId: detailInfo.materialId,
          materialUrl: detailInfo.materialUrl || defaultPng,
        })
      } catch (err) {}
    }
  }
  const onOkHandler = () => {
    form.submit()
  }
  function onCancelHandler() {
    onClose ? onClose() : setEditStatus(false)
    setTimeout(() => {
      form.resetFields() // 重置表单字段
      // form.setFieldsValue({tagList: formTagList})
    }, 100) // 设置延时，避免关闭时表单闪烁
  }
  function resetUplodData(obj: any, materialId: number) {
    try {
      const info: any = {materialId: materialId}
      Object.keys(obj).forEach((key: any) => {
        // 将列表selectTag取出来重新生成tagList
        if (key == 'tagList') {
          info['tagList'] = []
          const {tagList} = obj
          tagList.forEach((item: TagListItem) => {
            const selectOption: any = item['selectTag'] || []
            selectOption?.forEach((selectItem: SelectTagItem) => {
              const {tagId, tagName, typeId} = selectItem
              info['tagList'].push({
                tagId,
                tagName,
                typeId,
              })
            })
          })
        } else if (key === 'extJson') {
          try {
            info[key] = JSON.stringify(obj[key])
          } catch (e) {}
        } else {
          info[key] = obj[key]
        }
      })
      return info
    } catch (err: any) {
      console.error('resetUplodData error', err.message)
    }
    return obj
  }
  const onFinish = async (values: any) => {
    values['type'] = 4
    const res = await upsertMaterial(resetUplodData(values, materialId))
    console.log('onFinish--', res)
    if (res?.data.result == 200) {
      onCancelHandler()
      message.success('提交成功')
      completeHandler?.(1)
    } else {
      message.error(res?.data.reason ? res?.data.reason : '未知异常，请稍后再试')
    }
  }
  async function getTag() {
    const res = await getListTag()
    const {data, result} = res.data
    if (result == 200) {
      console.log('detailInfo?.tagList', detailInfo?.tagList)
      const tagList: ResetTagList = resetTagList(data ? [...data] : [], detailInfo?.tagList)
      console.log(`tagList`, tagList)
      form.setFieldsValue({tagList: tagList})
      // setFormTagList(tagList)
      setShowTagList(true)
    }
  }
  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo)
  }

  useEffect(() => {
    if (editStatus) {
      // getTag()
      getExtJsonObj()
    }
  }, [editStatus])
  useEffect(() => {
    if (imgRef.current) {
      imgRef.current.dataset.error = 'false'
    }
  }, [materialUrl])
  return (
    <Modal
      title="查看组件详情"
      open={editStatus}
      onClose={() => {
        onCancelHandler()
      }}
      onCancel={onCancelHandler}
      destroyOnClose={true}
      footer={[
        <Button
          key="cancelbtn"
          type="primary"
          htmlType="reset"
          onClick={() => {
            onCancelHandler()
          }}
        >
          关闭
        </Button>,
        // <Button type="primary" htmlType="submit" onClick={onOkHandler} key="confirm">
        //   确定
        // </Button>,
      ]}
      forceRender={true}
      width={680}
    >
      <Form form={form} onFinish={onFinish} onFinishFailed={onFinishFailed} preserve={true}>
        <Space size="large">
          <Form.Item label="组件名称" name="materialName">
            <Input placeholder="组件名称" disabled />
          </Form.Item>
          <Form.Item label="组件Id" name="materialId">
            <Input placeholder="组件Id" disabled />
          </Form.Item>
        </Space>

        <Form.Item label="组件地址" name={['extJson', 'materialUrl']}>
          <Input placeholder="组件地址" disabled />
        </Form.Item>
        <Space size="large">
          <Form.Item label="路劲(path)" name={['extJson', 'path']}>
            <Input placeholder="路劲(path)" disabled />
          </Form.Item>
          <Form.Item label="scope" name={['extJson', 'scope']}>
            <Input placeholder="scope" disabled />
          </Form.Item>
        </Space>
        <Form.Item label="素材预览" name="materialUrl">
          {/* <Input placeholder='素材预览地址' /> */}
          <img
            ref={imgRef}
            className={styles.cardImg}
            alt={'预览图'}
            src={materialUrl || defaultPng}
            onError={e => {
              console.log('Error loading image:', e)
              const target = imgRef.current
              if (target && (!target.dataset.error || target.dataset.error === 'false')) {
                target.src = defaultPng
                target.dataset.error = 'true'
              }
            }}
          />
        </Form.Item>

        {/* {showTagList && (
          <Form.Item label="标签录入">
            <Form.List name="tagList">
              {listTag =>
                listTag.map((item: any, idx: number) => (
                  <Form.Item {...item} key={`tag-${item.typeId}`} {...item}>
                    <FromTagSelect />
                  </Form.Item>
                ))
              }
            </Form.List>
          </Form.Item>
        )} */}
      </Form>
    </Modal>
  )
}
export default EditModla
