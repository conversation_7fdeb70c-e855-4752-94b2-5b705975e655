import {Button, Card, Form, Input, List, Modal, message} from '@astro/ui/ui'
import {useEffect, useState} from 'react'
import componentStore from 'src/components/playload/editPanel/toolPanel/DrawerComponent/componentStore'
import {batchUpsertMaterial, getListTag, type UpsertMaterialParams} from 'src/services/material'
import {resetTagList} from '../common/CreateDialogUtils'
import {
  defaultPng,
  extractAndFormatVersion,
  extractBaseURL,
  extractPackageName,
  fetchJsonData,
  type ResetTagList,
} from '../common/utils'
import TagList from './TagList'
import tagListStore from './TagList/tagListStore'
import TagPanel from './TagPanel'
type Props = {
  addStatus: boolean
  setAddStatus: (status: boolean) => void
  completeHandler?: (value?: any) => void
  onClose?: (value?: any) => void
}

const AddMaterialModal = (props: Props) => {
  const componentState = componentStore.state
  const {addStatus, setAddStatus, completeHandler, onClose} = props
  const [materialList, setMaterialList] = useState<any>(null)
  const [formTagList, setFormTagList] = useState<ResetTagList>([])
  const [showTagList, setShowTagList] = useState(true)
  const [baseURL, setBaseURL] = useState<string | null>(null)
  const [version, setVersion] = useState<string | null>(null)
  const [pkgName, setPkgName] = useState<string>('')
  const [form] = Form.useForm()
  //组件名编辑
  const [isEditing, setIsEditing] = useState<boolean>(false)
  const [editTitle, setEditTitle] = useState<string>('')
  const [currentItemIndex, setCurrentItemIndex] = useState<number>(-1)
  const handleEdit = (item: any, index: number) => {
    setIsEditing(true)
    setEditTitle(item.upsetName || item.name) // 如果有 upsetName，则使用 upsetName；否则使用 name
    setCurrentItemIndex(index)
  }

  const handleSave = (index: number) => {
    // 更新 materialList 中的标题
    const updatedList = materialList.map((material: any, idx: number) =>
      idx === index ? {...material, upsetName: editTitle} : material,
    )
    setMaterialList(updatedList) // 使用 setMaterialList 更新状态
    setIsEditing(false)
    setCurrentItemIndex(-1)
  }

  const handleCancel = () => {
    setIsEditing(false)
    setCurrentItemIndex(-1)
  }
  //const pageSize = 24
  async function fetchData(project: string) {
    // const res = await getMaterials('', pageSize, 1, undefined, project)
    const res = await componentState.searchProjectComponent(project)
    return res
  }

  async function getTag() {
    const res = await getListTag()
    const {data, result} = res.data
    if (result == 200) {
      const tagList: ResetTagList = resetTagList(data ? [...data] : [], null)
      console.log(`tagList`, tagList)
      form.setFieldsValue({tagList: tagList})
      setFormTagList(tagList)
      setShowTagList(true)
    }
  }
  function resetUplodData(obj: any) {
    try {
      const info: any = {}
      Object.keys(obj).forEach((key: any) => {
        // 将列表selectTag取出来重新生成tagList
        if (key == 'tagList') {
          const {tagList} = obj
          // info['tagList'] = []
          // console.log(`###tagList`, tagList)
          // tagList.forEach((item: TagListItem) => {
          //   const selectOption: any = item['selectTag'] || []
          //   selectOption?.forEach((selectItem: SelectTagItem) => {
          //     const {tagId, tagName, typeId} = selectItem
          //     info['tagList'].push({
          //       tagId,
          //       tagName,
          //       typeId,
          //     })
          //   })
          // })
          tagListStore.setTagList(tagList)
        } else if (key === 'extJson') {
          try {
            info[key] = JSON.stringify(obj[key])
          } catch (e) {}
        } else {
          info[key] = obj[key]
        }
      })
      return info
    } catch (err: any) {
      console.error('resetUplodData error', err.message)
    }
    return obj
  }

  function onCancelHandler() {
    onClose ? onClose() : setAddStatus(false)
    setTimeout(() => {
      form.resetFields() // 重置表单字段
      form.setFieldsValue({tagList: formTagList})
      setMaterialList(null)
    }, 100) // 设置延时，避免关闭时表单闪烁
    tagListStore.clear()
  }
  const onFinish = async (values: any) => {
    values['type'] = 4
    const tagList = resetUplodData(values)?.tagList || []
    const batchUpsertMaterialDTOs: UpsertMaterialParams[] = []
    fetchData(pkgName || '')
      .then(res => {
        const list = res.list
        materialList.map((material: {path: string; name: string; upsetName?: string}) => {
          const extJsonObj = {
            materialUrl: values.empUrl,
            path: material.path,
            scope: version,
          }
          const extJsonStr = JSON.stringify(extJsonObj)
          const params: UpsertMaterialParams = {
            type: 4,
            extJson: extJsonStr,
            materialUrl: baseURL ? `${baseURL}/astro/${material.name}.cover.png` : defaultPng,
            project: pkgName,
            materialName: material.upsetName || material.name,
            tagList: tagListStore.getMaterialTagList(material.path),
          }
          list.map((item: {project: string | null; path: string; id: any}) => {
            if (item.project === pkgName && item.path === `./${material.name}`) {
              params.materialId = item.id
            }
          })

          batchUpsertMaterialDTOs.push(params)
        })
        console.log('batchUpsertMaterialDTOs', batchUpsertMaterialDTOs)
        upset()
      })
      .catch(error => {
        console.error('fetchData 发生错误:', error)
        upset()
      })

    async function upset() {
      const res = await batchUpsertMaterial(batchUpsertMaterialDTOs)
      console.log('onFinish--', res)
      if (res?.data.result == 200) {
        onCancelHandler()
        message.success('提交成功')
        completeHandler?.(1)
      } else {
        message.error(res?.data.reason ? res?.data.reason : '未知异常，请稍后再试')
      }
    }
  }
  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo)
  }
  const onOkHandler = () => {
    form.submit()
  }
  const onChangeEmp = async (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setMaterialList(null)
    const jsFileUrl = e.target.value
    if (!jsFileUrl) {
      return
    }
    if (jsFileUrl && jsFileUrl.endsWith('.js')) {
      console.log(extractBaseURL(jsFileUrl))
      setBaseURL(extractBaseURL(jsFileUrl))
      setVersion(extractAndFormatVersion(jsFileUrl))
      setPkgName(extractPackageName(jsFileUrl) || '')
      const jsonUrl = jsFileUrl.replace('.js', '.json')
      const jsonData = await fetchJsonData(jsonUrl)
      console.log('jsonData', jsonData)
      setMaterialList(jsonData?.exposes || null)
      tagListStore.clear()
    } else {
      message.error('无效的链接，请输入正确的emp包地址')
    }
  }
  useEffect(() => {
    if (addStatus) {
      getTag()
    }
  }, [addStatus])
  return (
    <Modal
      title="批量管理组件"
      open={addStatus}
      onClose={() => {
        onCancelHandler()
      }}
      onCancel={onCancelHandler}
      destroyOnClose={true}
      footer={[
        <Button
          key="cancelbtn"
          type="primary"
          htmlType="reset"
          onClick={() => {
            onCancelHandler()
          }}
        >
          取消
        </Button>,
        <Button type="primary" htmlType="submit" onClick={onOkHandler} key="confirm">
          确定
        </Button>,
      ]}
      forceRender={true}
      width={680}
    >
      <Form form={form} onFinish={onFinish} onFinishFailed={onFinishFailed} preserve={true}>
        <Form.Item label="" name="empUrl" rules={[{required: true}]}>
          <Input placeholder="请输入emp地址" onChange={onChangeEmp} />
        </Form.Item>
        {materialList && (
          <Form.Item label="组件列表">
            <List
              grid={{gutter: 16, column: 3}}
              dataSource={materialList}
              renderItem={(item: {upsetName: string | null; name: string; path: string}, index) => (
                <List.Item>
                  <Card
                    style={{textAlign: 'center'}}
                    title={
                      isEditing && index === currentItemIndex ? (
                        <Input
                          value={editTitle}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditTitle(e.target.value)}
                          onBlur={() => handleSave(index)} // 监听输入完成
                          onPressEnter={() => handleSave(index)} // 按回车键也保存
                          autoFocus // 自动聚焦
                        />
                      ) : (
                        <div onClick={() => handleEdit(item, index)}>
                          {item.upsetName || item.name} {/* 如果有 upsetName，则显示 upsetName；否则显示 name */}
                        </div>
                      )
                    }
                    cover={
                      <img
                        alt="example"
                        src={baseURL ? `${baseURL}/astro/${item.name}.cover.png` : defaultPng}
                        onError={e => {
                          const target = e.target as HTMLImageElement
                          if (!target.dataset.error) {
                            target.src = defaultPng // 替换为你的默认图片地址
                            target.dataset.error = 'true' // 设置标记
                          }
                        }}
                      />
                    }
                  >
                    <TagList type={item?.path} />
                  </Card>
                </List.Item>
              )}
            />
          </Form.Item>
        )}
        {showTagList && (
          // <Form.Item label="标签录入">
          //   <Form.List name="tagList">
          //     {listTag =>
          //       listTag.map((item: any, idx: number) => {
          //         const {key, ...restItemProps} = item
          //         return (
          //           <Form.Item key={`tag-${idx}-${item.typeId}`} {...restItemProps}>
          //             <FromTagSelect />
          //           </Form.Item>
          //         )
          //       })
          //     }
          //   </Form.List>
          // </Form.Item>
          <Form.Item name="tagList">
            <TagPanel getTag={getTag} />
            {/* <CheckboxList getTag={getTag} /> */}
          </Form.Item>
        )}
      </Form>
    </Modal>
  )
}
export default AddMaterialModal
