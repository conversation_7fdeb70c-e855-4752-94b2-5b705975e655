import {DownOutlined, PlusOutlined, QuestionCircleOutlined} from '@astro/ui/icon'
import {Button, Divider, Dropdown, Flex, Input, Popover, Radio, Space, Tag, message, theme} from '@astro/ui/ui'
import type {MenuProps} from '@astro/ui/ui'
import React from 'react'
import {useMemo, useRef, useState} from 'react'
import {updateTagType} from 'src/services/material'
import {keepChineseEnglishNumbers} from 'src/utils'
import TagList from './TagList'
import tagListStore from './TagList/tagListStore'
const {useToken} = theme

// 定义 Checkbox 组件的 props 类型
interface CheckboxListProps {
  value?: any
  getTag?: () => void // 更新业务端标签
}

const TagPanel: React.FC<CheckboxListProps> = ({value, getTag}) => {
  const tagListState = tagListStore.state
  const {token} = useToken()
  const [dropdownText, setDropdownText] = useState('请选择')
  const [inputValue, setInputValue] = useState('')
  const inputRef = useRef(null)
  const contentStyle: React.CSSProperties = {
    backgroundColor: token.colorBgElevated,
    borderRadius: token.borderRadiusLG,
    boxShadow: token.boxShadowSecondary,
  }
  const arrMap = useMemo(() => {
    return value?.reduce((acc: any, item: any) => {
      acc.set(item?.typeName, item?.tagVoList)
      return acc
    }, new Map())
  }, [JSON.stringify(value)])
  const dealTagMap = (mapKey: string) => {
    const tags = new Map<string, string>()
    const mapObj = arrMap.get(mapKey)
    if (mapObj) {
      mapObj.forEach((item: any) => {
        if (!tags.get(item?.tagName)) tags.set(item?.tagName, item?.tagName)
      })
    }
    tagListState.setCurData(tags, mapKey)
  }
  const handleMenuClick: MenuProps['onClick'] = (e: any) => {
    const mapKey = e.key
    setDropdownText(mapKey)
    dealTagMap(mapKey)
  }
  const menuItems = useMemo(() => {
    const options: any = []
    if (value?.length > 0) {
      for (const item of value) {
        options.push({
          label: item?.typeName,
          key: item?.typeName,
        })
      }
    }
    setDropdownText(options[0]?.label || '请选择')
    options[0]?.label && dealTagMap(options[0]?.label)
    return options
  }, [JSON.stringify(value)])
  const menuProps = {
    items: menuItems,
    onClick: handleMenuClick,
  }

  const handleInputChange = (e: any) => {
    setInputValue(e.target.value)
  }

  const handleInput = async () => {
    if (inputValue) {
      const txt = keepChineseEnglishNumbers(inputValue)
      if (!arrMap.has(txt)) {
        //业务组名字影响比较大暂不支持修改
        const res = await updateTagType({name: inputValue})
        if (res?.data.result == 200) {
          getTag?.()
        } else {
          message.error(res?.data.reason || '添加失败~')
        }
        setInputValue('')
      } else {
        message.error('已存在分组，请重新输入~')
      }
    }
  }

  return (
    <Flex vertical>
      <Divider style={{margin: 10}} />
      <Flex>
        <Space>
          业务类型：
          <Dropdown
            menu={menuProps}
            dropdownRender={menu => (
              <div style={contentStyle}>
                {React.cloneElement(
                  menu as React.ReactElement<{
                    style: React.CSSProperties
                  }>,
                  {style: {boxShadow: 'none'}},
                )}
                <Divider style={{margin: 0}} />
                <Space style={{padding: 8}}>
                  <Input
                    ref={inputRef}
                    type="text"
                    size="small"
                    style={{
                      width: 78,
                    }}
                    value={inputValue}
                    onChange={handleInputChange}
                  />
                  <Button type="primary" onClick={handleInput}>
                    新增
                  </Button>
                </Space>
              </div>
            )}
          >
            <Button variant="outlined" color="default">
              <Space>
                {dropdownText}
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </Space>
      </Flex>
      <Space style={{marginTop: 6}}>
        通用标签
        <Popover key={'tagPanel'} content="批量新增/删除所有组件的标签" trigger="hover">
          <QuestionCircleOutlined style={{cursor: 'pointer'}} />
        </Popover>
        ：
        <TagList />
      </Space>
    </Flex>
  )
}

export default TagPanel
