import {PlusOutlined} from '@astro/ui/icon'
import {Checkbox, Divider, Flex, Input, Space, Tag, message} from '@astro/ui/ui'
import type {CheckboxProps} from '@astro/ui/ui'
import type React from 'react'
import {useMemo, useRef, useState} from 'react'
import {updateTagType} from 'src/services/material'
import TagList from './TagList'

const CheckboxGroup = Checkbox.Group

// 定义 Checkbox 组件的 props 类型
interface CheckboxListProps {
  value?: any
  getTag?: () => void // 更新业务端标签
}

const CheckboxList: React.FC<CheckboxListProps> = ({value, getTag}) => {
  const [checkedList, setCheckedList] = useState<string[]>([])
  const [inputVisible, setInputVisible] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [tagMap, setTagMap] = useState(new Map<string, string>())
  const inputRef = useRef(null)

  const arrMap = useMemo(() => {
    return value?.reduce((acc: any, item: any) => {
      acc.set(item?.typeId, item?.tagVoList)
      return acc
    }, new Map())
  }, [JSON.stringify(value)])
  const plainOptions = useMemo(() => {
    const options: any = []
    if (value?.length > 0) {
      for (const item of value) {
        options.push({
          label: item?.typeName,
          value: item?.typeId,
        })
      }
    }
    return options
  }, [JSON.stringify(value)])
  const checkAll = plainOptions.length === checkedList.length
  const indeterminate = checkedList.length > 0 && checkedList.length < plainOptions.length
  const dealTagList = (list: any[]) => {
    setCheckedList(list)
    const tags = new Map<string, string>()
    for (const item of list) {
      const mapObj = arrMap.get(item)
      if (mapObj) {
        mapObj.forEach((item: any) => {
          if (!tags.get(item?.tagName)) tags.set(item?.tagName, item?.tagName)
        })
      }
    }
    setTagMap(tags)
  }
  const onChange = (list: string[]) => {
    dealTagList(list)
  }

  const handleInputChange = (e: any) => {
    setInputValue(e.target.value)
  }

  const onCheckAllChange: CheckboxProps['onChange'] = e => {
    const list: any = plainOptions?.map((item: any) => item?.value)
    dealTagList(e.target.checked ? list : [])
  }

  const showInput = () => {
    setInputVisible(true)
  }
  const handleInput = async () => {
    if (inputValue) {
      //业务组名字影响比较大暂不支持修改
      const res = await updateTagType({name: inputValue})
      if (res?.data.result == 200) {
        getTag?.()
      } else {
        message.error(res?.data.reason || '添加失败~')
      }
    }

    setInputVisible(false)
    setInputValue('')
  }
  const addTag = () => {
    return (
      <Space style={{marginBottom: '12px', fontWeight: 'bold'}}>
        {inputVisible && (
          <Input
            ref={inputRef}
            type="text"
            size="small"
            style={{
              width: 78,
            }}
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInput}
            onPressEnter={handleInput}
          />
        )}
        {!inputVisible && (
          <Tag onClick={showInput} style={{cursor: 'pointer', width: '88px'}}>
            <Space>
              <PlusOutlined />
              新增标签
            </Space>
          </Tag>
        )}
      </Space>
    )
  }
  return (
    <Flex vertical style={{marginTop: '5px'}}>
      {plainOptions?.length > 0 && (
        <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
          全选
        </Checkbox>
      )}
      <Divider style={{fontSize: '12px', fontWeight: 'bold'}} orientation="left">
        业务端
      </Divider>
      <Flex>
        <Flex vertical>
          {addTag()}
          <CheckboxGroup options={plainOptions} value={checkedList} onChange={onChange} />
        </Flex>
      </Flex>
      {checkedList?.length > 0 && (
        <div>
          <Divider style={{fontSize: '12px', fontWeight: 'bold'}} orientation="left">
            批量标签管理
          </Divider>
          <TagList />
        </div>
      )}
    </Flex>
  )
}

export default CheckboxList
