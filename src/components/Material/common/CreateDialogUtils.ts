export const initInitialPanes = (stateList: any) => {
  try {
    if (stateList) {
      const list: Array<any> = []
      for (let i = 0; i < stateList?.length; i++) {
        const item: any = {}
        item['title'] = '状态' + (i + 1)
        item['key'] = i + 1 + ''
        item['stateId'] = stateList[i]['stateId']
        item['stateUrl'] = stateList[i]['stateUrl']
        item['stateExplain'] = stateList[i]['stateExplain']
        item['firstWorkHours'] = stateList[i]['firstWorkHours']
        item['reuseWorkHours'] = stateList[i]['reuseWorkHours']
        list.push(item)
      }
      if (list.length > 0) return list
    }
  } catch (err: any) {}
  return [
    {title: '状态1', key: '1', stateId: 0, stateUrl: '', stateExplain: '', setFirstWorkHours: 0, reuseWorkHours: 0},
  ]
}

export const initInitialPanes2 = (stateList: any) => {
  const pans: Array<any> = []
  pans[0] = {
    key: `0`,
    activityList: null,
    stateExplain: '',
    stateId: 0,
    stateName: '状态1',
    stateUrl: '',
    firstWorkHours: 0,
    reuseWorkHours: 0,
  }
  if (stateList) {
    stateList.forEach((item: any, ind: number) => {
      pans[ind] = {...item, key: `${ind}`}
    })
  }
  return pans
}

export const resetOptions = (list: Array<any>) => {
  const newlist: any = []
  list?.forEach(item => {
    const {tagName, typeId} = item
    if (tagName) {
      newlist.push({
        ...item,
        value: tagName + '_' + (typeId == 1 ? 'gold' : typeId == 2 ? 'lime' : 'cyan'),
        label: tagName,
      })
    }
  })
  return newlist
}

// 给列表加selectTag字段
export const resetTagList = (target: any, cloneList: any) => {
  if (target?.length) {
    target?.forEach((item: any) => {
      item['selectTag'] = []
      if (cloneList?.length) {
        cloneList?.forEach((selectListItem: any) => {
          if (selectListItem?.typeId == item.typeId) {
            item['selectTag'].push(selectListItem)
          }
        })
      }
    })
  }
  return target
}
