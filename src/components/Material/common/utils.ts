export const fetchJsonData = async (url: string) => {
  try {
    const response = await fetch(url)
    return await response.json()
  } catch (error) {
    console.error('json show', error)
  }
}
//以输入https://unpkg.yy.com/@astro-ui/template@0.2.0/dist/emp.js 为例子

//提取 "https://unpkg.yy.com/@astro-ui/template@0.2.0/dist" 用于拼接组件图片
export function extractBaseURL(url: string) {
  try {
    const match = url.match(/https:\/\/[^/]+\/@[^/]+\/[^@]+@[^/]+\/dist/)
    if (!match) {
      throw new Error('无法从 URL 中提取图片地址')
    }
    return match ? match[0] : null
  } catch (error) {
    console.error('解析 URL 时发生错误')
    return ''
  }
}

//提取 "astro_ui_template_0_2_0" 用于传scope
export function extractAndFormatVersion(input: string): string {
  try {
    // 提取 URL 部分
    const urlMatch = input.match(/https?:\/\/[^"]+/)
    if (!urlMatch) {
      throw new Error('无法从输入中提取有效的 URL')
    }
    const url = urlMatch[0]
    // 提取 @astro-ui/ 和 /dist 之间的内容
    const versionMatch = url.match(/@astro-ui\/([^@]+)@([\w.-]+)\/dist/)
    if (!versionMatch) {
      throw new Error('无法从 URL 中提取版本信息')
    }

    // 提取组件名称和版本号
    let [, name, version] = versionMatch
    name = name.replace(/[-.]/g, '_') // 将组件名称中的 - 和 . 替换为 _
    version = version.replace(/[-.]/g, '_') // 将版本号中的 - 和 . 替换为 _

    return `astro_ui_${name}_${version}`
  } catch (error) {
    console.error('解析输入时发生错误')
    return ''
  }
}

//提取 "@astro-ui/template"用于传projeck pkg报名
export function extractPackageName(url: string) {
  try {
    const match = url.match(/@[^/]+\/[^@]+/)
    if (!match) {
      throw new Error('无法从 URL 中提取pkg name')
    }
    return match ? match[0] : null
  } catch (error) {
    console.error('解析 URL 时发生错误')
    return ''
  }
}
export const defaultPng = 'https://hd-static.yystatic.com/6949637205046106.png' // 默认图片地址
export type ResetTagList = Array<ResetTagListItem>
export type ResetTagListItem = {
  selectTag: Array<tagVoItem>
  tagVoList: Array<tagVoItem>
  typeId: number
  typeName: string
}
export type tagVoItem = {
  tagId: number
  tagName: string
  typeId: number
}
export interface SelectTagItem {
  label: string
  tagId: number
  tagName: string
  typeId: number
  value: string
}
export interface TagListItem {
  option?: Array<SelectTagItem>
  selectTag?: Array<SelectTagItem>
}
export type DetailsParam = {
  extJson?: string
  tagList?: Array<any>
  materialName?: string
  type?: number
  materialId?: number
  materialUrl?: string
}
