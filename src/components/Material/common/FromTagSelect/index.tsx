import {PlusOutlined} from '@astro/ui/icon'
import {Input, Select, Tag} from '@astro/ui/ui'
import {useEffect, useMemo, useRef, useState} from 'react'
import {resetOptions} from '../CreateDialogUtils'
import TagRender from './TagRender'
import styles from './index.module.scss'

interface FromTagSelectProps {
  id?: any
  value?: any
  onChange?: (value: any) => void
}

const FromTagSelect = (props: FromTagSelectProps, a: any) => {
  const {onChange, value, id} = props
  const [inputVisible, setInputVisible] = useState(false)
  const inputRef = useRef(null)
  const [inputValue, setInputValue] = useState('')
  const [options, setOptions] = useState(resetOptions(value?.tagVoList))

  const defaultValue = useMemo(() => {
    if (value?.selectTag) {
      return resetOptions(value?.selectTag)
    }
  }, [JSON.stringify(value?.selectTag)])

  const handleSelectChange = (data: any, option: any) => {
    // const val = {...value, option}
    onChange?.({...value, selectTag: option})
  }

  const showInput = () => {
    setInputVisible(true)
  }

  const handleInputChange = (e: any) => {
    setInputValue(e.target.value)
  }

  const handleInputConfirm = () => {
    if (inputValue) {
      for (let i = 0; i < options.length; i++) {
        if (options[i].value != inputValue) {
          const addList = resetOptions([{tagId: 0, tagName: inputValue, typeId: options[i].typeId}])
          addList?.length > 0 && setOptions([...options, addList[0]])
          break
        }
      }
    }

    setInputVisible(false)
    setInputValue('')
  }

  useEffect(() => {
    onChange?.(value)
  }, [])

  useEffect(() => {
    value?.tagVoList && setOptions(resetOptions(value?.tagVoList))
  }, [value])

  const addTag = () => {
    return (
      <>
        {inputVisible && (
          <Input
            ref={inputRef}
            type='text'
            size='small'
            style={{
              width: 78
            }}
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputConfirm}
            onPressEnter={handleInputConfirm}
          />
        )}
        {!inputVisible && (
          <Tag onClick={showInput} className={styles.addTag}>
            <PlusOutlined />
          </Tag>
        )}
      </>
    )
  }
  return (
    <div className={styles.fromTagSelect}>
      <span className={styles.tagDesc}>{value?.typeName || ''}：</span>
      <Select
        mode='multiple'
        // showArrow={true}
        tagRender={TagRender}
        onChange={handleSelectChange}
        defaultValue={defaultValue}
        options={options}
        style={{
          width: '100%'
        }}
      />
      {addTag()}
    </div>
  )
}

export default FromTagSelect
