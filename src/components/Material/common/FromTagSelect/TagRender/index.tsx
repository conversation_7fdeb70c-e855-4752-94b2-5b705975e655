import {Tag} from '@astro/ui/ui'

const TagRender = (props: any) => {
  const {label, value, closable, onClose} = props
  const onPreventMouseDown = (event: any) => {
    event.preventDefault()
    event.stopPropagation()
  }
  function getColor(): string {
    if (value) {
      const startId = value?.lastIndexOf('_')
      return value.substring(startId + 1, value.length)
    }
    return 'purple'
  }
  return (
    <Tag
      color={getColor()}
      onMouseDown={onPreventMouseDown}
      closable={closable}
      onClose={onClose}
      style={{
        marginRight: 3
      }}
    >
      {label}
    </Tag>
  )
}

export default TagRender
