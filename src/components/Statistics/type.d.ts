// 项目详情类型
interface ProjectDetail {
  id: number // 项目唯一标识符
  actName: string // 项目名称
  workDays: number // 项目总工时(天)
  reduceDays: number // 项目节省人力(天)
  isOnline: boolean // 项目是否已上线
  onlineTime: number // 项目上线时间戳(毫秒)
  componentCount: number // 项目使用的组件总数
  reusedComponentCount: number // 项目中复用的组件数量
  componentIds: Set<string> // 项目使用的组件ID集合
  actDocumentUrl: string // 需求文档链接
  uiUrl: string // 设计稿链接
  editorPassports: UserInfo[] // 参与者信息
  statTags: [] // 分类标签
}

interface UserInfo {
  sex: string // 性别
  sign: string // 个性签名
  hdlogo: string // 高清头像链接
  register_time: string // 注册时间
  nick: string // 昵称
  passport: string // 通行证/账号
  jifen: string // 积分
  yyno: string // 用户编号
  id: string // 用户ID
  yylogo: string // 用户logo链接
  account: string // 账号（可能为空字符）
}

// 统计维度类型年
interface StatDimension {
  projectCount: number // 项目总数
  totalWorkDays: number // 所有项目实际总人力（天）
  totalSavedManpowerDays: number // 所有项目总节省人力(天)
  onlineActivityCount: number // 已上线活动数量
}

// 项目统计数据类型
export interface ProjectStats {
  previousStatDimension: StatDimension // 上一周期统计总览
  statDimension: StatDimension // 当前统计周期总览
  projectList: ProjectDetail[] // 项目详情列表
}

// 后台统计数据

// 表类型枚举（假设TableType对应的前端枚举）
enum TableType {
  NORMAL = 'NORMAL',
  SYSTEM = 'SYSTEM',
  TEMP = 'TEMP',
}

// 编辑人信息
interface EditorInfo {
  uid: number // 编辑人UID
  passport: string // 编辑人昵称
  editCount: number // 编辑次数
  lastEditTime: string // 最后编辑时间(yyyy-MM-dd HH:mm:ss格式)
}

// 表信息
interface TableInfo {
  tableId: string // 表ID
  tableKey: string // 表key
  tableName: string // 表名
  tableType: TableType // 表类型
  fieldCount: number // 字段数量
  buttonCount: number // 按钮数量
  document?: string // 文档内容(可选)
  editors: EditorInfo[] // 版本编辑人列表
  createTime: string // 创建时间(yyyy-MM-dd HH:mm:ss格式)
}

// 统计概览响应主体
export interface IBackendSummary {
  // year: number // 查询年份
  // quarter: number // 查询季度
  tableCount: number // 表创建数量
  buttonCount: number // 按钮总数量
  fieldCount: number // 字段总数量
  previousTableCount: number // 上一周期表创建数量（用于对比）
  previousButtonCount: number // 上一周期按钮数量（用于对比）
  previousFieldCount: number // 上一周期字段数量（用于对比）

  tableGrowthRate: number // 表创建数量增长率
  buttonGrowthRate: number // 按钮数量增长率
  fieldGrowthRate: number // 字段数量增长率
  periodDescription: string // 统计时间范围描述
  previousPeriodDescription: string // 对比时间范围描述
  tables: TableInfo[] // 表详情列表
}
