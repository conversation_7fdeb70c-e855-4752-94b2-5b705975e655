// 项目概览组件
import {ArrowDownOutlined, ArrowUpOutlined} from '@astro/ui/icon'
import {Card, Col, Divider, Flex, Progress, Row, Statistic, Tooltip} from '@astro/ui/ui'
import type React from 'react'
import CountUp from 'react-countup'
import type {ProjectStats} from './type'

export const formatter: any = (value: number) => <CountUp end={value as number} separator="," />

interface ProjectOverviewProps {
  statInfo: ProjectStats
  totalComponents: number
  totalReusedComponents: number
  quarter: string
}

type ContrastResult = {
  percentage: number
  isIncrease: boolean
  isDecrease: boolean
  isEqual: boolean
}

const calculateContrast = (current: number, previous: number): ContrastResult => {
  if (previous === 0) return {percentage: 0, isIncrease: false, isDecrease: false, isEqual: false}
  const percentage = safeDivide((current - previous) * 100, previous)
  return {
    percentage,
    isIncrease: percentage > 0,
    isDecrease: percentage < 0,
    isEqual: percentage === 0,
  }
}

const ContrastDisplay: React.FC<{previous: number; result: ContrastResult; quarter: string; suffix: string}> = ({
  previous,
  result,
  quarter,
  suffix,
}) => {
  const prefix = quarter ? '季度环比：' : '年度环比：'
  // const prefix = '环比：'

  if (!result.percentage) return <>{prefix}-</>
  if (result.isEqual) return <>{prefix}持平</>

  const Icon = result.isIncrease ? ArrowUpOutlined : ArrowDownOutlined
  const color = result.isIncrease ? 'green' : 'red'

  return (
    <Flex gap={10}>
      <span>
        上{quarter ? '季度' : '年度'}
        {suffix === '%' ? (previous * 100).toFixed(2) : previous}
        {suffix}
      </span>
      <span>|</span>
      <Flex>
        环比
        <Icon style={{color}} />
        <CountUp style={{color: color}} end={Math.abs(result.percentage)} decimals={2} duration={1.5} suffix="%" />
      </Flex>
    </Flex>
  )
}

const dataContrast = (current: number, previous: number, quarter: string, suffix: string) => {
  const result = calculateContrast(current, previous)
  return <ContrastDisplay previous={previous} result={result} quarter={quarter} suffix={suffix} />
}

// 安全除法
const safeDivide = (numerator: number, denominator: number): number => {
  if (!denominator) return 0
  return numerator / denominator
}

const ProjectOverview: React.FC<ProjectOverviewProps> = ({
  statInfo,
  totalComponents,
  totalReusedComponents,
  quarter,
}) => {
  return (
    <Row gutter={16}>
      <Col span={6}>
        <Card style={{height: '150px'}}>
          <Statistic title="项目总数（个）" value={statInfo?.statDimension?.projectCount || 0} formatter={formatter} />
          <Divider style={{margin: 10}} />
          {dataContrast(
            statInfo?.statDimension?.projectCount,
            statInfo?.previousStatDimension?.projectCount,
            quarter,
            '个',
          )}
        </Card>
      </Col>
      <Col span={6}>
        <Card style={{height: '150px'}}>
          <Statistic
            title="节省时间（人天）"
            value={statInfo?.statDimension?.totalSavedManpowerDays || 0}
            formatter={formatter}
          />
          <Divider style={{margin: 10}} />
          {dataContrast(
            statInfo?.statDimension?.totalSavedManpowerDays || 0,
            statInfo?.previousStatDimension?.totalSavedManpowerDays || 0,
            quarter,
            '人天',
          )}
        </Card>
      </Col>
      <Col span={6}>
        <Card style={{height: '150px'}}>
          <Tooltip
            title={
              <div>
                <div>
                  预估总人天：
                  {(statInfo?.statDimension?.totalWorkDays || 0) +
                    (statInfo?.statDimension?.totalSavedManpowerDays || 0)}
                  人天
                </div>
                <div>实际总人天：{statInfo?.statDimension?.totalWorkDays || 0} 人天</div>
                <div>节省总人天：{statInfo?.statDimension?.totalSavedManpowerDays || 0} 人天</div>
              </div>
            }
          >
            <Statistic
              title="总提效率"
              value={(() => {
                const saved = statInfo?.statDimension?.totalSavedManpowerDays || 0
                const actual = statInfo?.statDimension?.totalWorkDays || 0
                const total = saved + actual
                return total ? Number(((saved / total) * 100).toFixed(2)) : 0
              })()}
              suffix="%"
              formatter={formatter}
            />
            <Divider style={{margin: 10}} />
            {dataContrast(
              safeDivide(
                statInfo?.statDimension?.totalSavedManpowerDays || 0,
                statInfo?.statDimension?.totalSavedManpowerDays + statInfo?.statDimension?.totalWorkDays,
              ),
              safeDivide(
                statInfo?.previousStatDimension?.totalSavedManpowerDays || 0,
                statInfo?.previousStatDimension?.totalSavedManpowerDays +
                  statInfo?.previousStatDimension?.totalWorkDays,
              ),
              quarter,
              '%',
            )}
          </Tooltip>
        </Card>
      </Col>
      <Col span={6}>
        <Card style={{height: '150px'}}>
          <Statistic
            title="组件覆盖率"
            value={totalComponents ? Number(((totalReusedComponents / totalComponents) * 100).toFixed(2)) : 0}
            suffix="%"
            formatter={formatter}
          />

          <Divider style={{margin: 10}} />

          <Flex justify={'space-between'} align={'center'}>
            <Flex gap={10} align={'center'}>
              <div>复用数：{totalReusedComponents}</div> | <div>总数：{totalComponents}</div>
            </Flex>
            <Progress
              style={{width: '150px'}}
              percent={totalComponents ? Number(((totalReusedComponents / totalComponents) * 100).toFixed(2)) : 0}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
          </Flex>
        </Card>
      </Col>
    </Row>
  )
}

export default ProjectOverview
