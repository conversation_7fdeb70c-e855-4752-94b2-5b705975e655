import {Card, Flex, Spin} from '@astro/ui/ui'
import React, {useState} from 'react'
import {queryBackendStat} from 'src/services/material'
import type {IBackendSummary} from '../type'
import BackendProjectOverview from './BackendProjectOverview'
import BackendTable from './BackendTable'
import TableTypeDetail from './TableTypeDetail'
import styles from './index.module.scss'

const BackendStatistics = (props: any) => {
  const {year, quarter} = props
  const [loading, setLoading] = useState(false)
  const [statInfo, setStatInfo] = useState<IBackendSummary | null>(null)

  const getStat = async () => {
    setLoading(true)
    try {
      const params: any = {year}
      if (quarter) {
        params.quarter = quarter
      }
      const res = await queryBackendStat(params)
      if (res?.data && res?.data?.code === 0) {
        setStatInfo(res.data?.data)
      }
    } finally {
      setLoading(false)
    }
  }

  React.useEffect(() => {
    getStat()
  }, [year, quarter])

  const color = (statInfo?.tableGrowthRate ?? 0) >= 0 ? '#3f8600' : '#cf1322'

  return (
    <Spin spinning={loading}>
      <Flex className={styles.container}>
        <BackendProjectOverview statInfo={statInfo} color={color} />
        <TableTypeDetail statInfo={statInfo} />
      </Flex>

      <Card title="数据表详情" style={{marginTop: 20}}>
        <BackendTable dataSource={statInfo?.tables || []} />
      </Card>
    </Spin>
  )
}

export default BackendStatistics
