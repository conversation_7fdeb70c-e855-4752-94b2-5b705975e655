import {Flex} from '@astro/ui/ui'
import * as echarts from 'echarts'
import type React from 'react'
import {useEffect, useRef, useState} from 'react'
import type {EditorInfo} from '../type'
import {UserRole} from '../userInfo'
import styles from './index.module.scss'

export const getUserChineseName = (passport: string): string => {
  for (const [_, users] of Object.entries(UserRole)) {
    const matchedUser: any = users.find(u => Object.keys(u)[0] && passport.includes(Object.keys(u)[0]))
    if (matchedUser) {
      return matchedUser[Object.keys(matchedUser)[0]]
    }
  }
  return passport
}

interface TableTypeDetailProps {
  statInfo: any
}

interface TypeDetailProps {
  name: string
  value: number
  tables: any[]
}

const TypeDetail: React.FC<TypeDetailProps> = ({name, value, tables}) => {
  return (
    <div className={styles.detailContainer}>
      <h3 className={styles.header}>
        {name}: {value} 个表
      </h3>
      {tables.map(table => (
        <Flex wrap={'wrap'} gap={10} align="center" key={table.tableId} className={styles.projectCard}>
          <div className={styles.projectName}>{table.tableName}</div>

          <Flex gap={10} wrap={'wrap'} className={styles.userList}>
            {table.editors.map((editor: EditorInfo) => (
              <Flex key={editor.uid} className={styles.userTag}>
                <span className={styles.passport}>{getUserChineseName(editor.passport)}</span>
              </Flex>
            ))}
          </Flex>
        </Flex>
      ))}
    </div>
  )
}

const TableTypeDetail: React.FC<TableTypeDetailProps> = ({statInfo}) => {
  const [selectedType, setSelectedType] = useState<string | null>(null)
  const filteredTables =
    statInfo?.tables?.filter((table: any) => !selectedType || table.tableType === selectedType) || []

  const pieChartRef = useRef<HTMLDivElement>(null)
  const pieChartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!pieChartRef.current || !statInfo?.tables) return
    pieChartInstance.current = echarts.init(pieChartRef.current)
    // Calculate table type distribution
    const typeCount = statInfo.tables.reduce((acc: {[key: string]: number}, table: any) => {
      acc[table.tableType] = (acc[table.tableType] || 0) + 1
      return acc
    }, {})
    const pieData = Object.entries(typeCount)?.map(([type, count]) => ({
      name: type,
      value: count,
    }))
    const pieOption = {
      title: {
        text: '表类型分布',
        left: 'center',
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        left: 'left',
      },
      series: [
        {
          type: 'pie',
          // radius: '50%',
          radius: ['40%', '70%'],
          data: pieData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    }
    pieChartInstance.current?.setOption(pieOption)

    const handleResize = () => {
      pieChartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)
    const handleClick = (params: any) => {
      setSelectedType(params.name)
    }
    pieChartInstance.current?.on('click', handleClick)
    // Set default selection
    if (pieData.length > 0) {
      pieChartInstance.current?.dispatchAction({
        // type: 'highlight',
        type: 'select',
        seriesIndex: 0,
        dataIndex: 0,
      })
      setSelectedType(pieData[0].name)
    }
    return () => {
      pieChartInstance.current?.off('click', handleClick)
      pieChartInstance.current?.dispose()
      pieChartInstance.current = null
      window.removeEventListener('resize', handleResize)
    }
  }, [statInfo])

  return (
    <Flex className={styles.chartContainer}>
      <div className={styles.chart} ref={pieChartRef} />
      {selectedType && <TypeDetail name={selectedType} value={filteredTables.length} tables={filteredTables} />}
    </Flex>
  )
}

export default TableTypeDetail
