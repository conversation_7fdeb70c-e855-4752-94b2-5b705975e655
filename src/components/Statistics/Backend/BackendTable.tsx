import {FileWordOutlined} from '@astro/ui/icon'
import {Button, Table, Tag, Tooltip} from '@astro/ui/ui'
import type React from 'react'
import {statBackendTagsOptions} from 'src/config/project'
import type {EditorInfo} from '../type'
import {getUserChineseName} from './TableTypeDetail'

interface BackendTableProps {
  dataSource: any[]
}

const columns = [
  {
    title: '表名',
    dataIndex: 'tableName',
    key: 'tableName',
    // width: '300px',
  },
  {
    title: '表Key',
    dataIndex: 'tableKey',
    key: 'tableKey',
    width: '300px',
  },
  {
    title: '表类型',
    dataIndex: 'tableType',
    key: 'tableType',
    align: 'center',
    width: '100px',
    filters: statBackendTagsOptions.map(opt => ({
      text: opt.label,
      value: opt.value,
    })),
    onFilter: (value: string, record: any) => record.tableType === value,
    render: (type: string) => {
      const tagOption = statBackendTagsOptions.find(opt => opt.value === type)
      return (
        <Tag
          color={tagOption?.color ?? 'pink'}
          style={{textAlign: 'center', fontSize: '10px', display: 'inline-block'}}
        >
          {tagOption?.label ?? type}
        </Tag>
      )
    },
  },
  {
    title: '字段数量',
    dataIndex: 'fieldCount',
    key: 'fieldCount',
    align: 'center',
    width: '100px',
  },
  {
    title: '按钮数量',
    dataIndex: 'buttonCount',
    key: 'buttonCount',
    align: 'center',
    width: '100px',
  },
  {
    title: '需求文档',
    dataIndex: 'prdUrl',
    key: 'prdUrl',
    align: 'center',
    width: '100px',
    render: (_: any, record: any) => {
      return record?.prdUrl ? (
        <a href={record?.prdUrl} target="_blank" rel="noopener noreferrer">
          {/* <FileWordOutlined /> */}
          <span>{record?.prdName}</span>
        </a>
      ) : (
        '-'
      )
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    align: 'center',
  },
  {
    title: '参与人',
    key: 'edits',
    align: 'center',
    width: '100px',
    render: (_: any, record: any) => {
      const editors: EditorInfo[] = record.editors
      return (
        <ul style={{margin: 0, padding: 0, listStyle: 'none'}}>
          {editors?.map((editor, index) => (
            <li key={index}>
              {/* <Tooltip title={`最近编辑: ${editor.lastEditTime}`}>
                <span>{getUserChineseName(editor.passport)}</span>
              </Tooltip> */}
              <span>{getUserChineseName(editor.passport)}</span>
            </li>
          ))}
        </ul>
      )
    },
  },
]

const BackendTable: React.FC<BackendTableProps> = ({dataSource}) => (
  <Table
    size="small"
    columns={columns}
    dataSource={dataSource}
    rowKey="tableId"
    pagination={{
      defaultPageSize: 10,
      showSizeChanger: true,
      showTotal: total => `共 ${total} 条记录`,
    }}
  />
)

export default BackendTable
