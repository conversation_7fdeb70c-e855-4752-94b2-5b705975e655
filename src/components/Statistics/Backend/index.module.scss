.container {
  width: 100%;

  .overviewContainer {
    width: 40%;

    .overviewCard {
      height: 150px;
    }

    .chartLineContainer {
      height: 400px;
    }
  }

  .chartContainer {
    width: 60%;

    .chart {
      width: 60%;
      height: 400px;
      flex-shrink: 0;
    }


    .detailContainer {
      width: 40%;
      height: 560px;
      overflow-y: auto;
      padding: 0 16px;
      font-family: Arial, sans-serif;
      border: 1px solid rgba(161, 158, 158, 0.5);
      border-radius: 10px;
      @include scroll-bar(2px, #c1bfbf, 0.5, 0.8);

      .header {
        position: sticky;
        top: 0;
        height: 40px;
        line-height: 40px;
        margin: 0;
        font-size: 16px;
        color: #333;
        background-color: #fff;
      }

      .projectCard {
        background: #f2f2f2;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 12px;

        .projectName {
          font-weight: 600;
          color: #1a1a1a;
          cursor: pointer;
          flex-shrink: 0;
        }

        .userList {

          .userTag {
            background: #fff;
            padding: 4px 10px;
            border-radius: 16px;

            .avatar {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              margin-right: 6px;
            }
          }
        }

        .passport {
          color: #666;
          font-size: 14px;
        }
      }
    }
  }


}