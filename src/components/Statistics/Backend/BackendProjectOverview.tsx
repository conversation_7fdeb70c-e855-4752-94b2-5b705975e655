import {ArrowDownOutlined, ArrowUpOutlined} from '@astro/ui/icon'
import {Card, Divider, Flex, Statistic} from '@astro/ui/ui'
import * as echarts from 'echarts'
import type React from 'react'
import {useEffect, useRef} from 'react'
import CountUp from 'react-countup'
import {formatter} from '../ProjectOverview'
import styles from './index.module.scss'

interface BackendProjectOverviewProps {
  statInfo: any
  color: string
}

const BackendProjectOverview: React.FC<BackendProjectOverviewProps> = ({statInfo, color}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!chartRef.current || !statInfo) return
    chartInstance.current = echarts.init(chartRef.current)
    const option = {
      title: {
        text: '后台项目趋势',
      },
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['表数量', '按钮数量', '字段数量'],
      },
      xAxis: {
        type: 'category',
        data: [statInfo.previousPeriodDescription, statInfo.periodDescription],
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: '表数量',
          type: 'line',
          data: [statInfo.previousTableCount, statInfo.tableCount],
        },
        {
          name: '按钮数量',
          type: 'line',
          data: [statInfo.previousButtonCount, statInfo.buttonCount],
        },
        {
          name: '字段数量',
          type: 'line',
          data: [statInfo.previousFieldCount, statInfo.fieldCount],
        },
      ],
    }
    chartInstance.current.setOption(option)

    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)
    return () => {
      chartInstance.current?.dispose()
      window.removeEventListener('resize', handleResize)
    }
  }, [statInfo])

  return (
    <Flex vertical gap={40} className={styles.overviewContainer}>
      <Card className={styles.overviewCard}>
        <Statistic title="应用页面数（个）" value={statInfo?.tableCount} precision={0} formatter={formatter} />
        <Divider style={{margin: 10}} />
        <Flex>
          环比：
          <Flex style={{color: color}}>
            {statInfo?.tableGrowthRate >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
            <CountUp end={Math.abs(statInfo?.tableGrowthRate || 0)} decimals={2} duration={1.5} suffix="%" />
          </Flex>
        </Flex>
      </Card>

      <div ref={chartRef} className={styles.chartLineContainer} />
    </Flex>
  )
}

export default BackendProjectOverview
