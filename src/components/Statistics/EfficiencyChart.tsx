import * as echarts from 'echarts'
import type React from 'react'
import {useEffect, useRef} from 'react'
import type {ProjectStats} from './type'

interface EfficiencyChartProps {
  data: ProjectStats
}

const EfficiencyChart: React.FC<EfficiencyChartProps> = ({data}) => {
  const chartRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!chartRef.current) return

    const chart = echarts.init(chartRef.current)

    const filteredProjects =
      data.projectList?.filter(item => !item.statTags?.some((tag: any) => tag.includes('后台'))) || []

    const projectNames = filteredProjects.map(item => item.actName)
    const efficiencyData = filteredProjects.map(item => {
      const total = item.workDays + item.reduceDays
      return total ? +((item.reduceDays / total) * 100).toFixed(2) : 0
    })
    // 根据效率值范围返回对应的颜色
    const getColorByEfficiency = (value: number) => {
      if (value >= 90) return '#4a9c6d' // 深绿色 - 90%以上
      if (value >= 80) return '#5db17f' // 深绿色 - 80-90%
      if (value >= 70) return '#70c691' // 绿色 - 70-80%
      if (value >= 60) return '#83dba3' // 浅绿色 - 60-70%
      if (value >= 50) return '#96f0b5' // 浅绿色 - 50-60%
      if (value >= 40) return '#a9e5c7' // 浅绿色 - 40-50%
      if (value >= 30) return '#bcdad9' // 浅绿色 - 30-40%
      if (value >= 20) return '#e7f5e7' // 浅绿色 - 20-30%
      if (value >= 10) return '#e2e2fd' // 浅绿色 - 10-20%
      return '#f0f0f0' // 灰色 - 10%以下
    }

    const option = {
      // title: {
      //   text: '项目提效率明细',
      // },
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          const dataIndex = params[0].dataIndex
          const project = data?.projectList[dataIndex]
          const imagesHtml = project?.editorPassports
            ?.map(
              item =>
                `<img src="${item?.hdlogo || item?.yylogo}" style="width: 24px; height: 24px; border-radius: 50%; margin-right: 4px;" />`,
            )
            .join('')
          return `
          ${project.actName}<br/>
          预估总排期：${project.workDays + project.reduceDays} 人天<br />
          实际总排期：${project.workDays} 人天<br/>
          节省总人力：${project.reduceDays} 人天<br/>
          参与人员：${imagesHtml ?? '-'}
          `
        },
      },
      // legend: {
      //   data: ['提效率'],
      // },
      dataZoom: [
        {
          // type: 'inside',
          show: true,
          yAxisIndex: [0],
          start: 0,
          end: 100,
        },
      ],
      grid: {
        left: '0%',
        right: '10%',
        bottom: '3%',
        top: '3%',
        containLabel: true,
      },
      yAxis: {
        type: 'category',
        data: projectNames,
        axisLabel: {
          interval: 0,
          align: 'right',
        },
      },
      xAxis: {
        type: 'value',
        name: '提效率(%)',
      },
      series: [
        {
          name: '提效率',
          type: 'bar',
          data: efficiencyData?.map(value => ({
            value,
            itemStyle: {
              color: getColorByEfficiency(value),
            },
          })),
          label: {
            show: true,
            position: 'right',
            formatter: '{c}%',
          },
        },
      ],
    }

    chart.setOption(option)

    const handleResize = () => {
      chart.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      chart.dispose()
      window.removeEventListener('resize', handleResize)
    }
  }, [data])

  return <div ref={chartRef} style={{height: `calc(${data.projectList?.length} * 20px + 50px)`}} />
}

export default EfficiencyChart
