.tagChartContainer {
  width: 60%;

  .tagStatsList {
    position: absolute;
    left: 20px;
    bottom: 40px;
    @include flexCenter();
    flex-wrap: nowrap;
    justify-content: flex-start;

    margin: 24px auto 0;
    gap: 10px;
    // overflow-x: auto;
    @include scroll-bar(4px, #c1bfbf, 0.5, 0.8);


    .tagStatsCard {
      min-width: 90px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      font-weight: 500;
      border: 1.5px solid #e0e0e0;
      padding: 6px 9px;
      box-sizing: border-box;
      transition: box-shadow 0.2s;

      .tagStatsLabel {
        font-size: 14px;
        font-weight: 700;
        margin-bottom: 4px;
        @include text-overflow();
      }

      .tagStatsCount {
        font-size: 12px;
        margin-bottom: 2px;
      }

      .tagStatsPercent {
        font-size: 12px;
      }
    }

  }
}