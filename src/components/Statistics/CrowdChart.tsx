import {Ava<PERSON>, Card, Flex, List} from '@astro/ui/ui'
import {isTest} from '@astro/utils/env'
// 人群配置占比饼图组件
import * as echarts from 'echarts'
import type React from 'react'
import {useEffect, useRef, useState} from 'react'
import styles from './crowdChart.module.scss'
import detailStyles from './detail.module.scss'
import type {ProjectDetail, ProjectStats} from './type'
import {UserRole} from './userInfo'
interface CrowdChartProps {
  data: ProjectStats
}

interface DetailProps {
  name: string
  value: number
  projects: (ProjectDetail & {roleUsers: any})[]
}

const Detail: React.FC<DetailProps> = ({name, value, projects}) => {
  return (
    <div className={styles.detailContainer}>
      <h3 className={styles.header}>
        {name}: {value} 个项目
      </h3>
      {projects.map(project => (
        <Flex wrap={'wrap'} gap={10} align="center" key={project.id} className={styles.projectCard}>
          <div
            className={styles.projectName}
            onClick={() => {
              window.open(`https://astro${isTest() ? '-test' : ''}.yy.com/projects/playload/${project.id}`, '_blank')
            }}
          >
            {project.actName}
          </div>

          <Flex gap={10} wrap={'wrap'} className={styles.userList}>
            {project.editorPassports.map((user: any) => (
              <Flex key={user.passport} className={styles.userTag}>
                <img src={user.yylogo} className={styles.avatar} />
                <span className={styles.passport}>
                  {Object.entries(UserRole).reduce((name, [role, users]) => {
                    const matchedUser: any = users.find(
                      u => Object.keys(u)[0] && user.passport.includes(Object.keys(u)[0]),
                    )
                    return matchedUser ? matchedUser[Object.keys(matchedUser)[0]] : name
                  }, user.passport)}
                </span>
              </Flex>
            ))}
          </Flex>
        </Flex>
      ))}
    </div>
  )
}

const CrowdChart: React.FC<CrowdChartProps> = ({data}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const currentDataIndex = useRef<number>(-1)
  const [detailData, setDetailData] = useState<DetailProps | any>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!chartRef.current) return

    chartInstance.current = echarts.init(chartRef.current)

    // 统计各角色参与的项目
    const projectStats = data.projectList?.reduce(
      (acc, project) => {
        // 记录已匹配的 passport
        const matchedPassports = new Set<string>()

        // 遍历所有角色进行匹配
        Object.entries(UserRole).forEach(([role, passports]) => {
          const roleUsers = project?.editorPassports?.filter(user => {
            return passports.some(passportObj => {
              const passportKey = Object.keys(passportObj)[0]
              return passportKey && user.passport.includes(passportKey)
            })
          })
          if (roleUsers?.length > 0) {
            if (!acc[role]) {
              acc[role] = {
                name: `${role}参与项目`,
                value: 0,
                projects: [],
              }
            }
            acc[role].projects.push({
              ...project,
              roleUsers,
            })
            roleUsers.forEach(user => matchedPassports.add(user.passport))
          }
        })

        // 统计未匹配任何角色的用户
        const otherUsers = project?.editorPassports?.filter(user => !matchedPassports.has(user.passport))
        if (otherUsers?.length > 0) {
          if (!acc.Other) {
            acc.Other = {
              name: '其他参与者项目',
              value: 0,
              projects: [],
            }
          }
          acc?.Other?.projects.push({
            ...project,
            roleUsers: otherUsers,
          })
        }

        return acc
      },
      {} as Record<string, {name: string; value: number; projects: any[]}>,
    )

    const pieData = Object.entries(projectStats ?? [])?.map(([role, stat]) => ({
      ...stat,
      value: stat?.projects?.length,
    }))

    const option = {
      title: {
        text: '人群配置项目占比',
        left: 'left',
      },
      tooltip: {
        show: false,
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: true,
          label: {
            show: true,
            formatter: '{b}: {d}%',
          },
          labelLine: {
            show: true,
          },
          data: pieData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          selectedMode: 'single',
          select: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    }

    chartInstance.current.setOption(option)

    // Set default selected slice
    chartInstance.current.dispatchAction({
      type: 'select',
      dataIndex: 0,
    })
    currentDataIndex.current = 0
    setDetailData(pieData[0])

    chartInstance.current.on('click', params => {
      currentDataIndex.current = params.dataIndex
      setDetailData(params?.data)

      // // 先取消所有选中状态
      // if (currentDataIndex.current === params.dataIndex) {
      //   currentDataIndex.current = -1
      //   setDetailData(null)
      //   chartInstance.current?.dispatchAction({
      //     type: 'unselect',
      //     dataIndex: params.dataIndex,
      //   })
      //   return
      // } else {
      //   currentDataIndex.current = params.dataIndex
      //   setDetailData(params.data)
      // }
    })

    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      chartInstance.current?.dispose()
      window.removeEventListener('resize', handleResize)
    }
  }, [data])

  return (
    <Flex gap={20} justify="center" className={styles.container}>
      <div ref={chartRef} className={styles.chartContainer} />
      {detailData && <Detail {...detailData} />}
    </Flex>
  )
}

export default CrowdChart
