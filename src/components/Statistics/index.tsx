import {Card, DatePicker, Flex, Select, Spin} from '@astro/ui/ui'
import dayjs from 'dayjs'
import React, {useState, useEffect} from 'react'
import {queryProjectStat} from 'src/services/material'
import BackendStatistics from './Backend'
import CrowdChart from './CrowdChart_V2'
import DetailedTable from './DetailedTable'
import EfficiencyChart from './EfficiencyChart'
import ProjectOverview from './ProjectOverview'
import ProjectTagChart from './ProjectTagChart'
import styles from './index.module.scss'
import {mockData} from './mockData'
import type {ProjectDetail, ProjectStats} from './type'

// 季度选项
const quarterOptions = [
  {value: '', label: '全年'},
  {value: '1', label: '第一季度'},
  {value: '2', label: '第二季度'},
  {value: '3', label: '第三季度'},
  {value: '4', label: '第四季度'},
]

const Statistics = () => {
  const getCurrentQuarter = () => {
    const currentMonth = new Date().getMonth() + 1
    return Math.ceil(currentMonth / 3).toString()
  }

  const [year, setYear] = useState<string>(new Date().getFullYear().toString())
  const [quarter, setQuarter] = useState<string>(getCurrentQuarter())
  const [statInfo, setStatInfo] = useState<ProjectStats>()
  const [loading, setLoading] = useState(false)
  const [totalComponents, setTotalComponents] = useState<number>(0)
  const [totalReusedComponents, setTotalReusedComponents] = useState<number>(0)

  const getStat = async () => {
    setLoading(true)
    try {
      const params: any = {year}
      if (quarter) {
        params.quarter = quarter
      }
      const res = await queryProjectStat(params)
      // const res: any = mockData // 测试数据
      setStatInfo(res.data)

      const total =
        res.data.projectList?.reduce((sum: number, project: ProjectDetail) => sum + project.componentCount, 0) || 0
      const reused =
        res.data.projectList?.reduce((sum: number, project: ProjectDetail) => sum + project.reusedComponentCount, 0) ||
        0
      setTotalComponents(total)
      setTotalReusedComponents(reused)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getStat()
  }, [year, quarter])

  const handleYearChange = (date: any) => {
    if (date) {
      setYear(date.year().toString())
    }
  }

  return (
    <div className={styles.container}>
      <Spin spinning={loading}>
        <div style={{marginBottom: '24px'}}>
          <DatePicker
            picker="year"
            value={year ? dayjs(year) : null}
            onChange={handleYearChange}
            style={{width: 120, marginRight: 16}}
          />
          <Select
            style={{width: 120}}
            value={quarter}
            onChange={(value: any) => setQuarter(value)}
            options={quarterOptions}
            placeholder="请选择季度"
            // allowClear
          />
        </div>

        <Flex gap={16} vertical>
          {statInfo && (
            <>
              {/* 项目概览 */}
              <ProjectOverview
                statInfo={statInfo}
                totalComponents={totalComponents}
                totalReusedComponents={totalReusedComponents}
                quarter={quarter}
              />

              <Card>
                <Flex justify="center" style={{maxWidth: '1800px'}}>
                  {/* 项目分类提效率 */}
                  <ProjectTagChart data={statInfo} />

                  {/* 人群配置项目占比 */}
                  <CrowdChart data={statInfo} />
                </Flex>
              </Card>

              <Card title="项目统计明细">
                <DetailedTable data={statInfo} />
              </Card>

              <Card title="项目提效率明细">
                <EfficiencyChart data={statInfo} />
              </Card>

              <Card title="后台项目统计">
                <BackendStatistics year={year} quarter={quarter} />
              </Card>
            </>
          )}
        </Flex>
      </Spin>
    </div>
  )
}

export default Statistics
