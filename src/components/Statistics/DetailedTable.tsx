import {FileWordOutlined} from '@astro/ui/icon'
import {Ava<PERSON>, Button, Card, Flex, Table, Tag, Tooltip} from '@astro/ui/ui'
import {isTest} from '@astro/utils/env'
import dayjs from 'dayjs'
import {statTagsOptions} from 'src/config/project'
import type {ProjectDetail, ProjectStats, UserInfo} from './type'
import {UserRole} from './userInfo'
import './detailedTable.scss'

interface DetailedTableProps {
  data: ProjectStats
}

const DetailedTable: React.FC<DetailedTableProps> = ({data}) => {
  // 计算总节省人天和组件复用统计
  const calculateStats = () => {
    let totalReusedComponents = 0
    let totalComponents = 0

    data.projectList?.forEach(project => {
      totalReusedComponents += project.reusedComponentCount
      totalComponents += project.componentCount
    })

    return {totalReusedComponents, totalComponents}
  }

  const {totalReusedComponents, totalComponents} = calculateStats()

  // 获取用户角色描述和中文名
  const getUserRoleDesc = (passport: string): {role: string; name: string} => {
    for (const [role, users] of Object.entries(UserRole)) {
      for (const user of users as Array<Record<string, string>>) {
        const [key] = Object.keys(user)
        if (passport.includes(key)) {
          return {role, name: user[key]}
        }
      }
    }
    return {role: 'Other', name: passport}
  }
  const columns: any = [
    {
      title: '类型',
      dataIndex: 'statTags',
      key: 'statTags',
      align: 'center',
      width: 120,
      filters: statTagsOptions.map(opt => ({
        text: opt.label,
        value: opt.value,
      })),
      onFilter: (value: string, record: any) => record.statTags?.includes(value),
      render: (tags: string[]) => (
        <>
          {tags?.map(tag => {
            const tagOption = statTagsOptions.find(opt => opt.value === tag)
            return (
              <Tag
                color={tagOption?.color ?? 'pink'}
                key={tag}
                style={{textAlign: 'center', fontSize: '10px', display: 'inline-block'}}
              >
                {tagOption?.label ?? tag}
              </Tag>
            )
          })}
        </>
      ),
    },
    {
      title: '配置项目',
      dataIndex: 'id',
      key: 'id',
      align: 'left',
      width: 200,
      render: (id: number, record: any) => (
        <a
          href={`https://astro${isTest() ? '-test' : ''}.yy.com/projects/playload/${id}`}
          target="_blank"
          rel="noopener noreferrer"
        >
          {record.actName}
        </a>
      ),
    },
    {
      title: '需求文档',
      dataIndex: 'actDocumentUrl',
      key: 'actDocumentUrl',
      align: 'center',
      width: 80,
      render: (url: string) =>
        url ? (
          <a href={url} target="_blank" rel="noopener noreferrer">
            <FileWordOutlined />
          </a>
        ) : (
          '-'
        ),
    },
    {
      title: '设计稿',
      dataIndex: 'uiUrl',
      key: 'uiUrl',
      align: 'center',
      width: 80,
      render: (url: string) =>
        url ? (
          <a href={url} target="_blank" rel="noopener noreferrer">
            <FileWordOutlined />
          </a>
        ) : (
          '-'
        ),
    },
    // {
    //   title: '是否上线',
    //   dataIndex: 'isOnline',
    //   key: 'isOnline',
    //   align: 'center',
    //   width: 80,
    //   render: (isOnline: boolean) => (
    //     <Tag color={isOnline ? '#4CAF50' : 'pink'} style={{fontSize: '10px', display: 'inline-block'}}>
    //       {isOnline ? '已上线' : '未上线'}
    //     </Tag>
    //   ),
    // },
    {
      title: '预估人天',
      key: 'estimatedDays',
      width: 80,
      align: 'center',
      render: (_: any, record: ProjectDetail) => record.workDays + record.reduceDays,
    },
    {
      title: '实际人天',
      dataIndex: 'workDays',
      key: 'workDays',
      align: 'center',
      width: 80,
    },
    {
      title: '节省人天',
      dataIndex: 'reduceDays',
      key: 'reduceDays',
      align: 'center',
      width: 80,
    },
    {
      title: '总提效',
      key: 'efficiency',
      width: 80,
      align: 'center',
      render: (_: any, record: ProjectDetail) => {
        const total = record.workDays + record.reduceDays
        return total > 0 ? `${((record.reduceDays / total) * 100).toFixed(2)}%` : '0%'
      },
    },
    {
      title: '复用组件数',
      dataIndex: 'reusedComponentCount',
      key: 'reusedComponentCount',
      align: 'center',
      width: 80,
    },
    {
      title: '组件总数',
      dataIndex: 'componentCount',
      key: 'componentCount',
      align: 'center',
      width: 80,
    },
    {
      title: '组件复用率',
      key: 'componentReuseRate',
      align: 'center',
      width: 80,
      render: (_: any, record: ProjectDetail) => {
        return record.componentCount > 0
          ? `${((record.reusedComponentCount / record.componentCount) * 100).toFixed(2)}%`
          : '0%'
      },
    },
    {
      title: '参与人',
      dataIndex: 'editorPassports',
      key: 'editorPassports',
      width: 150,
      align: 'center',
      render: (users: UserInfo[]) => (
        <Avatar.Group maxCount={10}>
          {users?.map((user: any) => {
            const roleDesc = getUserRoleDesc(user.passport)
            return (
              <Tooltip key={user.passport} title={`${roleDesc.name}(${roleDesc.role})`}>
                <Avatar src={user.yylogo} size={'small'} />
              </Tooltip>
            )
          })}
        </Avatar.Group>
      ),
    },
    {
      title: '上线时间',
      dataIndex: 'onlineTime',
      key: 'onlineTime',
      align: 'center',
      width: 80,
      render: (time: number) => (time ? dayjs(time).format('YYYY-MM-DD') : '-'),
    },
  ]

  return (
    <Table
      className="detailedTable"
      columns={columns}
      dataSource={data.projectList}
      rowKey="id"
      scroll={{x: 1500, y: 500}}
      pagination={false}
      size="small"
      sticky
    />
  )
}

export default DetailedTable
