import {Flex} from '@astro/ui/ui'
// 项目类型提效柱状图组件
import * as echarts from 'echarts'
import type React from 'react'
import {useEffect, useRef, useState} from 'react'
import {statTagsOptions} from 'src/config/project'
import styles from './projectTagChart.module.scss'
import type {ProjectStats} from './type'

interface ProjectTagChartProps {
  data: ProjectStats
}

interface TagInfo {
  total: number
  count: number
  reduceDays: number
  workDays: number
}

interface TagStatsListProps {
  allTags: string[]
  tagStats: Record<string, TagInfo>
  totalProjects: number
}

const TagStatsList: React.FC<TagStatsListProps> = ({allTags, tagStats, totalProjects}) => {
  return (
    <Flex
      wrap={'wrap'}
      justify={'flex-start'}
      gap={10}
      style={{overflowX: 'auto', width: '100%', paddingBottom: 8}}
      className={styles.tagStatsList}
    >
      {allTags.map((tag, index) => {
        const count = tagStats[tag]?.count || 0
        const percent = totalProjects > 0 ? ((count / totalProjects) * 100).toFixed(1) : '0.00'
        const tagOption = statTagsOptions.find(opt => opt.value === tag)
        const tagLabel = tagOption?.label || tag
        const color = tagOption?.color || '#888'
        return (
          <Flex
            vertical
            wrap={'wrap'}
            align={'center'}
            key={index}
            className={styles.tagStatsCard}
            style={{border: `1.5px solid ${color}`, color}}
          >
            <div className={styles.tagStatsLabel}>{tagLabel}</div>
            <div className={styles.tagStatsCount}>数量：{count}个 </div>
            <div className={styles.tagStatsPercent}>占比：{percent}%</div>
          </Flex>
        )
      })}
    </Flex>
  )
}

const ProjectTagChart: React.FC<ProjectTagChartProps> = ({data}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)
  const [allTags, setAllTags] = useState<string[]>([])
  const [tagStats, setTagStats] = useState<Record<string, TagInfo>>({})
  const totalProjects = data.projectList?.length || 0

  useEffect(() => {
    if (!chartRef.current) return

    chartInstance.current = echarts.init(chartRef.current)

    const tagStatsTemp = statTagsOptions.reduce(
      (acc, {value}) => {
        acc[value] = {
          total: 0,
          count: 0,
          reduceDays: 0,
          workDays: 0,
        }
        return acc
      },
      {} as Record<string, TagInfo>,
    )

    data.projectList?.forEach(project => {
      // 补充自定义标签分类的数据
      project.statTags?.forEach(tag => {
        if (!tagStatsTemp[tag]) {
          tagStatsTemp[tag] = {
            total: 0,
            count: 0,
            reduceDays: 0,
            workDays: 0,
          }
        }
        tagStatsTemp[tag].count++
        tagStatsTemp[tag].reduceDays += project.reduceDays
        tagStatsTemp[tag].workDays += project.workDays
      })
    })

    const presetTags = statTagsOptions.map(option => option.value)
    const customTags = Object.keys(tagStatsTemp).filter(tag => !presetTags.includes(tag))
    const allTagsTemp = [...presetTags, ...customTags]
    setAllTags(allTagsTemp)
    setTagStats(tagStatsTemp)

    const option = {
      title: {
        text: '项目分类提效率',
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          const tag = params[0].name
          const stats = tagStatsTemp[tag]
          return `${tag}<br/>预估总排期：${(stats.workDays + stats.reduceDays).toFixed(2)} 人天<br/>实际总排期：${stats.workDays.toFixed(2)} 人天<br/>节省总人天：${stats.reduceDays.toFixed(2)} 人天<br/>提效率：${params[0].value}%`
        },
      },
      xAxis: {
        type: 'category',
        data: allTagsTemp,
        axisLabel: {
          interval: 0,
          rotate: 30,
        },
      },
      yAxis: {
        type: 'value',
        name: '提效率(%)',
      },
      series: [
        {
          type: 'bar',
          data: allTagsTemp.map(tag => ({
            name: tag,
            value: Number(
              (tagStatsTemp[tag].reduceDays / (tagStatsTemp[tag].reduceDays + tagStatsTemp[tag].workDays)) * 100,
            ).toFixed(2),
            itemStyle: {
              color: statTagsOptions.find(opt => opt.value === tag)?.color ?? '#D3D3D3',
            },
          })),
          label: {
            show: true,
            position: 'top',
            formatter: '{c}%',
          },
        },
      ],
    }

    chartInstance.current.setOption(option)

    // 监听窗口resize
    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      chartInstance.current?.dispose()
      window.removeEventListener('resize', handleResize)
    }
  }, [data])

  return (
    <Flex vertical className={styles.tagChartContainer}>
      <div ref={chartRef} style={{height: '400px'}} />
      <TagStatsList allTags={allTags} tagStats={tagStats} totalProjects={totalProjects} />
    </Flex>
  )
}

export default ProjectTagChart
