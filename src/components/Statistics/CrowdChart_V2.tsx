import {Avatar, Card, Flex, List, Tag} from '@astro/ui/ui'
import {isTest} from '@astro/utils/env'
// 人群配置占比饼图组件
import * as echarts from 'echarts'
import type React from 'react'
import {useEffect, useRef, useState} from 'react'
import {label} from '../common/F2CConfigIdContent/index.module.scss'
import styles from './crowdChart.module.scss'
import detailStyles from './detail.module.scss'
import type {ProjectDetail, ProjectStats} from './type'
import {COLOR_THEMES, UserRole} from './userInfo'
interface CrowdChartProps {
  data: ProjectStats
}

interface DetailProps {
  data: ProjectStats
  name: string
  value: number
  projects: (ProjectDetail & {roleUsers: any})[]
}

const Detail: React.FC<DetailProps> = ({data, name, value, projects}) => {
  const color = COLOR_THEMES[name as keyof typeof COLOR_THEMES]?.primary
  return (
    <div className={styles.detailContainer}>
      <Flex className={styles.header} gap={10} wrap={'wrap'}>
        <span>{name}</span>
        <span>
          参与项目 <Tag color={color}>{value} 个</Tag>
        </span>
        <span>
          占比 <Tag color={`green`}>{((value / data.projectList.length) * 100).toFixed(1)}%</Tag>
        </span>
      </Flex>
      {projects.map(project => (
        <Flex wrap={'wrap'} gap={10} align="center" key={project.id} className={styles.projectCard}>
          <div
            className={styles.projectName}
            onClick={() => {
              window.open(`https://astro${isTest() ? '-test' : ''}.yy.com/projects/playload/${project.id}`, '_blank')
            }}
          >
            {project.actName}
          </div>

          <Flex gap={10} wrap={'wrap'} className={styles.userList}>
            {project.editorPassports.map((user: any) => (
              <Flex key={user.passport} className={styles.userTag}>
                <img src={user.yylogo} className={styles.avatar} />
                <span className={styles.passport}>
                  {Object.entries(UserRole).reduce((name, [role, users]) => {
                    const matchedUser: any = users.find(
                      u => Object.keys(u)[0] && user.passport.includes(Object.keys(u)[0]),
                    )
                    return matchedUser ? matchedUser[Object.keys(matchedUser)[0]] : name
                  }, user.passport)}
                </span>
              </Flex>
            ))}
          </Flex>
        </Flex>
      ))}
    </div>
  )
}

const CrowdChart: React.FC<CrowdChartProps> = ({data}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const [detailData, setDetailData] = useState<DetailProps | null>(null)
  const currentDataIndex = useRef<number>(-1)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!chartRef.current || !data.projectList) return

    chartInstance.current = echarts.init(chartRef.current)

    const projectStats = data.projectList.reduce(
      (acc, project) => {
        const matchedPassports = new Set<string>()

        Object.entries(UserRole).forEach(([role, passports]) => {
          const roleUsers = project?.editorPassports?.filter(user => {
            return passports.some(passportObj => {
              const passportKey = Object.keys(passportObj)[0]
              return passportKey && user.passport.includes(passportKey)
            })
          })
          if (roleUsers?.length > 0) {
            if (!acc[role]) {
              acc[role] = {
                name: `${role}参与项目`,
                value: 0,
                projects: [],
              }
            }
            acc[role].projects.push({
              ...project,
              roleUsers,
            })
            roleUsers.forEach(user => matchedPassports.add(user.passport))
          }
        })

        const otherUsers = project?.editorPassports?.filter(user => !matchedPassports.has(user.passport))
        if (otherUsers?.length > 0) {
          if (!acc.Other) {
            acc.Other = {
              name: '其他参与者项目',
              value: 0,
              projects: [],
            }
          }
          acc?.Other?.projects.push({
            ...project,
            roleUsers: otherUsers,
          })
        }

        return acc
      },
      {} as Record<string, {name: string; value: number; projects: any[]}>,
    )

    // 找出项目数量最多的角色
    const maxRole = Object.entries(projectStats).reduce((max, [role, stat]) => {
      if (!max || stat.projects.length > projectStats[max].projects.length) {
        return role
      }
      return max
    }, '')

    // 设置默认选中的详情数据
    if (maxRole) {
      setDetailData({
        data,
        name: maxRole,
        value: projectStats[maxRole].projects.length,
        projects: projectStats[maxRole].projects,
      })
    }

    const totalProjects = data.projectList.length || 1
    const chart = echarts.init(chartRef.current)

    const series = Object.entries(projectStats).flatMap(([role, stat], index) => {
      const theme = COLOR_THEMES[role as keyof typeof COLOR_THEMES] || COLOR_THEMES.Other
      // Adjust radius calculation to accommodate more circles
      const totalRoles = Object.keys(projectStats).length
      const radiusStep = Math.min(15, 75 / totalRoles) // Dynamic step size based on number of roles
      const radiusStart = 90 - index * radiusStep
      const radiusEnd = radiusStart - radiusStep + 5 // Add small gap between rings
      const isSelected = currentDataIndex.current === index
      const percentage = (stat.projects.length / totalProjects) * 100

      // 创建底色环
      const backgroundSeries = {
        type: 'pie',
        radius: [`${radiusStart}%`, `${radiusEnd}%`],
        silent: true,
        animation: false,
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            value: 1,
            itemStyle: {
              color: theme.secondary,
              opacity: 0.1,
            },
          },
        ],
      }

      // 主数据环
      const dataSeries = {
        name: role,
        type: 'pie',
        radius: [`${radiusStart}%`, `${radiusEnd}%`],
        avoidLabelOverlap: true,
        // labelLine: {
        //   show: true,
        //   length: 20, // 第一段引导线长度
        //   length2: 40, // 第二段引导线长度
        //   minTurnAngle: 90, // 确保引导线有一个清晰的转折点
        //   smooth: 0.2, // 轻微的曲线效果
        //   lineStyle: {
        //     width: 1.5,
        //     type: 'solid',
        //     color: theme.primary,
        //   },
        // },
        label: {
          show: true,
          position: 'outside',
          alignTo: 'edge',
          edgeDistance: '5%', // 文字和边缘的距离
          formatter: (params: any) => {
            if (!params.name) return ''
            return [`{name|${role}}: {value|${percentage.toFixed(1)}%}`].join('\n')
          },
          color: theme.primary,
          rich: {
            name: {
              fontSize: 12,
              fontWeight: 'bold',
              lineHeight: 12,
              color: theme.primary,
            },
            value: {
              fontSize: 12,
              lineHeight: 12,
              color: theme.primary,
            },
          },
          minMargin: 0, // 标签之间的最小间距
          distanceToLabelLine: 5, // 文字和引导线之间的距离
        },
        emphasis: {
          scale: true,
          scaleSize: 1.2,
          itemStyle: {
            shadowBlur: 20,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        data: [
          {
            value: stat.projects.length,
            name: role,
            itemStyle: {
              color: theme.primary,
              opacity: isSelected ? 1 : 0.8,
              shadowBlur: isSelected ? 10 : 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
            },
          },

          // 占位使用
          {
            value: totalProjects - stat.projects.length,
            name: '', // 移除'其他'文案
            label: {show: false},
            labelline: {show: false},
            itemStyle: {
              color: 'transparent',
            },
            tooltip: {
              show: false, // 禁用hover提示
            },
            emphasis: {
              disabled: true, // 禁用点击高亮
            },
          },
        ],
      }

      return [backgroundSeries, dataSeries]
    })

    const option = {
      title: {
        text: '人群配置项目占比',
        left: 'left',
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} 个项目 ({d}%)',
      },
      series,
    }

    chart.setOption(option)
    chart.on('click', params => {
      if (!params.name) return // 点击空名称（原其他）时不响应
      const roleData = projectStats[params.name]
      setDetailData({
        data,
        name: params.name,
        value: roleData.projects.length,
        projects: roleData.projects,
      })
    })

    chartInstance.current?.on('click', params => {
      if (!params.name) return // 点击空名称（原其他）时不响应
      currentDataIndex.current = params.dataIndex
      const roleData = projectStats[params.name]
      setDetailData({
        data,
        name: params.name,
        value: roleData.projects.length,
        projects: roleData.projects,
      })
    })

    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      chartInstance.current?.dispose()
      window.removeEventListener('resize', handleResize)
    }
  }, [data])

  return (
    <Flex gap={20} className={styles.container}>
      <div
        ref={chartRef}
        className={styles.chart}
        style={{
          width: '600px',
          height: '400px',
        }}
      />
      {data.projectList?.length > 0 && detailData ? (
        <Detail {...detailData} data={data} />
      ) : (
        <Flex justify={'center'} align={'center'}>
          暂无数据
        </Flex>
      )}
    </Flex>
  )
}

export default CrowdChart
