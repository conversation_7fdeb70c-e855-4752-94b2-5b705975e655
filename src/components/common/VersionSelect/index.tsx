import { RedoOutlined } from '@astro/ui/icon'
import { Button, Flex, Form, Popconfirm, Select, Spin, Tooltip, message } from '@astro/ui/ui'
import { useCallback, useEffect, useRef, useState } from 'react'
import pageStore from 'src/components/playload/editPanel/pageStore'
import type { Component, Page } from 'src/components/playload/editPanel/types'
import { getDeployVersionByName, getEMPJsonByNameAndVersion } from 'src/services/unpkg'
import { getPkgVersionByUrl, pkgNameParse } from 'src/utils'

export const getScopeInfo = async (pkgName: string, version: string) => {
  const newScopeData = await getEMPJsonByNameAndVersion(pkgName, version)
  return newScopeData
  //?.data?.id
}

export const checkPathNameExist = (empData: Record<string, any>, pathName: string) => {
  console.log(`[version] checkPathNameExist`, empData, pathName)
  let rs = false
  if (empData?.data?.exposes) {
    empData.data.exposes.forEach((item: any) => {
      if (item.path === pathName) {
        rs = true
      }
    })
  }
  return rs
}

interface VersionSelectProps {
  currentSelectedComponent?: Component | Page
  onVersionChange?: (component: Component | Page) => Promise<void>
  showSubmitButton?: boolean // 是否显示提交按钮
  onVersionSelect?: (version: string) => Promise<void> // 选择版本但不提交时的回调
  selectedVersion?: string // 外部传入的已选版本
}

const VersionSelect = ({
  currentSelectedComponent,
  onVersionChange,
  showSubmitButton = true,
  onVersionSelect,
  selectedVersion,
}: VersionSelectProps) => {
  const formRef = useRef<any>(null)
  const [versionList, setVersionList] = useState<{ value: string; label: string }[]>([])
  const [pkgName, setPkgName] = useState<string>('')
  const [componentPath, setComponentPath] = useState<string>('')
  const [currentVersion, setCurrentVersion] = useState<string>('')
  const [spinning, setSpinning] = useState(false)
  const resetFlag = useRef(false)

  const updateComponentVersion = useCallback(
    async (version: string, newScope: string) => {
      if (currentSelectedComponent) {
        const component = pageStore.clone(currentSelectedComponent)
        const oldVersion = getPkgVersionByUrl(component.url || '')
        component.scope = newScope
        component.url = component.url?.replace(oldVersion, version)

        if ((currentSelectedComponent as Page)?.pageKey) {
          await pageStore.editPage(pageStore.pageKey, component, pageStore.pageKey, true)
        } else {
          pageStore.updateComponent(component)
          await pageStore.getComponentForm(component, resetFlag.current)
        }
        // 如果提供了回调函数，则调用它
        if (onVersionChange) {
          await onVersionChange(component)
        }
      }
    },
    [currentSelectedComponent, onVersionChange],
  )

  const getVersionListByUrl = useCallback(
    async (queryPkgName?: string) => {
      const versionList = await getDeployVersionByName(queryPkgName ?? pkgName)
      if (versionList?.data?.versions) {
        const rs_list = versionList?.data?.versions
        console.log(`[version] versionList:`, rs_list)
        setVersionList(
          rs_list.reverse().map((item: string) => {
            return {
              label: item,
              value: item,
            }
          }),
        )
      }
    },
    [pkgName],
  )

  const handleFinish = useCallback(
    async (value: any) => {
      setSpinning(true)
      const empData = await getScopeInfo(pkgName, value.version)
      if (checkPathNameExist(empData, componentPath)) {
        await updateComponentVersion(value.version, empData?.data?.id)
        message.success(`已切换到版本:${value.version}`)
      } else {
        message.error(`${value.version}版本不存在${componentPath}的组件`)
      }
      setTimeout(() => {
        setSpinning(false)
      }, 1800)
    },
    [pkgName, componentPath, updateComponentVersion],
  )

  const setComponentInfo = (componentInfo: Page | Component) => {
    const pkgName = pkgNameParse(componentInfo.url || '')
    setPkgName(pkgName)
    getVersionListByUrl(pkgName)
    setComponentPath(componentInfo?.path ?? '')
  }

  useEffect(() => {
    if (currentSelectedComponent) {
      console.log('[version] currentSelectedComponent', pageStore.clone(currentSelectedComponent))
      setComponentInfo(currentSelectedComponent)
    }
  }, [currentSelectedComponent])

  useEffect(() => {
    if (formRef.current && versionList.length > 0) {
      const version = selectedVersion || getInitVersion()
      formRef.current.setFieldValue('version', version)
      setCurrentVersion(version)
    }
  }, [versionList, selectedVersion])

  const getInitVersion = useCallback(() => {
    if (currentSelectedComponent) {
      return getPkgVersionByUrl(currentSelectedComponent.url || '')
    }
    return ''
  }, [currentSelectedComponent])

  const handleVersionChange = (value: string) => {
    setCurrentVersion(value)
    if (onVersionSelect) {
      onVersionSelect(value)
    }
  }

  return (
    <>
      <Form
        initialValues={{ version: selectedVersion || getInitVersion() }}
        ref={formRef}
        onFinish={async (value: any) => {
          handleFinish(value)
        }}
      >
        <Flex wrap gap="small" align="center">
          {showSubmitButton && (
            <Tooltip title="所属包 / 路径">
              <div style={{ fontSize: '12px' }}>
                <span>{pkgName}</span>
                <span style={{ color: '#1677ff' }}>/</span>
                <span>{componentPath.replace('./', '')}</span>
              </div>
            </Tooltip>
          )}
          <Form.Item name="version" label={'更改版本'} style={{ minWidth: '80px', marginBottom: 0 }}>
            <Select options={versionList} style={{ width: 150 }} onChange={handleVersionChange} />
          </Form.Item>
          {showSubmitButton ? (
            <Popconfirm
              title="是否重置组件配置"
              description="更新版本后要重置当前组件配置吗?"
              onConfirm={() => {
                resetFlag.current = false
                formRef.current.submit()
              }}
              onCancel={() => {
                resetFlag.current = true
                formRef.current.submit()
              }}
              cancelText="重置"
              okText="保留当前配置"
            >
              <Button type="primary" htmlType="button">
                确定
              </Button>
            </Popconfirm>
          ) : null}
          <Tooltip title="更新版本列表">
            <Button
              type="default"
              htmlType="button"
              onClick={() => {
                getVersionListByUrl()
              }}
            >
              <RedoOutlined />
            </Button>
          </Tooltip>
        </Flex>
      </Form>
      <Spin spinning={spinning} fullscreen />
    </>
  )
}

export default VersionSelect
