import {ProFormUploadButton} from '@astro/ui/pro'
import {useEffect, useState} from 'react'
import UploadRequest, {type IUploadData} from 'src/utils/uploadRequest'
import styles from './index.module.scss'
const getFileListValue = (props: any) => (props.value ? [{uid: props.id, name: props.id, url: props.value}] : [])
const SformUpload = (props: any) => {
  const defaultValue = getFileListValue(props)
  const [fileList, setFileList] = useState(defaultValue)
  useEffect(() => {
    setFileList(getFileListValue(props))
  }, [props.value])
  return (
    <ProFormUploadButton
      title={props.label}
      fileList={fileList}
      fieldProps={{
        rootClassName: styles.fUpload,
        customRequest: async ({onProgress, onSuccess, onError, file}: any) => {
          try {
            const res: IUploadData = await UploadRequest(file, {}, {onProgress})
            props.onChange(res.data.url)
            onSuccess(res.data.url, file)
          } catch (e) {
            onError(e)
          }
          return true
        },
        onRemove: () => {
          props.onChange('')
          return false
        },
        maxCount: 1,
        listType: 'picture-card',
      }}
    />
  )
}

export default SformUpload
