import {Divider, Flex, Input, Space} from '@astro/ui/ui'
import {getF2CHTMLContent} from 'src/utils'
import CodeEditor from '../CodeEditor'
import styles from './index.module.scss'

function F2CConfigIdContent(props: any) {
  async function onIdSearch(value: string) {
    const text = value
    const numReg = /^\d+$/
    if (numReg.test(text)) {
      const htmlContent = await getF2CHTMLContent(Number(text))
      console.log('htmlContent', htmlContent)
      props.onChange(htmlContent)
    }
  }

  return (
    <Space direction="vertical" size="middle" className={styles.container}>
      <Flex align="center">
        <div className={styles.label}>F2C Id</div>
        <Input.Search enterButton placeholder="请输入F2C的ConfigId" onSearch={onIdSearch} style={{width: '160px'}} />
      </Flex>
      <Flex>
        <div className={styles.label}>HTML代码</div>
        {/* <CodeEditor {...props} initLang="html" /> */}
        <CodeEditor {...props} initLang="rich" codeType={['rich', 'html']} />
      </Flex>
    </Space>
  )
}

export default F2CConfigIdContent
