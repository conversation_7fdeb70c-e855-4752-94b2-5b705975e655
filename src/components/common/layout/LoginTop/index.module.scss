.login {
	display: flex;
	align-items: center;
	padding-block: 0px;
	position: absolute;
	right: 20px;
	top: 10px;

	&:hover {
		background-color: transparent;
	}

	.out__login {
		line-height: 1;
		padding: 2px;
	}
}

.user {
	display: flex;
	align-items: center;
	position: relative;

	&__avatar {
		margin-right: 8px;
		width: 40px;
		height: 40px;
		border-radius: 50%;
		overflow: hidden;

		img {
			display: block;
			width: 100%;
		}
	}

	&__info {
		margin-right: 20px;
		//max-width: 92px;
	}

	&__name {
		display: block;
		font-size: 14px;
		color: #222;
		// @include text-overflow;
	}

	&__id {
		display: block;
		font-size: 12px;
		color: #9a9a9a;
	}

	.trigger {
		width: 40px;
		height: 40px;
		display: flex;
		position: absolute;
		top: 0;
		left: 0;
		background: rgba(0, 0, 0, 0.3);
		border-radius: 50%;
		cursor: pointer;
		align-items: center;
		justify-content: center;
	}
}