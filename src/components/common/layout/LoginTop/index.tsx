import {LogoutOutlined, UserOutlined} from '@astro/ui/icon'
import {Button} from '@astro/ui/ui'
import {useEffect, useState} from 'react'
import {dologinoutMaterial, getUserRole} from 'src/services/api'
import {logout, showLoginBox} from 'src/utils/user'
import {isLogin} from 'src/utils/user'
import styles from './index.module.scss'
const LoginTop = () => {
  const [userInfo, setUserInfo] = useState<{
    avatar?: string
    nickname?: string
    role?: string
  }>({})
  const [trigger, setTrigger] = useState(false)
  const checkLogin = isLogin()
  const doLogin = () => {
    if (!checkLogin) {
      showLoginBox()
    }
  }

  const doLogout = async () => {
    const res = await dologinoutMaterial()
    logout()
  }

  async function getUserRoleDetail() {
    const res = await getUserRole()
    console.log(res)
    if (res && res.data?.result == 200) {
      setUserInfo(res?.data.data || {})
      res?.data && localStorage.setItem('useInfo', JSON.stringify(res?.data))
    } else {
      localStorage.setItem('useInfo', '{}')
    }
  }
  useEffect(() => {
    checkLogin && getUserRoleDetail()
  }, [])

  return (
    <div className={styles.login}>
      {checkLogin && (
        <div className={styles.user} onMouseEnter={() => setTrigger(true)} onMouseLeave={() => setTrigger(false)}>
          <div className={styles.user__avatar}>
            <img src={userInfo.avatar || '//hd-static.yystatic.com/5226331924394756.png'} alt="avatar" />
          </div>
          {trigger && (
            <div
              className={styles.trigger}
              onClick={() => {
                doLogout()
              }}
            >
              <LogoutOutlined style={{color: '#fff'}} />
            </div>
          )}
        </div>
      )}
      {!checkLogin && <Button shape="circle" size="large" icon={<UserOutlined />} onClick={doLogin} />}
    </div>
  )
}
export default LoginTop
