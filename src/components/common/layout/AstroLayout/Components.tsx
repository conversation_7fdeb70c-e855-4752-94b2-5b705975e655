import {ProConfigProvider, ProLayout} from '@astro/ui/pro'
import {ConfigProvider} from '@astro/ui/ui'
import {useNavigate} from 'react-router-dom'
import {LoginTop} from 'src/components/common/user'
import userStore from 'src/components/common/user/userStore'
import projectStore from 'src/components/dashboard/AppStore'
import {playloadOptions} from 'src/components/playload'
import pageStore from 'src/components/playload/editPanel/pageStore'
import './astro.scss'
import astroStore from './astroStore'
import {customerOptions} from './Customer'

export const ProContainer = ({children}: any) => {
  // console.log('ProContainer', pathname)
  const astroState = astroStore.state
  const useState = userStore.state
  const pageState = pageStore.state
  const projectState = projectStore.state
  // const {isPlayload, isLogin, isFormOp} =
  const isPlayload = astroState.isPlayload
  const isFormOp = pageState.isFormOp
  const isLogin = useState.isLogin
  const canAccess = useState.canAccess
  const hasPermission = projectState.hasPermission
  const navigate = useNavigate()
  return (
    <div
      id="astro-layout"
      className={`astro-layout ${isPlayload ? `astro-playload` : ``}`}
      style={{
        height: '100vh',
        overflow: 'auto',
      }}
    >
      <ProConfigProvider hashed={false}>
        <ConfigProvider>
          <ProLayout
            title="Astro"
            logo={null}
            prefixCls="astro-prefix"
            {...astroState.config}
            location={{
              pathname: astroState.pathname,
            }}
            token={{
              header: {
                colorBgMenuItemSelected: 'rgba(0,0,0,0.04)',
              },
            }}
            siderMenuType="group"
            menu={{
              collapsedShowGroupTitle: true,
            }}
            avatarProps={{
              // src: '',
              // title: '',
              render(props: any, dom: any) {
                return <LoginTop />
              },
            }}
            menuItemRender={(
              item: {path: any; desc: any},
              dom:
                | string
                | number
                | boolean
                | React.ReactElement<any, string | React.JSXElementConstructor<any>>
                | Iterable<React.ReactNode>
                | React.ReactPortal
                | null
                | undefined,
            ) => <div onClick={() => navigate(item.path)}>{dom}</div>}
            {...{
              fixSiderbar: true,
              layout: 'mix',
              splitMenus: true,
            }}
            actionsRender={
              isPlayload
                ? playloadOptions({isPlayload, isLogin, isFormOp, canAccess, hasPermission})
                : customerOptions({isPlayload, isLogin, canAccess})
            }
          >
            {/* <ListContainer desc={store.page.desc} /> */}
            {children}
          </ProLayout>
        </ConfigProvider>
      </ProConfigProvider>
    </div>
  )
}
