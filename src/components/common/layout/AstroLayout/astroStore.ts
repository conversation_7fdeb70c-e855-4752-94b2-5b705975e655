import {BaseStore} from 'src/store'
type RoutesItem = {
  path: string
  name: string
  desc: string
  icon?: any
}
class AstroStore extends BaseStore {
  isPlayload = false
  pathname = ''
  page: RoutesItem = {
    path: '',
    name: '',
    desc: '',
  }
  config = {
    route: {
      path: '/',
      routes: [
        {
          path: '/',
          name: '应用',
          desc: '我的项目应用以及所有项目的合集',
          // icon: <SmileFilled />,
          // component: './Welcome',
        },
        {
          name: '案例',
          desc: '可复用的经典案例，支持一键创建活动',
          // icon: <TabletFilled />,
          path: '/projects/recomend',
          // component: './ListTableList',
        },
        // {
        //   name: '统计',
        //   desc: '数据统计面板',
        //   path: '/statistics',
        // },
        //暂时没有有价值的展示信息，后续需要再评估开启
        // {
        //   name: '组件',
        //   desc: '物料组件录入和管理',
        //   path: '/material',
        // },
      ],
    },
    appList: [
      {
        // icon: 'https://empjs.dev/rspress-icon.png',
        icon: '/icon/emp.png',
        title: 'EMP 3.0',
        desc: '基于Rust生态打造的高性能前端构建系统',
        url: 'https://empjs.dev/',
        target: '_blank',
      },
      {
        // icon: 'https://f2c.yy.com/rspress-icon.png',
        icon: '/icon/f2c.png',
        title: 'YY F2C',
        desc: 'Figma & AI相结合生成开发代码的一站式解决方案',
        url: 'https://f2c.yy.com/',
        target: '_blank',
      },
    ],
  }
  currentPage(pathname: string) {
    this.pathname = pathname
    let page = {path: '', name: '', desc: ''}
    if (pathname.startsWith('/projects/playload')) {
      page.path = pathname
      this.isPlayload = true
    } else {
      this.config.route.routes.map(v => {
        if (pathname === v.path) {
          page = v
          return
        }
      })
      this.isPlayload = false
    }

    this.page = page
  }
  addRouter(config: {path: string; name: string; desc: string}) {
    if (this.config.route.routes.findIndex(item => item.path === config.path) === -1) {
      this.config.route.routes.push(config)
    }
  }
}
export default new AstroStore()
