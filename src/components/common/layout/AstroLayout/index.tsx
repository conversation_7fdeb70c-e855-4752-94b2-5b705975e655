import {useEffect, useState} from 'react'
import {useLocation} from 'react-router-dom'
import {RouterContent} from 'src/Router'
import {lxWhitelist, statisticWhitelist} from 'src/utils/user'
import userStore from '../../user/userStore'
import {ProContainer} from './Components'
import astroStore from './astroStore'
const AstroLayout = () => {
  const store = astroStore.state
  const userState = userStore.state
  const location = useLocation()
  // console.log('location', location)
  // const {pathname} = location
  // store.currentPage(pathname)

  useEffect(() => {
    // 灵犀访问uid白名单
    if (userState.isLogin && lxWhitelist.includes(userState.user.uid)) {
      store.addRouter({
        name: '灵希',
        desc: '数据表单配置管理',
        path: '/admin-table',
      })
    }

    // 统计面板访问uid白名单
    // if (userState.isLogin && statisticWhitelist.includes(userState.user.uid)) {
    if (userState.isLogin) {
      store.addRouter({
        name: '统计',
        desc: '数据统计面板',
        path: '/statistics',
      })
    }
  }, [userState.isLogin, userState.user])

  useEffect(() => {
    store.currentPage(location.pathname)
  }, [location.pathname, store.config.route.routes.length])

  return (
    <ProContainer>
      <RouterContent />
    </ProContainer>
  )
}

export default AstroLayout
export {astroStore}
