import {TeamOutlined} from '@astro/ui/icon'
import {Button} from '@astro/ui/ui'
import {useState} from 'react'
import {ApplyPermissionModal} from 'src/components/playload/ApplyPermissionModal'

export const ApplyPermission = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <>
      <Button
        onClick={() => {
          setIsModalOpen(!isModalOpen)
        }}
        color="default"
        variant="filled"
        icon={<TeamOutlined />}
      >
        申请平台权限
      </Button>
      <ApplyPermissionModal isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen} key={'ApplyPermission'} />
    </>
  )
}

export const customerOptions =
  ({isPlayload, isLogin, canAccess}: any) =>
  (): React.JSX.Element[] => {
    return isLogin && !isPlayload && !canAccess ? [<ApplyPermission key="ApplyPermission" />] : []
  }
