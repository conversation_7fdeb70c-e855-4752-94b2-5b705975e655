import {NavLink} from 'react-router-dom'
import LoginTop from '../LoginTop'
import styles from './index.module.scss'
const NavigateBar = () => {
  return (
    <div className={styles.container}>
      <div className={styles.logo}>
        <img src="https://avatars.githubusercontent.com/u/73580039?s=200&v=4" />
        <h1>Astro 业务配置平台</h1>
      </div>
      <ul>
        <li>
          <NavLink to="/" className={({isActive}) => (isActive ? 'actived' : '')}>
            首页
          </NavLink>
        </li>
        <li>
          <NavLink to="/playload" className={({isActive}) => (isActive ? 'actived' : '')}>
            更多案例
          </NavLink>
        </li>
        <li>
          <NavLink to="/projectList" className={({isActive}) => (isActive ? 'actived' : '')}>
            项目列表
          </NavLink>
        </li>
        <li>
          <NavLink to="/classicCase" className={({isActive}) => (isActive ? 'actived' : '')}>
            使用文档
          </NavLink>
        </li>
      </ul>
      <LoginTop />
    </div>
  )
}
export default NavigateBar
