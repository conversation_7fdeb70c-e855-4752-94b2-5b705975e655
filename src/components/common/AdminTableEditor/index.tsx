// import TableEdit from '@astro/adminwebmaster/tableEdit'
// import {EditTwoTone} from '@astro/ui/icon'
import {Select, Spin} from '@astro/ui/ui'
import {type FC, useCallback, useEffect, useMemo, useRef, useState} from 'react'
import {type ServerInfo, type TableInfo, getServerList, getTableList} from '../../AdminTable/util/config'
import css from './layout.module.scss'
import pageStore from 'src/components/playload/editPanel/pageStore'

export interface Props {
  value: string
  onChange: (value: string) => void
  [key: string]: any
}

function debounce(fn: (...items: any[]) => any, wait: number, scope: any = null) {
  let timer = -1
  return function (...items: any[]) {
    clearTimeout(timer)
    timer = window.setTimeout(function () {
      fn.apply(scope, items)
    }, wait)
  }
}

// 默认 serverKey
const DEFAULT_SERVER_KEY = 'hd_boss'

const AdminTableEditor: FC<any> = props => {
  const [allTableList, setAllTableList] = useState<TableInfo[]>([])
  const [tableList, setTableList] = useState<TableInfo[]>([])
  const [serverList, setServerList] = useState<ServerInfo[]>([])
  // const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [fetching, setFetching] = useState(false)

  // 项目配置的 serverKey，如果有值，serverKey 不能切换，只有这个选项
  const projectConfigServerKey = pageStore.state.projectInfo.serverKey

  const [serverKey, setServerKey] = useState(projectConfigServerKey || DEFAULT_SERVER_KEY)
  const lxEnv = pageStore.state.templateControl.curEnvValue === '0' // true 生产环境，false 测试环境

  useEffect(() => {
    let value = DEFAULT_SERVER_KEY
    if (props.value) {
      const arr = props.value.split('.')
      if (arr.length > 1) {
        value = arr[0]
      }
    }
    setServerKey(value)
  }, [props.value])

  // const refInit = useRef(false)
  const selectTableKey = useMemo<string[]>(() => {
    const value: string[] = []
    if (props.value) {
      props.value.split(',').forEach((item: string) => {
        const arr = item.split('.')
        if (arr.length > 1) {
          value.push(arr[1])
        } else {
          value.push(item)
        }
      })
    }

    return value
  }, [props.value])

  console.log('AdminTableEdit', props.value)

  const selectOptions = useMemo(() => {
    return tableList.map(({tableKey: value, tableName: label}) => ({value, label}))
  }, [tableList])

  // const currentTableInfo = useMemo<TableInfo | undefined>(() => {
  //   return tableList.find(item => item.tableKey === tableKey)
  // }, [allTableList, tableKey])

  const onChangeServerKey = (value: string) => {
    setServerKey(value)
    props.onChange('')
  }
  const onChangeTableKey = (value: string[], option: any) => {
    const tableKeys = value.map(item => `${serverKey}.${item}`).join(',')
    props.onChange(tableKeys)
  }

  const debounceSearch = useCallback(
    debounce((search: string, serverKey: string) => {
      getTableList({search: encodeURIComponent(search), serverKey, pageNum: 1, pageSize: 10})
        .then(res => {
          if (res.code === 0) {
            setTableList(res.data.list)
          }
        })
        .finally(() => {
          setFetching(false)
        })
    }, 200),
    [],
  )

  const onSearch = (search: string) => {
    setFetching(true)
    debounceSearch(search, serverKey)
  }

  useEffect(() => {
    getServerList(lxEnv).then(res => {
      if (res.code === 0) {
        setServerList(res.data)
      }
    })
  }, [lxEnv])

  useEffect(() => {
    getTableList({pageNum: 1, pageSize: 200, serverKey}, lxEnv).then(res => {
      if (res.code === 0) {
        const list = res.data.list
        setTableList(list)
        setAllTableList(list)
      }
    })
  }, [serverKey, lxEnv])

  return (
    <div className={css.section}>
      <div className={css.header}>
        <span className={css.title}>{props.label}</span>
      </div>
      <Select
        className={css.btnSelect}
        disabled={!!projectConfigServerKey}
        value={serverKey}
        onChange={onChangeServerKey}
        options={serverList.map(item => ({
          label: item.serverName,
          value: item.serverKey,
        }))}
      />
      <Select
        showSearch
        className={css.btnSelect}
        value={selectTableKey}
        placeholder={'请选择表单数据'}
        defaultActiveFirstOption={false}
        filterOption={false}
        mode={'multiple'}
        onSearch={onSearch}
        onChange={onChangeTableKey}
        notFoundContent={fetching ? <Spin size="small" /> : null}
        options={selectOptions}
      />
      {/*{currentTableInfo && (*/}
      {/*  <ul className={css.infoList}>*/}
      {/*    <li>*/}
      {/*      <span>数据类型: </span>*/}
      {/*      <span className={css.tableType}>{currentTableInfo.tableType}</span>*/}
      {/*      <span className={css.btnEdit} title={'编辑数据表'} onClick={() => setIsEditModalOpen(true)}>*/}
      {/*        <EditTwoTone />*/}
      {/*      </span>*/}
      {/*    </li>*/}
      {/*  </ul>*/}
      {/*)}*/}

      {/*<TableEdit*/}
      {/*  editModal={isEditModalOpen}*/}
      {/*  setEditModal={setIsEditModalOpen}*/}
      {/*  editTableId={currentTableInfo?.tableId}*/}
      {/*  serverKey={serverKey}*/}
      {/*/>*/}
    </div>
  )
}
export default AdminTableEditor
