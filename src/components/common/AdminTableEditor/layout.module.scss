.section {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 8px 8px 8px 8px;
    font-size: 12px;
}
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}
.title {
    font-size: 13px;
    line-height: 16px;
}
.btnSelect {
    margin: 8px 0;
    display: block;
    :global {
        .ant-select-selection-item, .ant-select-selection-placeholder {
            font-size: 12px;
        }
    }
}
.infoList {
    margin: 0;
    padding: 0;
    list-style: none;
    font-size: 12px;
    line-height: 16px;
    word-break: break-word;

    span:first-child {
        opacity: 0.8;
    }

    > li {
        display: flex;
        align-items: center;
    }
}
.tableType {
    margin-left: 4px;
    display: block;
    color: rgba(0, 0, 0, 0.88);
    line-height: 14px;
    background-color: rgb(250, 250, 250);
    border: 1px solid rgb(217, 217, 217);
    border-radius: 4px;
    padding: 3px 6px;
    word-break: break-all;
}
.btnEdit {
    margin-left: auto;
    background-color: rgba(0,0,0,0.04);
    padding: 5px;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
        background-color: rgba(0,0,0,0.1);
    }

    :global {
        .anticon {
            display: block;
        }
    }
}
.textareaInput {
    width: 100%;
    border: 1px solid #ccc;
    padding: 10px;
    min-height: 100px;
    font-size: 12px;
    color: #333;
    margin-bottom: 10px;
}

.selectWrap {
    margin-right: 8cqi;
    display: inline-block;
}