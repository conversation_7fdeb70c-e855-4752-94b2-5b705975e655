.cardWrap {
    :global {

        .ant-card .ant-card-head-title,
        .ant-badge {
            font-size: 12px;
        }
    }
}

.more {
    cursor: pointer;

    span {
        margin-left: 5px;
        color: #1677ff;
    }
}

.noImg {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #1677ff;
    font-size: 18px;
    border-radius: 4px;
}

.uploadCard {
    :global {

        .ant-form-item {
            margin-bottom: 4px;
        }

        .ant-card-actions {

            li {
                margin: 2px 0;
            }
        }
    }
}

.f12 {
    font-size: 12px;
}

.imgContainer {
    flex: 2;
    min-width: 250px;
    max-height: 600px;
    height: fit-content;
    overflow: auto;
}

.htmlViewContainer {
    flex: 8;
    width: 100%;
    min-width: 600px;
    height: 600px;
    // height: fit-content;

    border-radius: 8px;
    border: 1px solid #ccc;
    padding: 20px 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    overflow: auto;
}

.noHtml {
    font-size: 20px;
    font-weight: 600;
}

.outLinedContainer {
    display: flex;
    margin: 5px auto;
}