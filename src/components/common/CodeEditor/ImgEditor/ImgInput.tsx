import {QuestionCircleOutlined} from '@astro/ui/icon'
import {Flex, Input, message, Popover, Space} from '@astro/ui/ui'

export const ImgInput = ({submit}: any) => {

  const dealInput = async (value: string) => {
    const regex = /(https?:\/\/[^\s"'<>]+\.(?:jpg|jpeg|png|gif|svg|mp4|svga)(?:\?[^\s"'<>]*)?)/gi
    let match
    if ((match = regex.exec(value)) !== null) {
      const url = match[0]
      submit?.(url)
    } else {
      message.warning('请输入正确的图片URL')
    }

  }

  return (
    <Flex gap={10}>
      <Space direction="horizontal" align="center">
        <div style={{fontWeight: 'bold'}}>{'媒体URL'}</div>
        <Popover key={'ImgInput'} content="请输入需要替换的媒体URL(支持图片,mp4,svga)" trigger="hover">
          <QuestionCircleOutlined style={{cursor: 'pointer'}} />
        </Popover>
        :
        <Input.Search
          placeholder="请输入需要替换的媒体URL"
          defaultValue={''}
          enterButton="确定"
          allowClear
          onSearch={dealInput}
        />
      </Space>
    </Flex>
  )
}
