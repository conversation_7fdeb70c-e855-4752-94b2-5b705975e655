import {CopyOutlined, FileImageOutlined, FormOutlined, LeftOutlined, RedoOutlined, RightOutlined} from '@astro/ui/icon'
import {Bad<PERSON>, Button, Card, Flex, Modal, Popover, message} from '@astro/ui/ui'
import cx from 'classnames'
import {useEffect, useMemo, useState} from 'react'
import pageStore from 'src/components/playload/editPanel/pageStore'
import SformUpload from '../../Upload'
import {ImgInput} from './ImgInput'
import style from './index.module.scss'

let _originalImgUrl: string[] = [] // 分析出来的原图片数组
let _standbyStr = '' // 分析后，图片值为唯一的字符串
const _imgMap = new Map<number, string>() //修改过的图片map

function ImgEditor(props: any) {
  const pageState = pageStore.state
  const {value, changeStr} = props
  const _isHtml = isHtmlStr(value)
  const [reviewStr, setReviewStr] = useState<string>(value) // 每次图片更换时，预览的html代码

  const [htmlShowView, setHtmlShowView] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const [imgUrlArr, setImgUrlArr] = useState<string[]>([])
  const [imgUrlArrSel, setImgUrlArrSel] = useState<string[]>([])
  const [scrollKey, setScrollKey] = useState('')
  const [colorTagArr, setColorTagArr] = useState<string[]>([])

  const showModal = () => {
    if (!_isHtml) {
      setHtmlShowView(true)
    }
    setIsModalOpen(true)
  }

  const handleOk = () => {
    callBackStr()
    handleCancel()
  }

  const handleCancel = () => {
    setIsModalOpen(false)
    _imgMap.clear()
  }
  //重新上传图片地址
  const onChange = (url: string, id: number) => {
    // const originalUrl = imgUrlArr[id]
    const newImgUrls: any = [...imgUrlArr]
    newImgUrls[id] = url
    setImgUrlArr(newImgUrls)

    _imgMap.set(id, url)

    if (_isHtml) {
      const newHtml = replaceImageToStr() //replaceImageUrl(reviewStr, originalUrl, url, 1)
      newHtml && setReviewStr(newHtml)
    }
  }
  //点击确定后的处理
  const callBackStr = () => {
    const standbyStr = replaceImageToStr()
    if (standbyStr) {
      changeStr?.(standbyStr)
      _imgMap.clear()
    }
  }
  //按当前修改的图片替换，并返回新的字符串
  const replaceImageToStr = () => {
    if (_imgMap && _imgMap.size > 0) {
      let standbyStr = _standbyStr
      _imgMap.forEach((targetUrl, id) => {
        if (id < _originalImgUrl.length) {
          const originalUrl = _originalImgUrl[id]
          standbyStr = replaceImageUrl(standbyStr, originalUrl, targetUrl, 1)
        }
      })

      return standbyStr
      // console.log('replaceImageToStr', value, 'standbyStr', standbyStr)
    }
    return null
  }
  const onClickHtml = (e: any) => {
    // console.log('onClickHtml', e.target)
    const outerHTML = e.target.outerHTML || ''
    // console.log('OuterHTML:', outerHTML)
    const {imageUrls} = extractImageUrls(outerHTML)
    if (imageUrls?.length > 0) {
      setImgUrlArrSel(imageUrls)
    }
    // console.log('onClickHtml---', imageUrls, _imgSelMap)
  }
  //拷贝url
  const contentPopover = (v: string) => {
    return (
      <div className={style.more}>
        {v}
        <span>(点击拷贝)</span>
      </div>
    )
  }
  //还原url为弹窗打开前的图片
  const restoreImg = (url: string, id: number) => {
    try {
      const targetUrl = _originalImgUrl[id]
      const newImgUrls: any = [...imgUrlArr]
      newImgUrls[id] = targetUrl
      setImgUrlArr(newImgUrls)
      _imgMap.delete(id)
      if (_isHtml) {
        setReviewStr(replaceImageUrl(reviewStr, url, targetUrl, 1))
      }
      // console.log('restoreImg url', url, 'targetUrl', targetUrl, '_imgMap', _imgMap, 'newImgUrls', newImgUrls)
    } catch (e: any) {
      console.log('restoreImg error', e.message)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      message.success('链接已拷贝到剪切板！')
    } catch (err) {
      console.error('无法拷贝文本到剪切板:', err)
    }
  }
  const getImgList = useMemo(() => {
    let find = false
    return imgUrlArr?.length > 0
      ? imgUrlArr?.map((v, k) => {
        const hit = imgUrlArrSel.includes(v)
        if (hit && !find) {
          find = true
          setScrollKey(`imgEditor_${k}`)
        }
        return (
          <Badge.Ribbon
            key={`imgEditor_${k}`}
            className={style.f12}
            color={colorTagArr?.[k] || '#1677ff'}
            text={`图片${k + 1}`}
          >
            <Card
              size="small"
              type="inner"
              className={style.uploadCard}
              id={`imgEditor_${k}`}
              key={`imgEditor_${k}`}
              actions={[
                // biome-ignore lint/correctness/useJsxKeyInIterable: <explanation>
                <Popover
                  // color="#eeeeee"
                  content={() => {
                    return contentPopover(v)
                  }}
                  trigger="hover"
                >
                  <CopyOutlined
                    key="copy"
                    onClick={event => {
                      event.stopPropagation()
                      copyToClipboard(v)
                    }}
                  />
                </Popover>,
                // biome-ignore lint/correctness/useJsxKeyInIterable: <explanation>
                <Popover content={'还原图片'} trigger="hover">
                  <RedoOutlined
                    key="setting"
                    onClick={event => {
                      event.stopPropagation()
                      restoreImg(v, k)
                    }}
                  />
                </Popover>,
                <Popover
                  content={<ImgInput submit={(url: string) => {
                    onChange(url, k)
                  }} />}
                  trigger="hover"
                  placement="top">
                  <FormOutlined
                    key="setting"
                    title='写入图片地址'
                    onClick={event => {
                      event.stopPropagation()
                    }}
                  />
                </Popover>,
              ]}
              style={{borderColor: hit ? '#ff0000' : '#f0f0f0'}}
            >
              <SformUpload
                value={v}
                onChange={(url: string) => {
                  onChange(url, k)
                }}
              />
            </Card>
          </Badge.Ribbon>
        )
      })
      : null
  }, [imgUrlArr, imgUrlArrSel])
  useEffect(() => {
    if (isModalOpen) {
      const {imageUrls, standbyStr} = extractImageUrls(value)
      _standbyStr = standbyStr
      _originalImgUrl = imageUrls.slice()
      setImgUrlArr(imageUrls)
      setReviewStr(_standbyStr)
      if (_originalImgUrl?.length > 0) {
        setColorTagArr(generateSmoothColors(_originalImgUrl?.length, {saturation: 90, lightness: 60}))
      }
      // console.log('standbyStr imageUrls', imageUrls, 'standbyStr', standbyStr)
    }
  }, [value, isModalOpen])

  useEffect(() => {
    let fontSize = '100%'
    if (isModalOpen) {
      const rootFontSizeStr = pageState.page?.ext?.rootFontSize || '{}'
      const rootFontSize = JSON.parse(rootFontSizeStr)
      try {
        if (pageState.platform in rootFontSize) {
          fontSize = rootFontSize?.['inner'] //[pageState.platform]
        }
      } catch (e) {}
    }
    // console.log('getImgList fontSize', fontSize)
    //按当前fontSize设置缩放比例
    document.documentElement.style.setProperty('--html-font-size', fontSize)
  }, [isModalOpen])

  useEffect(() => {
    const element = document.getElementById(scrollKey)
    element?.scrollIntoView({behavior: 'smooth'})
  }, [scrollKey])

  return (
    <div>
      <Button size="small" color="default" icon={<FileImageOutlined />} variant="filled" onClick={showModal}>
        图片
      </Button>
      <Modal
        destroyOnHidden={true}
        footer={[
          <Button key="cancel" type="default" onClick={handleCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleOk}>
            确认
          </Button>,
        ]}
        title={`图片编辑器${_originalImgUrl?.length > 0 ? `(${_originalImgUrl?.length})` : ''}`}
        width={
          {
            xs: '90%',
            sm: '90%',
            md: '80%',
            xl: 'fit-content',
            xxl: 'fit-content', //'70%',
          } as any
        }
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        {_originalImgUrl?.length > 0 ? (
          <Flex vertical={false}>
            {!htmlShowView &&
              (_isHtml ? (
                <div
                  className={style.htmlViewContainer}
                  onClick={onClickHtml}
                  // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                  dangerouslySetInnerHTML={{__html: reviewStr}}
                />
              ) : (
                <div className={cx(style.htmlViewContainer, style.noHtml)}>当前内容不是html代码</div>
              ))}
            <div className={style.outLinedContainer}>
              {htmlShowView ? (
                <LeftOutlined
                  title="展开节点可视化"
                  onClick={() => {
                    setHtmlShowView(!htmlShowView)
                  }}
                />
              ) : (
                <RightOutlined
                  title="关闭可视化节点"
                  onClick={() => {
                    setHtmlShowView(!htmlShowView)
                  }}
                />
              )}
            </div>
            <Flex wrap gap="small" className={style.imgContainer}>
              {getImgList}
            </Flex>
          </Flex>
        ) : (
          <div className={style.noImg}>当前内容没有图片数据</div>
        )}
      </Modal>
    </div>
  )
}
export default ImgEditor

function extractImageUrls(value: string) {
  const imageUrls: string[] = []
  const imageUrlMap: Record<string, string> = {}

  const regexes = [
    // // HTML img 标签中的 src 属性
    // /<img\s+[^>]*src=["']([^"']*)["']/gi,
    // // CSS 中的 background-image 属性
    // /background-image:\s*url\(["']?([^"']*)["']?\);?/gi,
    // // CSS 变量中的 URL
    // /--[^:]+:\s*url\s*\(\s*["']?((?:(?!["'\)])[\s\S])+)["']?\s*\)?\s*;/g,
    // // 独立的 URL（可能是内联样式或脚本中的）
    // /url\(["']?([^"']*)["']?\)/g,
    // 匹配图片和视频文件的URL（基于扩展名）
    // /(https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|svg|mp4|svga))/gi,
    // /(https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|svg|mp4|svga)(?:\?[^\s"'<>]*)?)/gi,
    /(https?:\/\/[^\s"'<>]+\.(?:jpg|jpeg|png|gif|svg|mp4|svga)(?:\?[^\s"'<>]*)?)/gi
  ]
  let standbyStr = value

  if (typeof value === 'string') {
    let flg = false //匹配到一个类型即退出
    let match
    for (const regex of regexes) {
      while ((match = regex.exec(value)) !== null) {
        const url = match[0] //match[1].replace(/["']/g, '')
        standbyStr = filterUrl(imageUrlMap, imageUrls, url, standbyStr)
        flg = true
      }
      if (flg) {
        break
      }
    }
  }

  return {imageUrls, standbyStr}
}
// 判断url是否重复，并替换为唯一，为了后续更换图片时，不需要遍历所有图片，减少替换成本
function filterUrl(imageUrlMap: Record<string, string>, imageUrls: string[], url: string, str: string) {
  let targetUrl = url
  let standbyStr = str
  if (!imageUrlMap[url]) {
    imageUrlMap[url] = url
  } else {
    const newUrl = generateUniqueId(url) // 生成唯一ID
    imageUrlMap[newUrl] = newUrl
    targetUrl = newUrl
    standbyStr = replaceImageUrl(standbyStr, url, targetUrl, 2)
  }
  imageUrls.push(targetUrl) // 使用唯一ID
  return standbyStr
}
function addQueryParamSeparator(base: string): string {
  if (base.includes('?')) {
    return base + '&'
  } else {
    return base + '?'
  }
}
let id = 0
// 简单的唯一ID生成函数
function generateUniqueId(base: string) {
  return `${addQueryParamSeparator(base)}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}_${++id}`
}
function replaceImageUrl(value: string, originalUrl: string, imageUrl: string, matchIndex = 1) {
  const escapedOriginalUrl = originalUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  const regex = new RegExp(escapedOriginalUrl, 'g')
  let count = 0
  let result = value

  // 遍历搜索结果，看是否匹配到了第 matchIndex 个结果
  function replaceAtIndex(match: string): string {
    count++
    if (count === matchIndex) {
      return imageUrl
    }
    return match
  }
  result = result.replace(regex, replaceAtIndex)
  return result
}
function isHtmlStr(str: string): boolean {
  const htmlRegex = /<[^>]+>/
  return htmlRegex.test(str)
}
//标签色值生成器，用于生成渐变色标签色值数组
function generateSmoothColors(length: number, options: any = {}) {
  const {
    hueRange = 60, // 色相变化范围，控制颜色变化的多样性
    saturation = 90, // 饱和度，值较低会使颜色更柔和
    lightness = 60, // 亮度，值较高会使颜色更柔和
  } = options

  const baseHue = Math.random() * 360

  const colors = []

  for (let i = 0; i < length; i++) {
    const ratio = i / (length - 1)
    const hue = (baseHue + ratio * hueRange) % 360
    const color = hslToHex(hue, saturation, lightness)
    colors.push(color)
  }

  return colors
}
function hslToHex(h: number, s: number, l: number) {
  s /= 100
  l /= 100

  const c = (1 - Math.abs(2 * l - 1)) * s
  const x = c * (1 - Math.abs(((h / 60) % 2) - 1))
  const m = l - c / 2
  let r = 0,
    g = 0,
    b = 0

  if (h >= 0 && h < 60) {
    r = c
    g = x
    b = 0
  } else if (h >= 60 && h < 120) {
    r = x
    g = c
    b = 0
  } else if (h >= 120 && h < 180) {
    r = 0
    g = c
    b = x
  } else if (h >= 180 && h < 240) {
    r = 0
    g = x
    b = c
  } else if (h >= 240 && h < 300) {
    r = x
    g = 0
    b = c
  } else if (h >= 300 && h < 360) {
    r = c
    g = 0
    b = x
  }

  // 将RGB值转换为HEX
  const toHex = (n: number) => {
    const hex = Math.round((n + m) * 255).toString(16)
    return hex.length === 1 ? '0' + hex : hex
  }

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`
}
