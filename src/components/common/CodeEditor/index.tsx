import {ExpandOutlined} from '@astro/ui/icon'
import {ProFormText} from '@astro/ui/pro'
import {Button, Flex, Modal, Radio} from '@astro/ui/ui'
import Editor from '@monaco-editor/react'
import cx from 'classnames'
import {useEffect, useState} from 'react'
import RichEditor from 'src/components/common/Editor/wang'
import pageStore from 'src/components/playload/editPanel/pageStore'
import ImgEditor from './ImgEditor'
import css from './editor.module.scss'

function CodeEditor(props: any) {
  const CodeType = props.codeType ?? ['json', 'javascript', 'html', 'scss']
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [showDiff, setShowDiff] = useState(false)
  const [editingValue, setEditingValue] = useState('') // 编辑中的值
  const [oldValue, setOldValue] = useState('') // 固定的旧值
  const pageState = pageStore.state

  const setFontSize = (size: string) => {
    document.documentElement.style.fontSize = size
  }
  // 初始化值
  useEffect(() => {
    setEditingValue(getInputValue())
  }, [props.value])

  const showModal = () => {
    setOldValue(getInputValue()) // 保存旧值
    setIsModalOpen(true)
    // 设置html的font-size为60px
    setFontSize('60px')
    setShowDiff(false)
  }

  const handleOk = () => {
    props.onChange(editingValue) // 确认时才提交
    setIsModalOpen(false)
    // 移除html的font-size设置
    setFontSize('')
    setShowDiff(false)
  }

  const handleCancel = () => {
    setIsModalOpen(false)
    // 移除html的font-size设置
    setFontSize('')
    setShowDiff(false)
  }

  const handleDiff = () => {
    setShowDiff(!showDiff)
  }

  // 统一获取输入值
  const getInputValue = () => {
    if (typeof props.value === 'object') {
      return JSON.stringify(props.value, null, 2)
    }
    return props.value || ''
  }

  const defaultLang = props.initLang || CodeType?.[0]
  const [lang, setLang] = useState(defaultLang)

  // 核心差异计算函数
  const getPureKeyDiff = (oldStr: string, newStr: string) => {
    try {
      const oldObj = JSON.parse(oldStr)
      const newObj = JSON.parse(newStr)
      const oldKeys = Object.keys(oldObj)
      const newKeys = Object.keys(newObj)
      // 显示完整键值对
      const removed = oldKeys.filter(k => !newKeys.includes(k)).map(k => `- "${k}": ${JSON.stringify(oldObj[k])}`)
      const added = newKeys.filter(k => !oldKeys.includes(k)).map(k => `+ "${k}": ${JSON.stringify(newObj[k])}`)

      return {
        oldText: removed.join('\n') || '{}',
        newText: added.join('\n') || '{}',
      }
    } catch {
      return {oldText: oldStr, newText: newStr}
    }
  }
  const {oldText, newText} = getPureKeyDiff(oldValue, editingValue)
  return (
    <div style={{width: '100%'}}>
      <ProFormText {...props} value={getInputValue()} />
      <Flex wrap gap="small">
        <Button size="small" color="default" icon={<ExpandOutlined />} variant="filled" onClick={showModal}>
          编辑
        </Button>
        <ImgEditor value={getInputValue()} changeStr={props.onChange} />
        {/* <Button color="default" icon={<FormatPainterOutlined />} variant="filled" onClick={handleFormatJson}>
          JSON格式化
        </Button> */}
      </Flex>

      <Modal
        footer={[
          <div className={css.selectWrap} key="lang">
            <Radio.Group
              onChange={e => {
                setLang(e.target.value)
              }}
              defaultValue={lang}
            >
              {CodeType.map((item: string) => {
                return (
                  <Radio.Button key={item} value={item}>
                    {item === 'rich' ? `富文本` : item}
                  </Radio.Button>
                )
              })}
            </Radio.Group>
          </div>,
          // <Button key="diff" type="default" onClick={handleDiff}>
          //   {showDiff ? '取消对比' : '换肤对比'}
          // </Button>,
          <Button key="cancel" type="default" onClick={handleCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleOk}>
            确认
          </Button>,
        ]}
        title="编辑器"
        width={{xs: '90%', sm: '90%', md: '80%', lg: '80%', xl: '70%', xxl: '70%'} as any}
        open={isModalOpen}
        onCancel={handleCancel}
      >
        {lang === 'rich' ? (
          <div className={cx(css.richEditor, pageState.page?.isDark && css.richEditorDark)}>
            <RichEditor {...props} />
          </div>
        ) : (
          <Editor
            language={lang}
            onChange={(d: any) => {
              props.onChange(d)
            }}
            width={'100%'}
            height={'500px'}
            value={getInputValue()} />
        )}
        {/* {showDiff ? (
          <ReactDiffViewer
            oldValue={oldText}
            newValue={newText}
            splitView={false}
            showDiffOnly={false}
            hideLineNumbers={true}
            styles={{
              diffAdded: {backgroundColor: '#e6ffed'},
              diffRemoved: {backgroundColor: '#ffebe9'},
              diffContainer: {
                // minHeight: '500px',
                overflow: 'auto',
                fontSize: '14px',
              },
              line: {
                padding: '2px',
              },
            }}
          />
        ) : (
          <Editor
            language={lang}
            value={editingValue}
            onChange={value => setEditingValue(value || '')}
            width="100%"
            height="500px"
          />
        )} */}
      </Modal>
    </div>
  )
}

export default CodeEditor
