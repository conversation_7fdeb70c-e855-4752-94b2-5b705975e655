import componentStore from 'src/components/playload/editPanel/toolPanel/DrawerComponent/componentStore'
import {getMTags} from 'src/services/api'
import {BaseStore} from 'src/store'
class TagStore extends BaseStore {
  tagList = []
  selectType: any = -1
  componentType: any = 1 //组件类型 1为常规组件 2为layout组件
  projectTaglist: any = []

  //init多加了一个ComponentType, 初始化后调用接口后返回正确类型的组件
  async init(componentType: number) {
    this.setComponentType(componentType)
    await Promise.all([this.getTabList(), this.searchTagComponent('')])
  }
  async getTabList() {
    this.tagList = await getMTags()
  }
  //业务类型下拉框选项列表
  getTypeOptions() {
    let options: any = []
    if (this.tagList?.length > 0) {
      options = this.tagList?.map((item: any) => {
        return {
          value: item.typeId,
          label: item.typeName,
        }
      })
      options.unshift({
        value: 0,
        label: '全部',
      })
    }
    return options
  }
  getTagOptions() {
    let options: any = []
    if (this.selectType === 0) {
      const tagMap = new Map<string, any>()
      this?.tagList?.map((itemTag: any) => {
        itemTag?.tagVoList?.map((item: any) => {
          const v = tagMap.get(item.tagName)
          !v && tagMap.set(item.tagName, item.tagName)
        })
      })
      //全部的情况下，不同业务可能存在相同value，但tagId不同。
      //搜索时候用keyworks
      options = Array.from(tagMap, ([key, value]) => ({value: value, label: key}))
    } else {
      this?.tagList?.map((itemTag: any) => {
        if (this.selectType === itemTag?.typeId) {
          options = itemTag?.tagVoList?.map((item: any) => {
            return {
              value: item.tagId,
              label: item.tagName,
            }
          })
          return
        }
      })
    }
    return options
  }
  setSelectType(value: any) {
    this.selectType = value
  }
  setComponentType(value: number) {
    this.componentType = value
  }
  setProjectTaglist(projecttaglist: any[]) {
    this.projectTaglist = [...projecttaglist].filter((item: number) => item === 1001 || item === 4 || item === 1002)
    // localStorage.setItem('projectList', JSON.stringify([...projecttaglist]))
  }
  async searchTagComponent(value: string) {
    const keyword = value.trim() === '' ? '' : value.trim()
    // const storageData = JSON.parse(localStorage.getItem('projectList') ?? '')
    // const tagChosen: [] = storageData ?? []
    componentStore.searchComponent({
      componentType: this.componentType > 0 ? this.componentType : undefined,
      tagTypeId: this.selectType > 0 ? this.selectType : '',
      keyword: keyword,
      projectTag: this.projectTaglist.length > 0 ? this.projectTaglist.join(',') : '',
    })
  }
}
export default new TagStore()

export enum componentType {
  common = 1,
  layout = 2,
}
