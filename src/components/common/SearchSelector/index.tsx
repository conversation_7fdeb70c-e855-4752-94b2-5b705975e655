import type {GetProps, RadioChangeEvent} from '@astro/ui/ui'
import {Checkbox, Input, Select, Space} from '@astro/ui/ui'
import {useCallback, useEffect, useMemo, useRef, useState} from 'react'
import TagList from 'src/components/Material/editPanel/TagList'
import pageStore from 'src/components/playload/editPanel/pageStore'
import config from 'src/config'
import LineHeight from 'wangeditor/dist/menus/lineHeight'
import tagStore, {componentType} from './tagStore'
interface SearchSelectorProps {
  open: boolean
  configType?: string
  defaultType?: number
  pageSize?: number
  pageNumber?: number
  reload?: number
}

// 搜索选择器组件
const SearchSelector = (props: SearchSelectorProps) => {
  const timeoutRef = useRef(0)
  const pageState = pageStore.state
  const {configType, open, defaultType = 1, pageSize, pageNumber, reload} = props
  const tagState = tagStore.state
  const [searchText, setSearchText] = useState('')
  const [projectTagOptions, setTagOptions] = useState([])
  const {Search} = Input
  type SearchProps = GetProps<typeof Input.Search>
  const typeOptions = useMemo(() => {
    const options = tagState.getTypeOptions()
    if (options?.length > 0 && !tagState.selectType) {
      tagState.setSelectType(defaultType)
    } else if (!options?.length) {
      tagState.setSelectType('全部')
    }
    return options
  }, [JSON.stringify(tagState?.tagList)])

  const tagOptions = useMemo(() => {
    return tagState.getTagOptions()
  }, [JSON.stringify(tagState?.tagList), tagState.selectType])

  const handleComponentChangeType = (value: RadioChangeEvent) => {
    tagState.setComponentType(value.target.value)
    tagState.searchTagComponent('')
  }
  const handleChangeType = (value: any) => {
    tagState.setSelectType(value)
    //清空
    tagState.searchTagComponent('')
  }
  const handleSearch: SearchProps['onSearch'] = (value, _e, info) => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current)
    switch (info?.source) {
      case 'clear':
        setSearchText('')
        tagState.searchTagComponent('')
        break
      case 'input':
        tagState.searchTagComponent(value)
        break
    }
  }
  const handleTagChange = (value: any) => {
    console.log('handleTagChange', value)
    tagState.setProjectTaglist([...value])
    tagState.searchTagComponent(searchText)
  }
  const onhandleChange = useCallback(() => {
    return (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      if (e.nativeEvent.type === 'click') return
      if (timeoutRef.current) clearTimeout(timeoutRef.current)
      setSearchText(e.target.value)
      timeoutRef.current = window.setTimeout(() => tagState.searchTagComponent(e.target.value), 600)
    }
  }, [])
  //只在进入项目的时候按照项目的tag标签更新，其他情况保留tag
  useEffect(() => {
    tagState.setProjectTaglist(Array.from(pageStore.projectInfo?.tags ?? []))
  }, [])
  useEffect(() => {
    if (!open) return
    setSearchText('')
    const componenttype = configType === 'page' ? componentType.layout : componentType.common
    tagState.init(componenttype)
  }, [open])

  // useEffect(() => {
  //   if (reload !== undefined || (pageSize && pageNumber)) {
  //     tagState.searchTagComponent('')
  //   }
  // }, [pageSize, pageNumber, reload])
  const options = [
    {
      value: 0,
      label: '全部',
    },
    {
      value: 1001,
      label: '基础',
    },
    {
      value: 4,
      label: '后台',
    },
    {
      value: 1002,
      label: '服务治理',
    },
  ]
  return (
    <>
      <Search
        // addonBefore={configType === 'page' ? 'Layout组件' : ''}
        addonBefore={
          <Select
            value={
              // 简化逻辑，使用 find 方法找到第一个匹配的标签值
              // 优先级：基础 > 后台 > 服务治理 > 全部
              tagState.projectTaglist.find((item: number) => item === 1001)
                ? 1001
                : tagState.projectTaglist.find((item: number) => item === 4)
                  ? 4
                  : tagState.projectTaglist.find((item: number) => item === 1002)
                    ? 1002
                    : 0
            }
            onChange={(d: any) => {
              handleTagChange([d])
            }}
            options={options}
            style={{width: '100px'}}
          />
        }
        onSearch={handleSearch}
        onChange={onhandleChange()}
        value={searchText}
        style={{
          minWidth: '400px',
        }}
        allowClear
      />
      {/* <Checkbox.Group
        options={config.projectTagOption.filter(item => item.value === 4)}
        defaultValue={Array.from(pageStore.projectInfo?.tags?.filter((item: number) => item === 4) ?? [])}
        onChange={handleTagChange}
        value={Array.from(tagState.projectTaglist.filter((item: number) => item === 4))}
      /> */}
    </>
  )
}
export default SearchSelector
