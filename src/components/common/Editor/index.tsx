import {ProFormTextArea} from '@astro/ui/pro'
import {Tabs} from '@astro/ui/ui'
import {useState} from 'react'
import RichEditor from './wang'
export default function Editor(props: any) {
  const [act, setAct] = useState('editor')
  return (
    <>
      <Tabs
        defaultActiveKey={act}
        onChange={(e: any) => {
          setAct(e)
        }}
        items={[
          {
            key: 'editor',
            label: '编辑器',
            children: act === 'editor' && <RichEditor {...props} />,
          },
          {
            key: 'code',
            label: '代码',
            children: act === 'code' && <TextArea {...props} />,
          },
        ]}
      />
    </>
  )
}
export const TextArea = (props: any) => {
  return <ProFormTextArea {...props} />
}
