import {useEffect, useRef} from 'react'
import UploadRequest, {type IUploadData} from 'src/utils/uploadRequest'
import E from 'wangeditor'

const uploadImg = async ({file, onSuccess, onError}: any) => {
  const res = await UploadRequest(file)
  onSuccess(res?.data?.url)
}

export const WangEditor = (props: any) => {
  const divRef: any = useRef()
  const editorRef: any = useRef()
  useEffect(() => {
    if (divRef.current && editorRef.current) return
    editorRef.current = new E(divRef.current)

    editorRef.current.config.onchange = (h: any) => {
      props.onChange(h)
    }
    editorRef.current.config.onchangeTimeout = 500 // 设置onchange的执行频率，初始化为200毫秒。
    editorRef.current.config.fontSizes = {
      'xx-small': {name: '10px', value: '0'},
      'x-small': {name: '12px', value: '1'},
      small: {name: '14px', value: '2'},
      normal: {name: '16px', value: '3'},
      large: {name: '18px', value: '4'},
      'x-large': {name: '24px', value: '5'},
      'xx-large': {name: '32px', value: '6'},
      'xxx-large': {name: '48px', value: '7'},
    }
    editorRef.current.config.customUploadImg = function (resultFiles: any[], insertImgFn: (arg0: any) => void) {
      // resultFiles 是 input 中选中的文件列表
      // insertImgFn 是获取图片 url 后，插入到编辑器的方法
      uploadImg({
        file: resultFiles[0],
        onSuccess: (imgUrl: any) => {
          insertImgFn(imgUrl)
        },
      })
      // 上传图片，返回结果，将图片插入到编辑器中
      // insertImgFn(imgUrl)
    }
    //
    editorRef.current.create()
    // editorRef.current.txt.html(value)
  }, [divRef])

  useEffect(() => {
    if (!editorRef.current || (editorRef.current && editorRef.current.txt.html() === props.value)) return
    editorRef.current.txt.html(props.value)
  }, [props.value, editorRef.current])

  return <div ref={divRef}></div>
}

export default WangEditor
