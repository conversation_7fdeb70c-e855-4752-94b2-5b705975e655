import {InfoCircleOutlined, LoadingOutlined, SearchOutlined} from '@astro/ui/icon'
import {Button, Form, Input, Select, Space, Tooltip, message} from '@astro/ui/ui'
import {useCallback, useEffect, useRef, useState} from 'react'
import f2cStore from 'src/components/playload/editPanel/f2cStore'
import pageStore from 'src/components/playload/editPanel/pageStore'
const checkValueType = (inputValue: any) => {
  const reg = /^-?\d*(\.\d*)?$/
  if (reg.test(inputValue) || inputValue === '' || inputValue === '-') {
    return true
  }
  return false
}
const getCurConfigData = (version: number, list: Array<any>) => {
  if (list) {
    for (const item of list) {
      if (item?.version == version) {
        return item
      }
    }
  }
  return null
}
const optionsRadio = [
  {label: '平铺key', value: 1},
  {label: '父级_key', value: 2},
]
const F2CConfigIdSelect = () => {
  const formRef = useRef<any>(null)
  const pageState = pageStore.state
  const f2cState = f2cStore.state
  const [configId, setConfigId] = useState('')
  const [configList, setConfigList] = useState([])
  const [loading, setLoading] = useState(false)
  const [versionList, setVersionList] = useState<{value: string; label: string}[]>([])
  const getF2CInfo = useCallback(async (configId: any) => {
    if (!checkValueType(configId)) {
      message.error('请检查内容，按数字输入')
    }
    setLoading(true)
    let rs_list: any = await f2cState.getConfigVersionList(Number(configId))
    if (rs_list) {
      rs_list = rs_list?.sort(
        (a: {[x: string]: number}, b: {[x: string]: number}) => Number(b['version']) - Number(a['version']),
      )
      setConfigList(rs_list)
      console.log('rs_list', rs_list)
      setVersionList(
        rs_list.map((item: any, id: number) => {
          return {
            label: id == 0 ? `最新(v-${item?.version})` : `v-${item?.version}`,
            // value: item?.version,
            value: item?.version,
          }
        }),
      )

      formRef.current.setFieldValue('configIdVersionF2C', rs_list[0]?.version)
      setLoading(false)
      return rs_list
    } else {
      message.error('F2C数据为空，请检查输入的configId')
    }
    setLoading(false)
    return []
  }, [])

  const handleFinish = async (value: any, list = null) => {
    const curComponent: any = f2cState.getComponent()
    const curExt: any = curComponent?.ext
    const modeF2C = value?.modeF2C || 1
    // console.log('@@@@@@handleFinish:', value, 'configList,', configList, 'curExt', curExt)
    if (!('configIdVersionF2C' in value) || !value?.configIdVersionF2C || value?.configIdVersionF2C == '暂无选择') {
      message.error('请选择F2C配置版本')
      return
    }

    if (value?.configIdF2C) {
      // pageState.editPage(pageState.pageKey, {ext: {...curExt, ...value}}, pageState.pageKey, true)
      value?.configIdF2C != curExt?.configIdF2C && pageState.saveFormItemExt('configIdF2C', value?.configIdF2C)
      if (value?.configIdVersionF2C && value?.configIdVersionF2C != '暂无选择') {
        const _list = list || configList
        pageState.saveFormItemExt('configIdVersionF2C', value?.configIdVersionF2C)
        f2cState.updateComponentToConfigId(
          curComponent,
          getCurConfigData(value?.configIdVersionF2C, _list),
          modeF2C,
        )
        pageState.setFormOp(pageState.pageKey, pageState.componentKey)
      }
    }
    pageState.saveFormItemExt('modeF2C', modeF2C)
  }

  // const handleChange = (value: string) => {
  //   console.log(`selected ${value}`)
  // }
  const onChangeInput = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    console.log('onChangeInput:', e.target.value)
    setConfigId(e.target.value)
  }

  const getSuffix = (configId: string) => {
    return !loading ? (
      <SearchOutlined
        onClick={() => {
          getF2CInfo(configId)
        }}
        style={{
          cursor: 'pointer',
          fontSize: 16,
          color: '#1677ff',
        }}
      />
    ) : (
      <LoadingOutlined
        style={{
          fontSize: 16,
          color: '#d9d9d9',
        }}
      />
    )
  }
  const getInitVal = useCallback(() => {
    //pageState.componentKey ==-1 表示只选择了页面
    let fieldsValue = {configIdF2C: '', configIdVersionF2C: '暂无选择', modeF2C: 1}
    const curExt: any = f2cState.getComponent()?.ext
    if (curExt) {
      fieldsValue = {
        configIdF2C: curExt.configIdF2C,
        configIdVersionF2C: curExt.configIdVersionF2C,
        modeF2C: curExt.modeF2C || 1,
      }
    }
    formRef.current.setFieldsValue(fieldsValue)
    console.log('ext 默认值', curExt, fieldsValue, pageState.componentKey, pageState.pageKey)
    return fieldsValue
  }, [pageState.componentKey, pageState.pageKey])

  useEffect(() => {
    const configInfo = getInitVal()
    if (configInfo?.configIdF2C) {
      setConfigId(configInfo?.configIdF2C)
    }
    // saveFormDataConfig
    // 未选择版本时候，还是先不拉版本，因为最终需要点击“更新”才会更新，交互上容易误解
    // if (configInfo?.configIdF2C && !configInfo.configIdVersionF2C) {
    //   getF2CInfo(configInfo?.configIdF2C)
    // }
  }, [pageState.componentKey, pageState.pageKey])

  const autoUpdate = async () => {
    const info = {...f2cState.updataComF2CInfo}
    if ('configId' in info && 'version' in info) {
      const list = await getF2CInfo(info.configId)
      handleFinish({configIdF2C: info.configId + '', configIdVersionF2C: info.version}, list)
    }
    f2cState.setUpdataComF2CInfo({})
  }
  useEffect(() => {
    autoUpdate()
  }, [f2cState.updataComF2CInfo])
  return (
    <Form
      // initialValues={getInitVal()}
      ref={formRef}
      onFinish={async (value: any) => {
        handleFinish(value)
      }}
    >
      <Space direction="vertical">
        <Space>
          <div>组件ConfigId</div>
          <Tooltip key="F2CCircleOutlined" title={'请输入F2C组件的ConfigId'}>
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
        <Space.Compact style={{width: '100%', height: '32px'}}>
          <Form.Item name="configIdF2C">
            <Input
              allowClear
              placeholder="ConfigId"
              suffix={getSuffix(configId)}
              disabled={loading}
              onChange={onChangeInput}
              onPressEnter={() => {
                getF2CInfo(configId)
              }}
            />
          </Form.Item>

          <Form.Item name="configIdVersionF2C">
            <Select
              disabled={loading || versionList?.length < 1}
              options={versionList}
              // onChange={handleChange}
              style={{width: '100px'}}
            />
          </Form.Item>
        </Space.Compact>
        {/* 功能先屏蔽，后续再放开(预埋替换的第二种方式，暂时不开放)
        <Form.Item name="modeF2C" >
          <Radio.Group block options={optionsRadio} />
        </Form.Item> */}
        <Button
          type="primary"
          key="submitF2C"
          htmlType="submit"
        >
          更新
        </Button>
      </Space>
    </Form>
  )
}

export default F2CConfigIdSelect
