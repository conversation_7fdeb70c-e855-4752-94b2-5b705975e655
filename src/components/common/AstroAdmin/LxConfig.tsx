import TableEdit from '@astro/adminwebmaster/tableEdit'
import {AppstoreAddOutlined, EditOutlined} from '@astro/ui/icon'
import {Button, Select, Spin} from '@astro/ui/ui'
import {type FC, useCallback, useEffect, useMemo, useRef, useState} from 'react'
import pageStore from 'src/components/playload/editPanel/pageStore'
import {
  type ServerInfo,
  type TableInfo,
  type TableMode,
  getServerList,
  getTableList,
} from '../../AdminTable/util/config'
import css from './layout.module.scss'

export interface Props {
  value?: string
  onChange?: (value: string) => void
  lxTableMode?: TableMode
  [key: string]: any
}

function debounce(fn: (...items: any[]) => any, wait: number, scope: any = null) {
  let timer = -1
  return function (...items: any[]) {
    clearTimeout(timer)
    timer = window.setTimeout(function () {
      fn.apply(scope, items)
    }, wait)
  }
}

// 默认 serverKey
const DEFAULT_SERVER_KEY = 'hd_boss'

const AstroAdmin: FC<Props> = props => {
  const [allTableList, setAllTableList] = useState<TableInfo[]>([])
  const [tableList, setTableList] = useState<TableInfo[]>([])
  const [serverList, setServerList] = useState<ServerInfo[]>([])
  const [openEditModal, setOpenEditModal] = useState(false)
  const [editTableId, setEditTableId] = useState('')
  const [addTableKey, setAddTableKey] = useState<string>('') // 记录添加成功的 tableKey
  const [fetching, setFetching] = useState(false)

  // 项目配置的 serverKey，如果有值，serverKey 不能切换，只有这个选项
  const projectConfigServerKey = pageStore.state.projectInfo.serverKey

  const [serverKey, setServerKey] = useState(projectConfigServerKey || DEFAULT_SERVER_KEY)
  const lxEnv = pageStore.state.templateControl.curEnvValue === '0' // true 生产环境，false 测试环境

  useEffect(() => {
    let value = DEFAULT_SERVER_KEY
    if (props.value) {
      const arr = props.value.split('.')
      if (arr.length > 1) {
        value = arr[0]
      }
    }
    setServerKey(value)
  }, [props.value])

  useEffect(() => {
    if (projectConfigServerKey) {
      setServerKey(projectConfigServerKey)
    }
  }, [projectConfigServerKey])

  const tableKey = useMemo<string | undefined>(() => {
    return props.value?.split('.').pop() || undefined
  }, [props.value])

  const selectOptions = useMemo(() => {
    return tableList.map(({tableKey: value, tableName: label}) => ({value, label}))
  }, [tableList])

  const currentTableInfo = useMemo<TableInfo | undefined>(() => {
    return tableList.find(item => item.tableKey === tableKey)
  }, [allTableList, props.value])

  // console.log(
  //   'AstroAdmin',
  //   'props.value=',
  //   props.value,
  //   'TableInfo=',
  //   currentTableInfo,
  //   'serverKey=',
  //   serverKey,
  //   'tableKey=',
  //   tableKey,
  //   'lxEnv=',
  //   lxEnv,
  //   pageStore.state,
  // )

  const onChangeServerKey = (value: string) => {
    setServerKey(value)
    props.onChange?.('')
  }
  const onChangeTableKey = (value: string, option: any) => {
    if (value) {
      props.onChange?.(`${serverKey}.${value}`)
    } else {
      props.onChange?.('')
    }
  }

  const onEdit = () => {
    setEditTableId(currentTableInfo?.tableId || '')
    setOpenEditModal(true)
  }

  const onAdd = () => {
    setEditTableId('')
    setOpenEditModal(true)
  }

  const onUpdateSuccess = (tableKey: string) => {
    props.onChange?.(`${serverKey}.${tableKey}`)
    setAddTableKey(tableKey)

    // TODO 刷新 iframe 预览
    const iframe = document.querySelector(`iframe[title="preview"]`) as HTMLIFrameElement
    if (iframe) {
      const url = new URL(iframe.src)
      url.searchParams.append('_time', Date.now() + '')
      iframe.src = url.toString()
    }
  }

  const debounceSearch = useCallback(
    debounce((search: string, serverKey: string, tableMode: TableMode) => {
      getTableList({search: encodeURIComponent(search), serverKey, pageNum: 1, pageSize: 10, tableMode}, lxEnv)
        .then(res => {
          if (res.code === 0) {
            setTableList(res.data.list)
          }
        })
        .finally(() => {
          setFetching(false)
        })
    }, 200),
    [lxEnv],
  )

  const onSearch = (search: string) => {
    setFetching(true)
    debounceSearch(search, serverKey, props.lxTableMode)
  }

  useEffect(() => {
    getServerList(lxEnv).then(res => {
      if (res.code === 0) {
        setServerList(res.data)
      }
    })
  }, [lxEnv])

  useEffect(() => {
    getTableList({pageNum: 1, pageSize: 200, serverKey, tableMode: props.lxTableMode}, lxEnv).then(res => {
      if (res.code === 0) {
        const list = res.data.list
        setTableList(list)
        setAllTableList(list)
      }
    })
  }, [serverKey, addTableKey, props.lxTableMode, lxEnv])

  return (
    <div className={css.section}>
      <div className={css.header}>
        <span className={css.title}>{props.label}</span>
      </div>
      <Select
        disabled={!!projectConfigServerKey}
        className={css.btnSelect}
        value={serverKey}
        onChange={onChangeServerKey}
        options={serverList.map(item => ({
          label: item.serverName,
          value: item.serverKey,
        }))}
      />
      <Select
        showSearch
        className={css.btnSelect}
        value={tableKey}
        placeholder={'请选择表单数据'}
        defaultActiveFirstOption={false}
        filterOption={false}
        onSearch={onSearch}
        onChange={onChangeTableKey}
        notFoundContent={fetching ? <Spin size="small" /> : null}
        options={selectOptions}
      />
      {/*{currentTableInfo && (*/}
      {/*  <ul className={css.infoList}>*/}
      {/*    <li>*/}
      {/*      <span>数据类型: </span>*/}
      {/*      <span className={css.tableType}>{currentTableInfo.tableType}</span>*/}
      {/*    </li>*/}
      {/*  </ul>*/}
      {/*)}*/}
      <div className={css.btnGroup}>
        {currentTableInfo && (
          <Button size={'small'} onClick={onEdit} icon={<EditOutlined />}>
            数据配置
          </Button>
        )}
        <Button type={'primary'} size={'small'} onClick={onAdd} icon={<AppstoreAddOutlined />}>
          添加表
        </Button>
      </div>

      {openEditModal && (
        <TableEdit
          env={lxEnv}
          editModal={openEditModal}
          setEditModal={setOpenEditModal}
          onSuccess={onUpdateSuccess}
          editTableId={editTableId}
          serverKey={serverKey}
        />
      )}
    </div>
  )
}
export default AstroAdmin
