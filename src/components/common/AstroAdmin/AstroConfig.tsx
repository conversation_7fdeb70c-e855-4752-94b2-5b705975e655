import TableEdit from '@astro/adminwebmaster/tableEdit'
import {AppstoreAddOutlined, EditOutlined} from '@astro/ui/icon'
import {Button, Select, Spin, Tag} from '@astro/ui/ui'
import {type FC, useCallback, useEffect, useMemo, useRef, useState} from 'react'
import {useParams} from 'react-router-dom'
import type {TableMode} from 'src/components/AdminTable/util/config'
import pageStore from 'src/components/playload/editPanel/pageStore'
import css from './layout.module.scss'

export interface Props {
  value?: string
  onChange?: (value: string) => void
  tableMode?: TableMode
  [key: string]: any
  dataType: 'astro' | 'lx' | 'sysop'
}

const AstroAdmin: FC<Props> = props => {
  const [openEditModal, setOpenEditModal] = useState(false)
  const {id: projectId} = useParams()

  const [astroAdminConfig, setAstroAdminConfig] = useState<any>({})

  useEffect(() => {
    if (props.value) {
      let value: any = {}
      try {
        value = JSON.parse(props.value)
      } catch (e) {}
      setAstroAdminConfig(value)
    } else {
      setAstroAdminConfig({})
    }
  }, [props.value])

  // console.log('AstroAdmin', 'props.value=', props.value, 'astroAdminConfig=', astroAdminConfig, pageStore.state.page)

  const onEdit = () => {
    // setEditTableId(currentTableInfo?.tableId || '')
    setOpenEditModal(true)
  }

  const onUpdateSuccess = (data: any) => {
    setAstroAdminConfig(data)
    props.onChange?.(JSON.stringify(data))
  }

  return (
    <div className={css.section}>
      {astroAdminConfig?.tableKey && (
        <div className={css.astroInfoList}>
          <div>表格Key：</div>
          <Tag>{astroAdminConfig?.tableKey}</Tag>
        </div>
      )}

      <Button size={'small'} type={'primary'} onClick={onEdit} icon={<EditOutlined />}>
        数据配置
      </Button>

      {openEditModal && (
        <TableEdit
          isAstroSave={true}
          astroProjectId={projectId}
          astroPageKey={pageStore.page.pageKey}
          astroTableMode={props.tableMode}
          astroAdminConfig={astroAdminConfig}
          editModal={openEditModal}
          setEditModal={setOpenEditModal}
          onSuccess={onUpdateSuccess}
          dataType={props.dataType}
        />
      )}
    </div>
  )
}
export default AstroAdmin
