import {Radio} from '@astro/ui/ui'
import {type FC, useEffect, useMemo, useState} from 'react'
import type {TableMode} from 'src/components/AdminTable/util/config'
import AstroConfig from './AstroConfig'
import LxConfig from './LxConfig'
import css from './layout.module.scss'

export interface Props {
  value?: string
  onChange?: (value: string) => void
  tableMode?: TableMode
  [key: string]: any
}
type DataTypeString = 'lx' | 'sysop' | 'astro'

const AstroAdmin: FC<Props> = props => {
  const hasValue = useMemo(() => {
    return props.value !== undefined && props.value !== ''
  }, [props.value])

  const [dataType, setDataType] = useState<DataTypeString>('astro')
  const [data, setData] = useState({lx: '', sysop: '', astro: ''})

  useEffect(() => {
    if (props.value) {
      try {
        const valueJson = JSON.parse(props.value)
        if (valueJson.sysopTableName) {
          setDataType('sysop')
          setData({...data, sysop: props.value})
        } else {
          setData({...data, astro: props.value})
          setDataType('astro')
        }
      } catch (e) {
        setData({...data, lx: props.value})
        setDataType('lx')
      }
    }
  }, [props.value])

  const handleChange = (value: DataTypeString) => {
    setDataType(value)
  }

  // console.log('AstroAdmin', 'props.value=', props.value, 'isAstroConfig=', isAstroConfig)

  return (
    <div className={''}>
      <div className={css.section}>
        <div className={css.marBtm8}>配置类型</div>
        <Radio.Group
          onChange={event => handleChange(event.target.value)}
          defaultValue={dataType}
          value={dataType}
          optionType="button"
          options={[
            {value: 'astro', label: 'Astro'},
            {value: 'lx', label: '灵希'},
            {value: 'sysop', label: 'Sysop'},
          ]}
        />
      </div>
      {dataType === 'lx' ? (
        <LxConfig {...props} value={data[dataType]} />
      ) : (
        <AstroConfig {...props} value={data[dataType] || ''} dataType={dataType} />
      )}
    </div>
  )
}
export default AstroAdmin
