import {Empty, Flex, Result, Spin} from '@astro/ui/ui'
import './status.scss'
const contentStyle: React.CSSProperties = {
  padding: 50,
  borderRadius: 4,
}
export const Loading = () => (
  <Flex style={{height: '300px'}} justify={'center'} align={'center'}>
    <Spin tip="Loading..." size="large">
      <div style={contentStyle} />
    </Spin>
  </Flex>
)
export const NotFind = () => (
  <Result
    status="404"
    title="404"
    subTitle="暂无项目，可尝试去创建一个项目!"
    // extra={<Button type="primary">Back Home</Button>}
  />
)

export const Tips = ({title}: any) => {
  return (
    <div className="astro-ui-result">
      <Result title={title} />
    </div>
  )
}
export const Warn = ({title}: any) => {
  return (
    <div className="astro-ui-result">
      <Result status={'warning'} title={title} />
    </div>
  )
}

export const EmptyContent: React.FC = () => <Empty style={{minHeight: '200px'}} />
