import pageStore from 'src/components/playload/editPanel/pageStore'
import type {RoleType} from 'src/config/types'
import * as api from 'src/services/api'
export type UserType = {
  avatar: string
  nickname: string
  role: RoleType //角色 T 技术人员 C普通人员 P 产品人员 A管理员
  roleDesc?: string
  uid: number
  userName?: string
  roomId?: string
  pageKey?: string
  componentKey?: number
  createTime?: number
}
import {BaseStore} from 'src/store'
import {isLogin} from 'src/utils/user'
// import {mockUsers} from './mockData'
export class UserStore extends BaseStore {
  isLogin = isLogin()
  users: UserType[] = []
  user: UserType = {
    avatar: '',
    nickname: '',
    role: 'C',
    uid: 0,
    pageKey: pageStore.pageKey,
    componentKey: pageStore.componentKey,
  }
  get canAccess() {
    return this.isLogin && ['A', 'P', 'T'].includes(this.user.role)
  }
  public userLoading = false
  async getUserInfo(query?: any) {
    this.userLoading = true
    if (isLogin()) {
      this.isLogin = true
      this.user = await api.getUserInfo(query)
    }
    this.userLoading = false
  }
  async logout(query?: any) {
    await api.logout(query)
  }
  onlineUsers(u: UserType[]) {
    // 重置线上的用户选择 避免多开串号
    const user = u.find(d => d.uid === this.user.uid)
    if (user) {
      user.pageKey = pageStore.pageKey
      user.componentKey = pageStore.componentKey
    }
    //
    this.users = u
  }
  getOnlineUserList(pageKey: string, componentKey: number) {
    // this.users = mockUsers // mock数据
    const users: UserType[] = this.users
    if (!pageKey) return users
    return users.filter((user: UserType) => {
      return (!pageKey || user.pageKey === pageKey) && (componentKey < 0 || user.componentKey === componentKey)
    })
  }
  //
  editorUser: UserType = {
    avatar: '',
    nickname: '',
    role: 'C',
    uid: 0,
    userName: '',
    createTime: 0,
  }
  setEditorUser(user: UserType, createTime?: number) {
    this.editorUser = {...user, createTime}
  }
  setOwnEditorUser() {
    this.editorUser = {...this.user, createTime: Date.now()}
  }
}
export default new UserStore()
