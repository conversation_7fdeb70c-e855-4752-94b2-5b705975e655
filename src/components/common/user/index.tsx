import {LogoutOutlined, UserOutlined} from '@astro/ui/icon'
import {Avatar, Button, Dropdown, Space} from '@astro/ui/ui'
import {useEffect} from 'react'
import {logout, showLoginBox} from 'src/utils/user'
import userStore from './userStore'

export const LoginTop = () => {
  const store = userStore.state
  useEffect(() => {
    store.getUserInfo()
  }, [])
  return !store.isLogin ? (
    <Button onClick={() => showLoginBox()} key="login" variant="filled" color="default" icon={<UserOutlined />}>
      登录
    </Button>
  ) : (
    <Dropdown
      menu={{
        items: [
          {
            key: 'account',
            label: store.user.userName || store.user.nickname,
            disabled: true,
          },
          {
            type: 'divider',
          },
          {
            key: 'logout',
            icon: <LogoutOutlined />,
            label: (
              <span
                onClick={async () => {
                  await store.logout()
                  logout()
                }}
              >
                退出登录
              </span>
            ),
          },
        ],
      }}
    >
      {/*   <span onClick={e => e.preventDefault()}>
        {store.user.avatar ? <Avatar src={store.user.avatar} /> : <Avatar icon={<UserOutlined />} />}
        <span style={{marginInlineStart: `8px`}}>
          <span className="anticon">{store.user.userName || store.user.nickname}</span>
        </span>
      </span> */}
      <span onClick={e => e.preventDefault()}>
        <Space>
          {store.user.avatar ? (
            <Avatar shape="square" src={store.user.avatar} />
          ) : (
            <Avatar shape="square" icon={<UserOutlined />} />
          )}
          {/* <DownOutlined style={{fontSize: 9}} /> */}
        </Space>
      </span>
    </Dropdown>
  )
}
