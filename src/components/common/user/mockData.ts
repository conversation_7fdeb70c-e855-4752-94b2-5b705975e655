import type {UserType} from './userStore'

export const mockUsers: UserType[] = [
  {
    avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=1',
    nickname: 'User1',
    role: 'A',
    roleDesc: '管理员',
    uid: 1,
    userName: 'User One',
    roomId: 'room123',
    pageKey: 'index',
    componentKey: 0,
  },
  {
    avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=2',
    nickname: 'User2',
    role: 'P',
    roleDesc: '产品人员',
    uid: 2,
    userName: 'User Two',
    roomId: 'room456',
    pageKey: 'index',
    componentKey: 0,
  },
  {
    avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=3',
    nickname: 'User3',
    role: 'T',
    roleDesc: '技术人员',
    uid: 3,
    userName: 'User Three',
    roomId: 'room789',
    pageKey: 'index',
    componentKey: 1,
  },
  {
    avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=4',
    nickname: 'User4',
    role: 'C',
    roleDesc: '普通人员',
    uid: 4,
    userName: 'User Four',
    roomId: 'room101112',
    pageKey: 'index',
    componentKey: 2,
  },
  {
    avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=5',
    nickname: 'User5',
    role: 'A',
    roleDesc: '管理员',
    uid: 5,
    userName: 'User Five',
    roomId: 'room131415',
    pageKey: 'widget',
  },
  {
    avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=6',
    nickname: 'User5',
    role: 'A',
    roleDesc: '管理员',
    uid: 6,
    userName: 'User Five',
    roomId: 'room131415',
    pageKey: 'widget',
    componentKey: 0,
  },
]
