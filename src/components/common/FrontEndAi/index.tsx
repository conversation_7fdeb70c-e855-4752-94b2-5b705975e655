import {UploadOutlined} from '@astro/ui/icon'
import {<PERSON>ge, Flex, Popconfirm, Space, Tooltip, message} from '@astro/ui/ui'
import React, {useState, useEffect} from 'react'
import {convertUrlInfo} from 'src/utils/f2c'
import CodeEditor from '../CodeEditor'
import AuthFigma from './AuthFigma'
import SearchFigma from './SearchFigma'
import styles from './index.module.scss'
import figmaStore from './store'

// 前端君组件
const FrontEndAi = (props: any) => {
  const figmaState = figmaStore.state

  useEffect(() => {
    console.log('###figma授权检查')
    figmaState.setUpAuth()
    figmaState.checkAuthTime()
  }, [])

  const gotoCourse = () => {
    window.open(
      'https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/M-wK0zh99p/nNsrl5_5fg/UmQVJw34Uvw_6G?hi_client_lang=zh-Hans',
      '_blank',
    )
  }

  const convertUrl = async () => {
    const convertText = await convertUrlInfo(props.value)
    props.onChange(convertText)
    message.success('上传成功')
  }

  return (
    <Badge.Ribbon text="前端君" placement="start" color="volcano" style={{top: '0px'}}>
      <Space direction="vertical" size="middle" className={styles.container}>
        <span title="查看教程" className={styles.tips} onClick={gotoCourse}></span>

        {!figmaState.isAuth ? <AuthFigma /> : <SearchFigma {...props} />}

        <Flex style={{width: '100%'}}>
          <Popconfirm
            placement="topLeft"
            title={'扫描HTML代码，将figma链接图片转存到bos中?'}
            okText="确认"
            showCancel={false}
            onConfirm={convertUrl}
          >
            <div className={styles.label} title="手动转换上传图片到bos">
              HTML代码
            </div>
          </Popconfirm>

          {/* <CodeEditor {...props} initLang="html" /> */}
          <CodeEditor {...props} initLang="rich" codeType={['rich', 'html']} />
        </Flex>
      </Space>
    </Badge.Ribbon>
  )
}
export default FrontEndAi
