import {Flex, Image, Input, Spin, message} from '@astro/ui/ui'
import type React from 'react'
import {useEffect, useState} from 'react'
import {getCode, getPreviewImage, getSearchParams} from 'src/services/f2cFigma'
import {convertUrlInfo} from 'src/utils/f2c'
import styles from './index.module.scss'
import figmaStore from './store'

const urlReg = /^((https?):\/\/)?([\w-]+(\.[\w-]+)*)(:\d+)?(\/[\w\u4e00-\u9fa5\-\.\/?%&=]*)?$/

const SearchFigma = (props: any) => {
  const figmaState = figmaStore.state
  const [previewUrl, setPreviewUrl] = useState('')
  const [loading, setLoading] = useState(false)
  const [previewLoading, setPreviewLoading] = useState(false)
  const [previewTips, setPreviewTips] = useState('暂无预览')
  const [figmaLink, setFigmaLink] = useState('')

  useEffect(() => {
    return () => {
      setPreviewUrl('')
      setPreviewTips('暂无预览')
      setFigmaLink('')
    }
  }, [])

  // 加载预览图
  const getFigmaPreview = async () => {
    if (!urlReg.test(figmaLink)) return
    setPreviewLoading(true)
    try {
      const previewResponse = await getPreviewImage(getSearchParams(figmaLink, figmaState.acessToken, 'png'))
      if (previewResponse?.data) {
        const images = previewResponse?.data?.images
        // 获取 images 对象中第一个元素的值
        const firstImageUrl = Object.values(images)[0] as string
        setPreviewUrl(firstImageUrl)
      } else {
        setPreviewTips('加载预览图失败')
      }
    } finally {
      setPreviewLoading(false)
    }
  }

  // 生成代码
  async function onFigmaSearch() {
    if (urlReg.test(figmaLink)) {
      setLoading(true)
      try {
        // 获取预览图
        getFigmaPreview()

        // 生成代码
        const fileResponse: any = await getCode(getSearchParams(figmaLink, figmaState.acessToken, 'html'))
        if (fileResponse.ok) {
          const originText = await fileResponse.text()
          const convertText = await convertUrlInfo(originText)
          props.onChange(convertText)
        } else {
          const info = JSON.parse(await fileResponse.text())
          message.error(`${info?.msg}:  ${info?.detail}`)

          // 授权码失效，重新申请授权
          if (info.code === 403) {
            figmaState.cleanAuth()
          }
        }
      } finally {
        setLoading(false)
      }
    } else {
      message.error('请输入Figma链接')
    }
  }

  // 监听 figmaLink 变化
  useEffect(() => {
    if (!figmaLink) {
      setPreviewUrl('')
      setPreviewTips('暂无预览')
    }
  }, [figmaLink])

  return (
    <Flex vertical gap={10}>
      <Flex align="center">
        <div className={styles.label}>Figma链接</div>
        <Input.Search
          placeholder="请输入Figma链接"
          onSearch={e => {
            figmaState.isAuth ? onFigmaSearch() : message.error('当前没有Figma授权')
          }}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFigmaLink(e.target.value)}
          value={figmaLink}
          style={{width: '100%'}}
          loading={loading}
          enterButton={'生成'}
        // clear
        />
      </Flex>

      <Flex align="center">
        <div className={styles.label}>生成预览</div>
        {previewLoading ? (
          <Flex align="center" justify="center" className={styles.previewContainer}>
            <Spin />
          </Flex>
        ) : previewUrl ? (
          <Image align="center" justify="center" src={previewUrl} className={styles.previewImage} />
        ) : (
          <Flex align="center" justify="flex-start" className={styles.previewContainer}>
            <span className={styles.previewTips}>{previewTips}</span>
          </Flex>
        )}
      </Flex>
    </Flex>
  )
}
export default SearchFigma
