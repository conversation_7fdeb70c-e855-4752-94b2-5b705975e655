import {Button, Flex} from '@astro/ui/ui'
import {useEffect} from 'react'
import config from 'src/config'
import {AuthFigmaLink} from './config'
import figmaStore from './store'

const AuthFigma = () => {
  const figmaState = figmaStore.state
  const gotoAuth = () => {
    window.open(AuthFigmaLink, '_blank')
  }

  useEffect(() => {
    const handleAuthInfo = async (e: any) => {
      figmaState.setUpAuth()
    }
    window.addEventListener('storage', handleAuthInfo)

    return () => {
      window.removeEventListener('storage', handleAuthInfo)
    }
  }, [])

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // 添加一个小延迟，确保页面完全获得焦点
        setTimeout(() => {
          figmaState.getClipboardInfo()
        }, 100)
      }
    }

    // 测试环境才需要监听剪贴板获取acessToken
    if (!config.isTest) return

    // 添加页面可见性变化事件监听
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 如果页面初始状态是可见的，也执行一次
    if (document.visibilityState === 'visible') {
      setTimeout(() => {
        figmaState.getClipboardInfo()
      }, 100)
    }

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  return (
    <Flex align={'center'} gap={10}>
      <span>设计稿转代码功能需要您向figma授权</span>
      <Button type="primary" onClick={gotoAuth}>
        去授权
      </Button>
    </Flex>
  )
}

export default AuthFigma
