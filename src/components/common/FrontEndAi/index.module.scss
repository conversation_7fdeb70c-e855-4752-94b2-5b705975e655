.container {
  width: 100%;
  padding: 20px 4px 10px;
  border: 1px solid rgb(209, 207, 207);
  border-radius: 10px;

  .label {
    flex-shrink: 0;
    flex-grow: 0;
    // width: 80px;
    font-size: 12px;
    text-align: right;
    margin-right: 10px;
  }

  .tips {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    width: 50px;
    height: 20px;
    cursor: pointer;
  }

  :global {
    .ant-input-search .ant-input::placeholder {
      font-size: 12px;
      /* 调整字体大小 */
    }
  }
}

.previewContainer {
  width: 120px;
  height: 120px;
}

.previewTips {
  margin-left: 15px;
  color: #999;
  font-size: 12px;
}

.previewImage {
  width: 120px;
}