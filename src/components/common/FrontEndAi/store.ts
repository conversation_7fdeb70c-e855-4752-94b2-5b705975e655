import {message} from '@astro/ui/ui'
import {BaseStore} from 'src/store'
import userStore from '../user/userStore'
import {AstroLocalFigmaAuth} from './config'

export const getLocalKey = (key: string) => {
  const uid = userStore?.user?.uid
  return `${key}-${uid}`
}

class FigmaStore extends BaseStore {
  isAuth = false
  acessToken = ''
  authTime = 0

  setUpAuth() {
    const uid = userStore?.user?.uid
    this.setFigmaAuthState({
      isAuth: localStorage.getItem(`${AstroLocalFigmaAuth.IsAuth}-${uid}`) === 'true',
      acessToken: localStorage.getItem(`${AstroLocalFigmaAuth.AcessToken}-${uid}`) || '',
      authTime: Number(localStorage.getItem(`${AstroLocalFigmaAuth.AuthTime}-${uid}`)) || 0,
    })
  }

  setFigmaAuthState({isAuth, acessToken, authTime}: {isAuth: boolean; acessToken: string; authTime: number}) {
    this.isAuth = isAuth
    this.acessToken = acessToken
    this.authTime = authTime
  }

  // 检查授权时间是否过期
  checkAuthTime() {
    if (!this.authTime) return
    const now = Math.floor(Date.now() / 1000)
    const authTimestamp = Math.floor(this.authTime / 1000)
    if (authTimestamp - now <= 0) {
      message.info('figma授权已过期')
      this.cleanAuth()
    }
  }

  cleanAuth() {
    const uid = userStore?.user?.uid

    this.isAuth = false
    this.acessToken = ''
    this.authTime = 0
    localStorage.removeItem(`${AstroLocalFigmaAuth.IsAuth}-${uid}`)
    localStorage.removeItem(`${AstroLocalFigmaAuth.AcessToken}-${uid}`)
    localStorage.removeItem(`${AstroLocalFigmaAuth.AuthTime}-${uid}`)
  }

  async getClipboardInfo() {
    try {
      if (!document.hasFocus()) return

      const text = await navigator.clipboard?.readText()

      try {
        const obj = JSON.parse(text || '{}')
        if (obj.accessToken) {
          const uid = userStore?.user?.uid

          localStorage.setItem(`${AstroLocalFigmaAuth.IsAuth}-${uid}`, obj.isAuth)
          localStorage.setItem(`${AstroLocalFigmaAuth.AcessToken}-${uid}`, obj.accessToken)
          localStorage.setItem(`${AstroLocalFigmaAuth.AuthTime}-${uid}`, obj.authTime)

          this.setUpAuth()
        }
      } catch (error) {
        // console.error('解析剪贴板内容失败:', error)
      }
    } catch (error) {
      // console.error('读取剪贴板失败:', error)
    }
  }
}

const figmaStore = new FigmaStore()

export default figmaStore
