import {ExclamationCircleOutlined} from '@astro/ui/icon'
import {Badge, Flex} from '@astro/ui/ui'
import {memo, useEffect} from 'react'
import pageStore from 'src/components/playload/editPanel/pageStore'
import styles from './playload.module.scss'
export const Dot = memo(({children, pageKey, componentKey}: any) => {
  const pageState = pageStore.state
  useEffect(() => {}, [pageState.formOp])
  return <Badge dot={pageState.checkFormOp(pageKey, componentKey)}>{children}</Badge>
})

export const NoSetting = () => {
  return (
    <Flex style={{height: '100%'}} justify={'center'} align="center">
      <div className={styles.NoSetting}>
        <ExclamationCircleOutlined style={{fontSize: '50px'}} />
        <h3>Welcome to Astro</h3>
        <p>无设置选项</p>
      </div>
      {/* <Result status={'warning'} title="无设置选项" /> */}
    </Flex>
  )
}
