import { LoadingOutlined, PlusOutlined } from '@astro/ui/icon'
import { ProFormUploadButton } from '@astro/ui/pro'
import { message } from '@astro/ui/ui'
import React, { useState, useEffect } from 'react'
import { uploadFiles } from 'src/services/api'
import { type IUploadData, UploadRequest } from 'src/utils/uploadRequest'
import styles from './index.module.scss'

// const getBase64 = (img: any, callback: any) => {
//   const reader = new FileReader()
//   reader.addEventListener('load', () => {
//     callback(reader.result)
//   })
//   reader.readAsDataURL(img)
// }

const beforeUpload = (file: any) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'

  if (!isJpgOrPng) {
    message.error('只能上传JPG/PNG文件!')
  }

  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isLt2M) {
    message.error('图片必须小于2MB!')
  }

  return isJpgOrPng && isLt2M
}
interface ImgUploadProps {
  img?: string
  value?: any
  onChange?: (value: any) => void
}

const ImgUpload = (props: ImgUploadProps) => {
  const { value: img, onChange } = props
  const [loading, setLoading] = useState(false)
  // const [imageUrl, setImageUrl] = useState(img || '')
  // const uploadUrl = uploadFiles()

  const uploadRequest = async ({ onProgress, onSuccess, onError, file }: any) => {
    try {
      setLoading(true)
      const res: IUploadData = await UploadRequest(file, {}, { onProgress })
      setLoading(false)
      console.log("###res.data.url", res.data.url)
      onChange?.(res.data.url)
      // setImageUrl(res.data.url)
      onSuccess(res.data.url, file)
    } catch (e) {
      onError(e)
    }
    return true
  }

  // const handleChange = (info: any) => {
  //   if (info.file.status === 'uploading') {
  //     //服务文件字段为file
  //     info.file.file = info.file.originFileObj
  //     setLoading(true)
  //     return
  //   }

  //   if (info.file.status === 'done') {
  //     if (info.file?.response?.result == 200) {
  //       onChange?.(info.file?.response?.data?.downloadUrl)
  //     }
  //     getBase64(info.file.originFileObj, (url: string) => {
  //       setLoading(false)
  //       setImageUrl(url)
  //     })
  //   }
  // }
  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div
        style={{
          marginTop: 8,
        }}
      >
        图片上传
      </div>
    </div>
  )

  return (
    <ProFormUploadButton
      name="avatar"
      label="项目图片"
      max={1}
      className={styles.uploadImg}
      listType="picture-card"
      showUploadList={false}
      // action={uploadUrl}
      data={{ addTimeTag: 1 }}
      beforeUpload={beforeUpload}
      fieldProps={{
        customRequest: uploadRequest,
        onRemove: () => {
          onChange?.('')
        },
      }}
    // onChange={handleChange}
    >
      {img ? <img className={styles.pic} src={img} /> : uploadButton}
    </ProFormUploadButton>
  )
}

export default ImgUpload
