import {message} from '@astro/ui/ui'
import pageState from 'src/components/playload/editPanel/pageStore'
import type {ProjectBusinessType, ProjectTagsType, ProjectType} from 'src/config/project'
import {
  type IListParams,
  type ISaveProjectParams,
  collectProject,
  copyProject,
  getPermission,
  getProjectList,
  getProjectPublicList,
  hideProject,
  recommendProject,
  saveProject,
  uncollectProject,
} from 'src/services/api'
import {BaseStore} from 'src/store'

export class AppStore extends BaseStore {
  channel = 'all'
  tagsTypeFilter: number | undefined = undefined
  businessTypeFilter: number | undefined = undefined
  statusTypeFilter: number | undefined = undefined
  list: ProjectType[] = []
  hasPermission = false
  setChannel(select: string) {
    this.channel = select
  }
  setTagsTypeFilter(v: number | undefined) {
    this.tagsTypeFilter = v
  }
  setBusinessTypeFilter(v: number | undefined) {
    this.businessTypeFilter = v
  }
  setStatusTypeFilter(v: number | undefined) {
    this.statusTypeFilter = v
  }
  get filterList() {
    let filterList = this.list
    if (this.businessTypeFilter) {
      filterList = this.list.filter(v => v.business.includes(this.businessTypeFilter as ProjectBusinessType))
    }
    if (this.tagsTypeFilter) {
      filterList = this.list.filter(v => v.tags.includes(this.tagsTypeFilter as ProjectTagsType))
    }
    /* if (this.statusTypeFilter !== undefined) {
      console.log(this.statusTypeFilter )
      filterList = this.list.filter((v) => (v.status === this.statusTypeFilter))
    } */
    return filterList
  }
  projectLoad = false
  async queryProjectList(query?: IListParams) {
    const {recommend, ...rest} = query || {}
    this.list = []
    this.projectLoad = true
    if (recommend || this.channel === 'recommend') {
      const res = await getProjectPublicList()
      if (res && res.data?.result == 200 && res.data?.data && res.data?.data?.list) {
        this.list = res.data.data.list
      }
    } else {
      const res = await getProjectList({
        scope: this.channel === 'my' ? 0 : this.channel === 'collect' ? 2 : 1,
        ...rest,
      })
      if (res && res.data?.result == 200 && res.data?.data && res.data?.data?.list) {
        this.list = res.data.data.list
      }
    }
    this.projectLoad = false
  }
  async doSaveProject(
    params: ISaveProjectParams | {envKey: string; envValueList: any[]; id?: number},
    refreshList = true,
  ) {
    const {data: res, err} = await saveProject(params as ISaveProjectParams)
    console.log(res, err)
    if (err) {
      message.error(`${params?.id ? '编辑' : '新建'}项目失败`)
    }

    if (res && res?.result == 200) {
      message.success(`${params?.id ? '编辑' : '新建'}项目成功`)
      if (refreshList) this.queryProjectList()
      // params?.id && pageState.queryProjectDetail({projectId: params?.id})
      pageState.updateProjectInfo(params)
      return true
    } else {
      message.error(res?.reason)
      return false
    }
  }
  async doCopyProject(id: number) {
    const {data: res, err} = await copyProject(id)
    if (err) {
      message.error('复制项目失败')
      return false
    }
    if (res && res?.result == 200) {
      message.success('复制项目成功')
      this.queryProjectList()
      return res?.data
    } else {
      message.error(res?.reason)
      return false
    }
  }

  async doRecommend(id: number) {
    const {data: res, err} = await recommendProject(id)
    if (err) {
      message.error('推荐项目失败')
    }
    const d = this.list.find(v => v.id === id)
    if (d) {
      d.recommend = !d.recommend
    }
  }

  async doHide(id: number) {
    const {data: res, err} = await hideProject(id)
    if (err) {
      message.error('删除项目失败')
      return false
    }
    if (res && res?.result == 200) {
      message.success('删除项目成功')
      return true
    } else {
      message.error(res?.reason)
      return false
    }
  }

  async checkPermission(id: number | string) {
    const {data: res} = await getPermission(id)
    if (res && res?.result == 200) {
      this.hasPermission = true
    } else {
      this.hasPermission = false
    }
  }

  async doCollect(id: number) {
    const {data: res, err} = await collectProject(id)
    if (err) {
      message.error('收藏项目失败')
    }
    const d = this.list.find(v => v.id === id)
    if (d) {
      d.isFavorite = !d.isFavorite
    }
  }
  async cancelCollect(id: number) {
    const {data: res, err} = await uncollectProject(id)
    if (err) {
      message.error('取消收藏项目失败')
    }
    const d = this.list.find(v => v.id === id)
    if (d) {
      d.isFavorite = !d.isFavorite
    }
  }
}

export default new AppStore()
