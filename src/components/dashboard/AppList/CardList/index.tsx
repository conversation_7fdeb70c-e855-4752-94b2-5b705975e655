import {SyncOutlined, UnorderedListOutlined, UserOutlined} from '@astro/ui/icon'
import {ProList} from '@astro/ui/pro'
import {Avatar, Badge, Col, Flex, Progress, Row, Tag, Tooltip} from '@astro/ui/ui'
import dayjs from 'dayjs'
import {useEffect} from 'react'
import {useLoaderData} from 'react-router-dom'
import {AstroPageContainer} from 'src/components/common'
import userStore from 'src/components/common/user/userStore'
import appStore from 'src/components/dashboard/AppStore'
import type {ProjectType} from 'src/config/project'
import {astroBusinessMap, astroTagsMap} from 'src/config/project'
import {
  CollectBtn,
  CopyBtn,
  EditorBtn,
  PreviewBtn,
  RecomendBtn,
  ToolBarAddProject,
  ToolBarFilter,
  ToolBarSearch,
  ToolBarSelectChannel,
} from './Actions'
import './card.scss'
const getPercent = (record: ProjectType) => {
  const curTime = Date.now()
  return curTime < record.beginTime
    ? 0
    : curTime > record.endTime
      ? 100
      : Math.floor(((curTime - record.beginTime) / (record.endTime - record.beginTime)) * 100)
}

// 标题组件
const CardTitle = ({record}: {record: ProjectType}) => (
  <div key={`title-${record.id}`}>
    <Flex key={`row-title-name-${record.id}`}>
      <span>{record.actName}</span>
      <TagsList record={record} />
    </Flex>
    <Row key={`row-title-desc-${record.id}`}>
      <Col className={'desc'} key={`desc-${record.id}`}>
        {record.description}
      </Col>
    </Row>
    {/* <Row key={`row-title-tags-${record.id}`}>
      <Col>
        <div className={'tags'} key={`tags-${record.id}`}>
          {record.business.map((v, k) => (
            <Fragment key={`${v}-biz-${record.id}`}>
              <span>{astroBusinessMap[v]}</span>
              {k < record.business.length - 1 && <Divider type="vertical" />}
            </Fragment>
          ))}
          <Divider type="vertical" />
          {record.tags.map((v, k) => (
            <Fragment key={`${v}-tag-${record.id}`}>
              <span>{astroTagsMap[v]}</span>
              {k < record.tags.length - 1 && <Divider type="vertical" />}
            </Fragment>
          ))}
        </div>
      </Col>
    </Row> */}
  </div>
)

// 头像组件
const CardAvatar = ({record}: {record: ProjectType}) => (
  <Tooltip
    key={`avatar-${record.id}`}
    title={
      <div className="avatar-tooltip">
        <p>{record.creator}</p>
        <p>{dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
        {!!record.lockVersion && <p>lockVersion: {record.lockVersion}</p>}
      </div>
    }
  >
    <div className={'avatar'}>
      <Badge status="success" dot={!!record.lockVersion}>
        <Avatar shape="square" icon={<UserOutlined />} size="large" src={record.createAvatar} />
      </Badge>
    </div>
  </Tooltip>
)

// 编辑者列表组件
const EditorsList = ({editors}: {editors: any[]}) => (
  <div>
    {editors && editors.length > 0 ? (
      <Avatar.Group
        max={{
          count: 5,
        }}
      >
        {editors.toReversed().map((editor: any, idx: number) => (
          <Tooltip title={editor.passport} key={editor.uid}>
            <Avatar
              src={editor.avatar}
              size="small"
              style={{
                cursor: 'pointer',
              }}
            />
          </Tooltip>
        ))}
      </Avatar.Group>
    ) : (
      '-'
    )}
  </div>
)

// 标签列表组件
const TagsList = ({record}: {record: ProjectType}) => (
  <div className={`contentItem tags`}>
    {record.tags.map((v, k) => (
      <Tag color="cyan" key={`${v}-tag-${record.id}`} bordered={false}>
        {astroTagsMap[v]}
      </Tag>
    ))}

    {record.business.map((v, k) => (
      <Tag color="geekblue" key={`${v}-biz-${record.id}`} bordered={false}>
        {astroBusinessMap[v]}
      </Tag>
    ))}
  </div>
)

// 时间进度组件
const TimeProgress = ({record}: {record: ProjectType}) => (
  <div className={`contentItem time`}>
    {record.beginTime && record.endTime ? (
      <Tooltip
        placement="topLeft"
        title={
          <div>
            {`开始时间：${dayjs(record.beginTime).format('YYYY-MM-DD HH:mm:ss')}`}
            <br />
            {`结束时间：${dayjs(record.endTime).format('YYYY-MM-DD HH:mm:ss')}`}
          </div>
        }
      >
        {record.status ? (record.status === 1 ? '进行中' : '已结束') : '未开始'}
        <Progress style={{width: '100%'}} percent={getPercent(record)} />
      </Tooltip>
    ) : (
      <div className="never">
        <p>
          <Tooltip title={'无限期'}>
            <SyncOutlined style={{fontSize: `14px`}} />
          </Tooltip>
        </p>
      </div>
    )}
  </div>
)

// 卡片内容组件
const CardContent = ({record}: {record: ProjectType}) => (
  <div className="content" key={`content-${record.id}`}>
    <Flex justify="space-around">
      <div className={`contentItem appInfo`}>
        {/* <Tooltip title={'应用id'}> */}
        <p className="b">
          <Tooltip title={`项目id ${record.id}`}>{record.id}</Tooltip>
        </p>
        <p>
          <Tooltip title={`ActId ${record.actId || `-`}`}>{record.actId > 0 ? record.actId : '-'}</Tooltip>
        </p>
        {/* <p>app</p> */}
        {/* </Tooltip> */}
      </div>
      {/* <div className={`contentItem`}>
        <Tooltip title={'ActId'}>
          <b style={{fontSize: `10px`}}>{record.actId > 0 ? record.actId : '-'}</b>
          <p>act</p>
        </Tooltip>
      </div> */}
      <div className={`contentItem partner`}>
        <EditorsList editors={record.editors} />
        {/* <p>Partner</p> */}
      </div>
      <TimeProgress record={record} />
    </Flex>
    {/* <Flex>
      <TagsList record={record} />
      <TimeProgress record={record} />
    </Flex> */}
  </div>
)

const dataRnder = (list: ProjectType[]) => {
  return list.map((record, index) => {
    return {
      title: <CardTitle record={record} />,
      avatar: <CardAvatar record={record} />,
      content: <CardContent record={record} />,
      actions: [
        <PreviewBtn key={`k-${index}-1`} data={record} />,
        <EditorBtn key={`k-${index}-2`} data={record} />,
        <CopyBtn key={`k-${index}-3`} data={record} />,
        <CollectBtn key={`k-${index}-4`} data={record} />,
        <RecomendBtn key={`k-${index}-5`} data={record} />,
      ],
    }
  })
}
export const SearchForm = () => {
  // const navigate = useNavigate()
  const appListState = appStore.state
  const userState = userStore.state
  const canAccess = userState.canAccess
  //
  let {channel}: any = useLoaderData()
  channel = channel ? channel : appListState.channel

  useEffect(() => {
    appListState.setChannel(channel)
    appListState.queryProjectList()
  }, [channel])

  return (
    <ProList
      ghost={true}
      loading={appListState.projectLoad}
      itemCardProps={{
        bordered: false,
        checked: false,
      }}
      pagination={{
        defaultPageSize: 12,
        showSizeChanger: true,
      }}
      //
      // rowKey="name"
      rowKey="id"
      rowSelection={false}
      rowClassName={'astroRowClassName'}
      /**
        xs?: ColumnCount;
        sm?: ColumnCount;
        md?: ColumnCount;
        lg?: ColumnCount;
        xl?: ColumnCount;
        xxl?: ColumnCount;
       */
      grid={{xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1}}
      metas={{
        title: {},
        subTitle: {},
        type: {},
        avatar: {},
        content: {},
        actions: {
          cardActionProps: 'actions',
        },
      }}
      toolBarRender={() => {
        return canAccess
          ? [
              <ToolBarFilter key={`ToolBarFilter`} />,
              <ToolBarSelectChannel key={'ToolBarSelectChannel'} channel={channel} />,
              <ToolBarSearch key={'ToolBarSearch'} />,
              <ToolBarAddProject key={'ToolBarAddProject'} />,
            ]
          : null
      }}
      headerTitle={<UnorderedListOutlined />}
      dataSource={dataRnder(appListState.filterList)}
    />
  )
}
export const SearchList = () => {
  return (
    <AstroPageContainer>
      <SearchForm key={'SearchForm'} />
    </AstroPageContainer>
  )
}
export default SearchList
