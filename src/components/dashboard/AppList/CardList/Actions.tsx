import {CopyOutlined, EditOutlined, EyeOutlined, <PERSON>Filled, HeartFilled, PlusOutlined} from '@astro/ui/icon'
import {Button, Input, Popconfirm, Radio, Select, Tooltip} from '@astro/ui/ui'
import {useNavigate} from 'react-router-dom'
import userStore from 'src/components/common/user/userStore'
import appStore from 'src/components/dashboard/AppStore'
import ProjectInfoForm from 'src/components/playload/ProjectInfoForm'
import config from 'src/config'
import type {ProjectType} from 'src/config/project'
export function PreviewBtn({data}: {data: ProjectType}) {
  // console.log('data.previewUrl', data.previewUrl)
  if (!data.previewUrl) return <></>
  return (
    <Tooltip title={'预览'}>
      <EyeOutlined
        onClick={(event: {stopPropagation: () => void}) => {
          event.stopPropagation()
          window.open(data.previewUrl, '_blank')
        }}
        key="editor"
      />
    </Tooltip>
  )
}
export function CopyBtn({data}: {data: ProjectType}) {
  const navigate = useNavigate()
  const store = appStore.state
  const userState = userStore.state
  return userState.user.role === 'A' || userState.user.role === 'T' || userState.user.role === 'P' ? (
    <Popconfirm
      title="复制项目"
      description={() => {
        return (
          <>
            <p>点击确定将直接创建复制项目。</p>
          </>
        )
      }}
      onConfirm={async (event: any) => {
        event.stopPropagation()
        const res = await store.doCopyProject(data.id)
        if (res) {
          navigate(`/projects/playload/${res}`)
        }
      }}
      onCancel={(event: any) => {
        event.stopPropagation()
      }}
      okText="确定"
      cancelText="取消"
    >
      <Tooltip title={'复制'}>
        <CopyOutlined key="copy" />
      </Tooltip>
    </Popconfirm>
  ) : null
}
export function RecomendBtn({data}: {data: ProjectType}) {
  const appListState = appStore.state
  const userState = userStore.state
  return userState.user.role === 'A' ? (
    <Tooltip key="HeartTwoTone" title={'推荐'}>
      <FireFilled
        style={{color: data.recommend ? '#eb2f96' : 'grey'}}
        // twoToneColor={data.recommend ? '#eb2f96' : 'grey'}
        onClick={(event: any) => {
          event.stopPropagation()
          appListState.doRecommend(data.id)
        }}
        key="recomend"
      />
    </Tooltip>
  ) : null
}
export function EditorBtn({data}: {data: ProjectType}) {
  const navigate = useNavigate()
  const userState = userStore.state
  return userState.user.role === 'A' || userState.user.role === 'T' || userState.user.role === 'P' ? (
    <Tooltip key="HeartTwoTone" title={'编辑'}>
      <EditOutlined
        onClick={(event: any) => {
          event.stopPropagation()
          navigate(`/projects/playload/${data.id}`)
        }}
        key="editor"
      />
    </Tooltip>
  ) : null
}

export function ToolBarSelectChannel({channel}: any) {
  const navigate = useNavigate()
  return (
    <div style={{width: '100%', textAlign: 'right'}} key="selectChannel">
      <Radio.Group
        onChange={e => {
          // console.log(e)
          // e.target.value !== 'my' ? navigate(`/?channel=${e.target.value}`) : navigate(`/`)
          navigate(`/?channel=${e.target.value}`)
        }}
        defaultValue={channel}
      >
        <Radio.Button value="all">全部</Radio.Button>
        <Radio.Button value="my">我的</Radio.Button>
        <Radio.Button value="recommend">推荐</Radio.Button>
        <Radio.Button value="collect">收藏</Radio.Button>
      </Radio.Group>
    </div>
  )
}

export function ToolBarSearch() {
  const appListState = appStore.state
  return (
    <Input.Search
      key="search"
      placeholder="查询应用名称"
      allowClear
      onSearch={(value: string) => {
        appListState.queryProjectList({search: value})
      }}
      enterButton
    />
  )
}

export function ToolBarAddProject() {
  const userState = userStore.state
  return userState.isLogin ? (
    <ProjectInfoForm
      refresh={true}
      key={'ProjectInfoForm'}
      trigger={
        <Button type="primary">
          <PlusOutlined />
        </Button>
      }
    />
  ) : null
}

export function ToolBarFilter() {
  const store = appStore.state
  return (
    <>
      <span style={{flexShrink: 0}}>项目类型</span>
      <Select
        style={{width: '120px', flexShrink: 0}}
        allowClear
        options={[
          {value: 1, label: '交友'},
          {value: 2, label: '语音房'},
        ]}
        placeholder="选择项目类型"
        onChange={store.setBusinessTypeFilter}
      />
      <span style={{flexShrink: 0}}>功能模块</span>
      <Select
        style={{width: '120px', flexShrink: 0}}
        allowClear
        // options={[
        //   {value: 1, label: '活动'},
        //   {value: 2, label: '玩法'},
        //   {value: 3, label: '功能'},
        //   {value: 4, label: '后台'},
        //   {value: 5, label: '协议'},
        // ]}
        options={config.projectTagOption}
        placeholder="选择功能模块"
        onChange={store.setTagsTypeFilter}
      />
      {/* <span style={{flexShrink: 0}}>项目状态</span>
      <Select
        style={{ width: '120px', flexShrink: 0 }}
        allowClear
        options={[
          { value: null, label: '永久' },
          { value: 0, label: '未开始' },
          { value: 1, label: '进行中' },
          { value: 2, label: '已结束' },
        ]}
        placeholder="选择功能模块"
        onChange={store.setStatusTypeFilter}
      /> */}
    </>
  )
}
export function CollectBtn({data}: {data: ProjectType}) {
  const appListState = appStore.state
  const userState = userStore.state
  return userState.user.role === 'A' || userState.user.role === 'T' || userState.user.role === 'P' ? (
    <Tooltip title={'收藏'}>
      <HeartFilled
        style={{color: data.isFavorite ? '#eb2f96' : 'grey'}}
        // twoToneColor={data.recommend ? '#eb2f96' : 'grey'}
        onClick={(event: any) => {
          event.stopPropagation()
          data.isFavorite ? appListState.cancelCollect(data.id) : appListState.doCollect(data.id)
        }}
        key="collect"
      />
    </Tooltip>
  ) : null
}
