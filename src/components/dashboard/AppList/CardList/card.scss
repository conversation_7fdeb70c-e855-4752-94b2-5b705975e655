.astroRowClassName {
    .ant-pro-checkcard-body {
        padding: 3px 8px !important;
    }

    .ant-pro-checkcard-content {
        // padding-inline: 6px;
        padding-block: 0;
        padding: 16px 16px 8px 16px;
    }

    .ant-pro-list-row-header {
        display: block;
    }

    // .ant-tag {
    //     font-size: 10px;
    // }

    .content {
        .ant-flex {
            height: 48px;
        }
    }

    .contentItem {
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
        text-align: center;
        display: flex;
        justify-content: center;
        flex-direction: column;

        p {
            padding: 0;
            margin: 0;
            font-size: 12px;
            color: #666;
        }

        b {
            font-size: 12px;
        }
    }

    .appInfo {

        text-align: left;

        p {
            font-size: 10px;
        }

        .b {
            font-weight: bold;
        }
    }

    .partner {
        line-height: 1;

        p {
            font-size: 10px;
        }
    }

    .tags {
        // flex: 1;
        align-items: center;
        display: flex;
        justify-content: space-between;
        flex-direction: row;

        .ant-tag {
            font-size: 10px;
            padding-inline: 2px;
            margin-inline-start: 2px;
            margin-inline-end: 2px;
            line-height: 12px;
            // border-radius: 10px;
        }
    }

    .time {
        // flex: 1;
        // padding: 0 20px 0;
        text-align: left;
        font-size: 10px;
        height: 50px;

        .never {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;

        }

        .ant-progress .ant-progress-text {
            font-size: 10px;
        }
    }

    .avatar {
        border-radius: 6px;
        width: 40px;
        height: 40px;
        margin-right: 10px;
        display: block;

        .ant-avatar-lg {
            width: 100%;
            height: 100%;
        }

        .ant-badge {
            top: 3px;
        }

    }

    .desc {
        font-size: 12px;
        font-weight: normal;
        color: #aaa;
    }

    .tags {
        font-size: 10px;
        color: #888;
    }

    .ant-pro-checkcard-title {
        align-items: flex-start;
    }

}

.avatar-tooltip {
    p {
        padding: 0;
        margin: 0;
    }
}