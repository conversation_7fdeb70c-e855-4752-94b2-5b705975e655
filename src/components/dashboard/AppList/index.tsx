import {UnorderedListOutlined, UserOutlined} from '@astro/ui/icon'
import {ProList} from '@astro/ui/pro'
import {Avatar, Badge, Col, Progress, Row, Space, Tag, Tooltip} from '@astro/ui/ui'
import dayjs from 'dayjs'
import {useEffect} from 'react'
import {useLoaderData, useNavigate} from 'react-router-dom'
import {AstroPageContainer} from 'src/components/common'
import userStore from 'src/components/common/user/userStore'
import appStore from 'src/components/dashboard/AppStore'
import {astroBusinessMap} from 'src/config/project'
import type {ProjectBusinessType} from 'src/config/project'
import {
  CopyBtn,
  EditorBtn,
  PreviewBtn,
  RecomendBtn,
  ToolBarAddProject,
  ToolBarSearch,
  ToolBarSelectChannel,
} from './Actions'
import css from './appList.module.scss'

export const getBizName = (business: any[], join = true) => {
  let cb: any = join ? '' : []
  if (business && business.length > 0) {
    cb = business.map((item: number) => astroBusinessMap[item as ProjectBusinessType])
    if (join) cb = cb.join('、')
  }
  return cb
}

// 参考 https://procomponents.ant.design/components/list
export const SearchForm = () => {
  const navigate = useNavigate()
  const appListState = appStore.state
  const userState = userStore.state
  const canAccess = userState.canAccess
  //
  let {channel}: any = useLoaderData()
  channel = channel ? channel : appListState.channel

  useEffect(() => {
    appListState.setChannel(channel)
    appListState.queryProjectList()
  }, [channel])

  return (
    <ProList
      loading={appListState.projectLoad}
      rowKey="name"
      pagination={{
        defaultPageSize: 20,
        showSizeChanger: true,
      }}
      metas={{
        title: {
          render(text, record, index) {
            return (
              <Space>
                {record.actName}
                {(getBizName(record.business, false) as string[]).map(v => (
                  <Tag style={{fontSize: `10px`}} key={`${v}-tag`} bordered={false} color="blue">
                    {v}
                  </Tag>
                ))}
              </Space>
            )
          },
        },
        avatar: {
          render(text, record, index) {
            return (
              <Tooltip title={record.creator}>
                <Avatar
                  shape="square"
                  icon={<UserOutlined />}
                  style={{backgroundColor: '#222'}}
                  size="large"
                  src={record.createAvatar}
                />
              </Tooltip>
            )
          },
        },
        description: {
          render(text, record, index) {
            return <div>{record.description}</div>
          },
        },
        content: {
          render(text, record, index) {
            const curTime = Date.now()
            const percent =
              curTime < record.beginTime
                ? 0
                : curTime > record.endTime
                  ? 100
                  : Math.floor(((curTime - record.beginTime) / (record.endTime - record.beginTime)) * 100)
            return (
              <Row key="moreContent" gutter={[8, 8]}>
                <Col className={css.contentItem} md={{span: 8}} xs={{span: 12}}>
                  {/* <Tooltip
                    placement="topLeft"
                    title={`创建时间：${dayjs(record.createTime).format('YYYY-MM-DD HH:mm')}`}
                  >
                    <span>Creator</span>
                    <p>{record.creator}</p>
                  </Tooltip> */}
                  <p>ActId {record.actId > 0 ? record.actId : '-'}</p>
                  <p>ProJectID {record.id}</p>
                </Col>
                <Col className={css.contentItem} md={{span: 8}} xs={{span: 12}}>
                  <span>参与者</span>
                  <div>
                    {record.editors && record.editors.length > 0 ? (
                      <Avatar.Group
                        max={{
                          count: 5,
                        }}
                      >
                        {record.editors.toReversed().map((editor: any, idx: number) => (
                          <Tooltip title={editor.passport} key={editor.uid}>
                            <Avatar
                              src={editor.avatar}
                              size="small"
                              style={{
                                cursor: 'pointer',
                              }}
                            />
                          </Tooltip>
                        ))}
                      </Avatar.Group>
                    ) : (
                      '-'
                    )}
                  </div>
                </Col>
                <Col className={css.contentItem} md={{span: 8}} xs={{span: 12}}>
                  {record.beginTime && record.endTime ? (
                    <Tooltip
                      placement="topLeft"
                      title={
                        <div>
                          {`开始时间：${dayjs(record.beginTime).format('YYYY-MM-DD HH:mm:ss')}`}
                          <br />
                          {`结束时间：${dayjs(record.endTime).format('YYYY-MM-DD HH:mm:ss')}`}
                        </div>
                      }
                    >
                      {/* <span>线上进度</span> */}
                      {/* <span
                      style={{
                        display: 'inline-block',
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        backgroundColor: record.status ? (record.status === 1 ? 'green' : 'red') : 'grey',
                        marginInlineEnd: 8,
                      }}
                    /> */}
                      {record.status ? (record.status === 1 ? '进行中' : '已结束') : '未开始'}
                      <Progress style={{width: '100%'}} percent={percent} />
                    </Tooltip>
                  ) : (
                    <>
                      <span>活动时间</span>
                      <p>-</p>
                    </>
                  )}
                </Col>
              </Row>
            )
          },
        },
        actions: {
          render(_, d, index) {
            return (
              <Space>
                <PreviewBtn data={d} />
                <EditorBtn data={d} />
                <CopyBtn data={d} />
                <RecomendBtn data={d} />
              </Space>
            )
          },
        },
      }}
      toolBarRender={() => {
        return canAccess
          ? [
              <ToolBarSelectChannel key={'ToolBarSelectChannel'} channel={channel} />,
              <ToolBarSearch key={'ToolBarSearch'} />,
              <ToolBarAddProject key={'ToolBarAddProject'} />,
            ]
          : null
      }}
      headerTitle={<UnorderedListOutlined />}
      dataSource={appListState.list}
    />
  )
}
export const SearchList = () => {
  return (
    <AstroPageContainer>
      <SearchForm />
    </AstroPageContainer>
  )
}
export default SearchList
