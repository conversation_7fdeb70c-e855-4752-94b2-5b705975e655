import {CopyOutlined, <PERSON>Outlined, <PERSON><PERSON><PERSON><PERSON><PERSON>, PlusOutlined, ZoomInOutlined} from '@astro/ui/icon'
import {Button, Input, Popconfirm, Radio, Tooltip} from '@astro/ui/ui'
import {useNavigate} from 'react-router-dom'
import userStore from 'src/components/common/user/userStore'
import appStore from 'src/components/dashboard/AppStore'
import ProjectInfoForm from 'src/components/playload/ProjectInfoForm'
import type {ProjectType} from 'src/config/project'
export function PreviewBtn({data}: {data: ProjectType}) {
  return (
    <Tooltip title={'预览'}>
      <Button
        onClick={(event: {stopPropagation: () => void}) => {
          event.stopPropagation()
          window.open(data.previewUrl, '_blank')
        }}
        key="editor"
        variant="filled"
        color="default"
        icon={<ZoomInOutlined />}
      />
    </Tooltip>
  )
}
export function CopyBtn({data}: {data: ProjectType}) {
  const navigate = useNavigate()
  const store = appStore.state
  const userState = userStore.state
  return userState.user.role === 'A' || userState.user.role === 'T' || userState.user.role === 'P' ? (
    <Popconfirm
      title="复制项目"
      description={() => {
        return (
          <>
            <p>点击确定将直接创建复制项目。</p>
          </>
        )
      }}
      onConfirm={async (event: any) => {
        event.stopPropagation()
        const res = await store.doCopyProject(data.id)
        if (res) {
          navigate(`/projects/playload/${res}`)
        }
      }}
      onCancel={(event: any) => {
        event.stopPropagation()
      }}
      okText="确定"
      cancelText="取消"
    >
      <Tooltip title={'复制'}>
        <Button key="editor" variant="filled" color="default" icon={<CopyOutlined />}></Button>
      </Tooltip>
    </Popconfirm>
  ) : null
}
export function RecomendBtn({data}: {data: ProjectType}) {
  const appListState = appStore.state
  const userState = userStore.state
  return userState.user.role === 'A' || userState.user.role === 'T' || userState.user.role === 'P' ? (
    <Tooltip key="HeartTwoTone" title={'推荐'}>
      <Button
        onClick={(event: any) => {
          event.stopPropagation()
          appListState.doRecommend(data.id)
        }}
        key="editor"
        variant="filled"
        color="default"
        icon={<HeartTwoTone twoToneColor={data.recommend ? '#eb2f96' : 'grey'} />}
      />
    </Tooltip>
  ) : null
}
export function EditorBtn({data}: {data: ProjectType}) {
  const navigate = useNavigate()
  const userState = userStore.state
  return userState.user.role === 'A' || userState.user.role === 'T' || userState.user.role === 'P' ? (
    <Tooltip key="HeartTwoTone" title={'编辑'}>
      <Button
        onClick={(event: any) => {
          event.stopPropagation()
          navigate(`/projects/playload/${data.id}`)
        }}
        key="editor"
        variant="filled"
        color="default"
        icon={<EditOutlined />}
      />
    </Tooltip>
  ) : null
}

export function ToolBarSelectChannel({channel}: any) {
  const navigate = useNavigate()
  return (
    <div style={{width: '100%', textAlign: 'right'}} key="selectChannel">
      <Radio.Group
        onChange={e => {
          // console.log(e)
          // e.target.value !== 'my' ? navigate(`/?channel=${e.target.value}`) : navigate(`/`)
          navigate(`/?channel=${e.target.value}`)
        }}
        defaultValue={channel}
      >
        <Radio.Button value="all">全部</Radio.Button>
        <Radio.Button value="my">我的</Radio.Button>
        <Radio.Button value="recommend">推荐</Radio.Button>
      </Radio.Group>
    </div>
  )
}

export function ToolBarSearch() {
  const appListState = appStore.state
  return (
    <Input.Search
      key="search"
      placeholder="查询应用名称"
      allowClear
      onSearch={(value: string) => {
        appListState.queryProjectList({search: value})
      }}
      enterButton
    />
  )
}

export function ToolBarAddProject() {
  const userState = userStore.state
  return userState.isLogin ? (
    <ProjectInfoForm
      key={'ProjectInfoForm'}
      trigger={
        <Button type="primary">
          <PlusOutlined />
        </Button>
      }
    />
  ) : null
}
