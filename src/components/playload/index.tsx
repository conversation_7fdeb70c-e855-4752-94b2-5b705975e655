import {
  CaretRightOutlined,
  ChromeOutlined,
  CloudUploadOutlined,
  CopyOutlined,
  HomeOutlined,
  Html5Outlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SaveFilled,
  SettingFilled,
  WindowsOutlined
} from '@astro/ui/icon'
import {B<PERSON>c<PERSON>b, Button, Divider, Flex, QRCode, Result, Tooltip, message} from '@astro/ui/ui'
import {memo, useEffect, useState} from 'react'
import {useParams} from 'react-router-dom'
import userStore from 'src/components/common/user/userStore'
import {PublishDrawer} from 'src/components/playload/PublishDrawer'
import EditPanel from 'src/components/playload/editPanel'
import pageStore from 'src/components/playload/editPanel/pageStore'
import type {ProjectType} from 'src/config/project'
import type {ISaveProjectParams} from 'src/services/material'
import {ApplyPermission} from '../common/layout/AstroLayout/Customer'
import {Loading} from '../common/status'
import projectStore from '../dashboard/AppStore'
import {CopyAndPasteModal} from './CopyAndPasteModal'
import {EnvControl} from './EnvControl'
import ProjectInfoForm from './ProjectInfoForm'
import {OnlineUsers, ResetPages} from './UserSocket'
import {OperationList} from './UserSocket/OperationList'
import {VersionModal} from './VersionModal'
import type {PlatformType} from './editPanel/types'
const QRCodeBox: any = QRCode
// const PreviewContent = () => {
//   const qrlink = `${config.renderOnline}/${pageStore.projectId}/${pageStore.pageKey}`
//   return (
//     <div>
//       <Space direction="vertical" align="center">
//         <QRCodeBox value={qrlink} />
//         <div>发布后可预览</div>

//         <Flex gap={10}>
//           {/* <Input readOnly={true} value={qrlink} size="small" /> */}
//           <Button
//             title="点击可复制h5移动端预览链接"
//             onClick={() => {
//               copy(`${qrlink}?device=h5`)
//               message.success('复制成功')
//             }}
//             icon={<Html5Outlined />}
//           ></Button>
//           <Button
//             title="点击可复制pcyy inner内嵌页预览链接"
//             onClick={() => {
//               copy(`${qrlink}?device=inner`)
//               message.success('复制成功')
//             }}
//             icon={<WindowsOutlined />}
//           ></Button>
//           <Button
//             title="点击可复制pc web浏览器预览链接"
//             onClick={() => {
//               copy(`${qrlink}?device=pc`)
//               message.success('复制成功')
//             }}
//             icon={<ChromeOutlined />}
//           ></Button>
//         </Flex>
//         <div>点击对应图标复制访问链接</div>
//       </Space>
//     </div>
//   )
// }
// const AppPreview = () => {
//   return (
//     <Tooltip key="preview_6" title={'预览'}>
//       <Popover content={PreviewContent}>
//         <Button variant="filled" color="default" icon={<EyeOutlined />} />
//       </Popover>
//     </Tooltip>
//   )
// }

// const cancel: PopconfirmProps['onCancel'] = e => {
// console.log(e)
// message.error('Click on No')
// }

const Playload = () => {
  const {id: projectId} = useParams()
  const userState = userStore.state
  const projectState = projectStore.state
  // const {id} = useParams()
  // console.log('Playload', id)
  // const navigator = useNavigate()
  useEffect(() => {
    if (projectId) projectState.checkPermission(projectId)
  }, [projectId])
  if (userState.userLoading) return <Loading />
  return userState.canAccess && projectState.hasPermission ? (
    <EditPanel />
  ) : (
    <Result
      status="403"
      title="403"
      subTitle={userState.isLogin === false ? '请登录后再试!' : '您没有访问权限!'}
    /* extra={
    <Button type="primary" onClick={() => navigator('/')}>
      Back Home
    </Button>
  } */
    />
  )
}
export default Playload
//

//
const AppSetting = () => {
  const pageState = pageStore.state
  const [, forceUpdate] = useState({})

  // useEffect(() => {
  //   console.log('###store', pageState.projectInfo.projectConfig?.urlParams)
  // }, [pageState.projectInfo.projectConfig?.urlParams])

  useEffect(() => {
    forceUpdate({})
  }, [pageState.projectInfo?.projectConfig?.urlParams])

  return (
    <ProjectInfoForm
      key="setting_4"
      info={pageStore.clone(pageState.projectInfo) as ProjectType}
      refresh={false}
      trigger={
        <Tooltip title={'应用设置'}>
          <Button variant="filled" color="default" icon={<SettingFilled />} />
        </Tooltip>
      }
    />
  )
}

// 项目内复制和粘贴，主要用于测试环境项目和正式环境项目配置互通，仅开发可见可操作，
// 复制的内容是项目的projectConfig，粘贴后调用saveProjectConfig接口保存完成项目配置同步
const AppCopyAndPaste = memo(() => {
  const store = pageStore.state
  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <>
      <Tooltip key="copyAndPaste_9" placement="bottom" title={`拷贝配置`}>
        <Button
          onClick={() => {
            setIsModalOpen(!isModalOpen)
          }}
          color="default"
          variant="filled"
          icon={<CopyOutlined />}
        />
      </Tooltip>
      <CopyAndPasteModal isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen} key={'CopyAndPasteModal'} />
    </>
  )
})

const AppVersion = memo(() => {
  const store = pageStore.state
  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <>
      <Tooltip
        key="version_5"
        placement="bottom"
        title={`${Number(store.curLockVersion) > 0
          ? `当前对外锁定版本（${store.curLockVersion}）`
          : `当前对外默认最新版本（${store.versionList?.[0]?.versionId || '无发布版本'}）`
          }`}
      >
        <Button
          onClick={() => {
            setIsModalOpen(!isModalOpen)
          }}
          color="default"
          variant="filled"
          icon={<CloudUploadOutlined />}
        />
      </Tooltip>
      <VersionModal isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen} key={'VersionModal'} />
    </>
  )
})

const MoreBtns = () => {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <Flex align="center" gap={8}>
      <Tooltip title="更多设置">
        <Button
          icon={
            isExpanded ? (
              <MenuFoldOutlined style={{fontSize: '12.5px'}} />
            ) : (
              <MenuUnfoldOutlined style={{fontSize: '12.5px'}} />
            )
          }
          onClick={() => setIsExpanded(!isExpanded)}
        />
      </Tooltip>
      {isExpanded && (
        <Flex gap={8} style={{transition: 'all 0.3s'}}>
          <AppCopyAndPaste key="copyAndPaste_9" />
          {/* <AppVersion key="version_5" /> */}
        </Flex>
      )}
    </Flex>
  )
}
//
const AppPublish = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const pageState = pageStore.state

  useEffect(() => {
    if (!pageState.projectId) return
    pageState.queryVersionList()
  }, [pageState.projectId])

  // 是否有新保存的数据，保存编辑时间大于发布时间，发布按钮高亮
  const [hasNewSaveData, setHasNewSaveData] = useState(false)

  useEffect(() => {
    const editTime = new Date(pageState?.projectInfo?.editTime ?? 0).getTime()
    const publishTime = new Date(pageState?.versionList?.[0]?.publishTime ?? 0).getTime()

    console.log('###editTime,publishTime', editTime, publishTime)
    setHasNewSaveData(editTime > publishTime)
  }, [pageState?.projectInfo?.editTime, pageState?.versionList])

  return (
    <>
      <Tooltip key="publish_8" title={hasNewSaveData ? '发布' : '暂无任何修改'}>
        <Button
          onClick={() => {
            setIsModalOpen(!isModalOpen)
          }}
          color="primary"
          variant="solid"
          icon={<CaretRightOutlined />}
        />
      </Tooltip>
      <PublishDrawer isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen} key={'PublishDrawer'} />
    </>
  )
}
//
const AppSave = () => {
  const pageState = pageStore.state
  const confirm = async () => {
    // console.log(e)
    // message.success('Click on Yes')
    const res: any = await pageState.saveProjectConfig()
    // console.log(`res`, res?.data?.reason)
    if (res?.data?.result == 200) {
      message.success('保存成功')

      // 保存后更新项目编辑时间
      const projectInfo: ISaveProjectParams = Object.assign({}, pageStore.clone(pageState.projectInfo), {
        editTime: res?.data?.data,
        // editTime: new Date().getTime(),
      })
      pageState.updateProjectInfo(projectInfo)
    } else {
      message.error(res?.data?.reason)
    }
  }

  return (
    <Tooltip key="publish_6" title={'保存'}>
      {/* <Popconfirm
  title="保存配置"
  description="确定保存所有配置?"
  onConfirm={confirm}
  onCancel={cancel}
  okText="确定"
  cancelText="取消"
> */}
      {!pageState.isFormOp ? (
        <Button
          color="default"
          variant="filled"
          icon={
            // <SaveOutlined />
            <SaveFilled />
          }
          disabled={true}
        />
      ) : (
        <Button
          loading={pageState.submitProjectConfig}
          color="primary"
          variant="solid"
          icon={<SaveFilled />}
          onClick={confirm}
        />
      )}
      {/* </Popconfirm> */}
    </Tooltip>
  )
}
const AppInfo = () => {
  const pageState = pageStore.state
  // return <>{pageState.projectInfo.actName}</>
  if (pageState.page && pageState.page.name) {
    return (
      <Breadcrumb
        style={{fontSize: '12px'}}
        items={[
          {
            href: '/',
            title: <HomeOutlined />,
          },
          {
            title: pageState.projectInfo.actName,
          },
          {
            title: (
              <>
                {/* <Tag icon={<LinkOutlined />} bordered={false}> */}
                {/* <Tag bordered={false}>{pageState.pageKey}</Tag> */}
                <Tooltip title={`pageKey ${pageState.pageKey}`}>{pageState.page.name}</Tooltip>
              </>
            ),
          },
        ]}
      />
    )
  }
  return (
    <Breadcrumb
      style={{fontSize: '12px'}}
      items={[
        {
          href: '/',
          title: <HomeOutlined />,
        },
        {
          title: pageState.projectInfo.actName,
        },
      ]}
    />
  )
}
//
const DeviceItem = ({platform, icon}: any) => {
  const store = pageStore.state
  const handlePlatform = (type: PlatformType) => {
    pageStore.setPlatform(type)
  }
  return (
    <Tooltip key="MobileOutlined_1" title={platform}>
      <Button
        variant="filled"
        color={store.platform === platform ? 'primary' : 'default'}
        icon={icon}
        onClick={() => handlePlatform(platform)}
      />
    </Tooltip>
  )
}
//
export const playloadOptions =
  ({isPlayload, isLogin, isFormOp, canAccess, hasPermission}: any) =>
    (): React.JSX.Element[] => {
      const arr = []
      if (isPlayload && canAccess && hasPermission) {
        arr.push(
          ...[
            // <RealTimeEditor key="RealTimeEditor" />,
            <div key="d0">
              <Divider type="vertical" />
            </div>,
            <AppInfo key="app_info" />,
            <div key="d1">
              <Divider type="vertical" />
            </div>,
            <DeviceItem key="MobileOutlined_1" platform={'h5'} icon={<Html5Outlined />} />,
            <DeviceItem key="WindowsOutlined_2" platform={'inner'} icon={<WindowsOutlined />} />,
            <DeviceItem key="ChromeOutlined_3" platform={'pc'} icon={<ChromeOutlined />} />,
            <div key="d1">
              <Divider type="vertical" />
            </div>,
            <EnvControl key="EnvControl" />,

            <div key="d1">
              <Divider type="vertical" />
            </div>,
            <OperationList key="OperationList" />,
            <OnlineUsers key="OnlineUsers" />,
            <ResetPages key="ResetPages" />,
            <div key="d1">
              <Divider type="vertical" />
            </div>,
            <MoreBtns key="more" />,
            <AppSetting key="setting_4" />,
            <AppSave key="save_7" />,
            <AppPublish key="publish_8" />,
          ],
        )
      } else if (isLogin && !canAccess) {
        arr.push(<ApplyPermission key="ApplyPermission" />)
      }
      return arr
    }
