import {message} from '@astro/ui/ui'
import {type OperationParams, getOperationList} from 'src/services/ws'
import {BaseStore} from 'src/store'
import type {RecordItem} from './type'
// 定义历史记录项的接口

export class HistoryStore extends BaseStore {
  list: RecordItem[] = [] // 历史操作的数组
  async getList(params: OperationParams, isInit = true) {
    const res: any = await getOperationList(params)
    if (res && res?.data?.code === 200) {
      const info: RecordItem[] = res?.data?.data?.history
      if (isInit) {
        this.list = info
      } else {
        this.list.push(...info)
      }
      return info
    } else {
      message.error(`历史记录 ${res?.data?.message ?? '获取失败'}`)
      return []
    }
  }
  // socket同步单条数据
  addRecordItem(item: RecordItem) {
    if (!item?.timestamp) {
      item.timestamp = new Date()?.getTime()
    }
    this.list.unshift(item)
  }
}
export default new HistoryStore()
