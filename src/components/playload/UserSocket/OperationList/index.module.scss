.tipsContainer {
    font-size: 12px;

    &:hover {
        background-color: transparent;
    }
}


.drawerContainer {
    :global {
        .ant-drawer-header {
            padding: 10px 16px;


            .ant-drawer-title {
                font-size: 12px;
                // font-weight: normal;
            }

        }

        .ant-drawer-body {
            padding: 10px;
        }

        .ant-timeline .ant-timeline-item-content {
            margin-inline-start: 10%;
        }

        //名字
        .ant-timeline-item-content span {
            // width: 50%;
        }

        //头像
        .ant-avatar.ant-avatar-sm.ant-avatar-circle.ant-avatar-image {
            // width: 32px;
            // height: 32px;
            // min-width: 32px; // 确保最小宽度
            // min-height: 32px; // 确保最小宽度
        }

        .ant-avatar-image img {
            // width: 32px;
            // height: 32px;
            // max-width: 100%;
            // max-height: 100%;
            // border-radius: 50%;
            // border: 2px solid rgba(24, 34, 43, 0.15);
        }

        // 操作
        .historyListFlex .desc {
            cursor: pointer;
            width: 200px;

            &:hover {
                color: rgb(9, 89, 180);
                font-weight: bold;
                text-decoration: underline;
            }
        }

        /* 去掉最后一个 Timeline.Item 的 padding-bottom */
        .ant-timeline .ant-timeline-item:last-child {
            padding-bottom: 0;
            /* 去掉底部间距 */
        }
    }

    .timelineContainer {
        height: 100%;
        overflow: hidden;

        .timeLineList {
            height: 100%;
            overflow: auto;
            @include scroll-bar(4px, rgb(91, 88, 88), 0.3, 0.7);
        }

        :global {
            .ant-timeline {
                margin: 0;
                padding: 0;

                .ant-timeline-item {
                    padding-bottom: 0;

                    .ant-timeline-item-tail {
                        display: block;
                        height: 100%;
                    }
                }
            }
        }

        .timelineItem {
            padding: 8px 16px;

            :global {
                .ant-timeline {
                    .ant-timeline-item {
                        padding-bottom: 40px;

                        .ant-timeline-item-head {
                            width: 28px;
                            height: 28px;
                            border: none;
                            font-size: 18px;
                            margin-top: 4px; // 添加上边距
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }

                        .ant-timeline-item-content {
                            margin-left: 30px;
                            min-height: auto;
                            padding-bottom: 0;
                        }

                        &:last-child {
                            padding-bottom: 24px;
                        }
                    }
                }
            }
        }
    }

    .historyItem {
        .name {
            // width: 130px;
            // font-weight: 500;
            color: #333;
            // font-size: 14px;
            line-height: 16px;
            font-size: 12px;
            // @include text-overflow();

            p {
                margin: 0;
                padding: 0;
            }
        }

        .time {
            color: #666;
            font-size: 10px;
        }

        .operation {
            width: fit-content;
            cursor: pointer;
            padding-inline: 3px;
            margin-inline-end: 0px;
            font-size: 10px;
            font-weight: normal;

            &:hover {
                opacity: 0.8;
            }
        }
    }

    .loadingWrapper {
        text-align: center;
        padding: 16px 0;
    }

}