import {
  ClockCircleOutlined,
  CloudSyncOutlined,
  DeleteOutlined,
  EditOutlined,
  FileAddOutlined,
  HistoryOutlined,
  PlusCircleOutlined,
  ProjectOutlined,
  ProjectTwoTone,
  PushpinOutlined,
  RollbackOutlined,
  SaveOutlined,
  SyncOutlined,
} from '@astro/ui/icon'
// 首先在顶部导入 Tag 组件
import {Avatar, Button, Drawer, Flex, Modal, Space, Spin, Tag, Timeline} from '@astro/ui/ui'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import {useCallback, useEffect, useRef, useState} from 'react'
import {useParams} from 'react-router-dom'
import config from 'src/config'
import type {OperationParams} from 'src/services/ws'
import historyStore from './historyStore'
import styles from './index.module.scss'
import 'dayjs/locale/zh-cn'
import Editor from '@monaco-editor/react'
import {FixedSizeList} from 'react-window'
import userStore from 'src/components/common/user/userStore'
import type {publish} from 'src/services/astro'
import type {ActTypeInfo, RecordItem} from './type'
// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')
const PAGE_SIZE = 30 // 定义每页数量常量

// 操作类型映射
const ActTypeMap: Record<string, ActTypeInfo> = {
  forceSync: {
    label: '强制同步',
    color: 'volcano',
    icon: <CloudSyncOutlined />,
  },
  removeComponent: {
    label: '移除组件',
    color: 'red',
    icon: <RollbackOutlined />,
  },
  addComponent: {
    label: '添加组件',
    color: 'geekblue',
    icon: <PlusCircleOutlined />,
  },
  updateComponentItem: {
    label: '更新组件属性',
    color: 'lime',
    icon: <EditOutlined />,
  },
  updateComponent: {
    label: '更新组件',
    color: 'green',
    icon: <SaveOutlined />,
  },
  updateComponents: {
    label: '更新所有组件',
    color: 'cyan',
    icon: <SyncOutlined />,
  },
  saveFormDataConfig: {
    label: '保存组件配置',
    color: 'orange',
    icon: <SaveOutlined />,
  },
  editPage: {
    label: '编辑页面',
    color: 'purple',
    icon: <EditOutlined />,
  },
  deletePage: {
    label: '删除页面',
    color: 'red',
    icon: <DeleteOutlined />,
  },
  addPage: {
    label: '添加页面',
    color: 'geekblue',
    icon: <FileAddOutlined />,
  },
  updateProjectInfo: {
    label: '更新项目',
    color: 'green',
    icon: <ProjectOutlined />,
  },
  saveProjectConfig: {
    label: '保存项目',
    color: 'gold',
    icon: <ProjectTwoTone />,
  },
  publish: {
    label: '发布',
    color: 'blue',
    icon: <PushpinOutlined />,
  },
  unknown: {
    label: '未知操作',
    color: 'magenta',
    icon: <HistoryOutlined />,
  },
}

export const OperationList = () => {
  const [open, setOpen] = useState(false)
  const {id: roomId} = useParams()
  const historyState = historyStore.state
  const [loading, setLoading] = useState(false) // 加载状态
  const page = useRef(1) // 当前页数
  const hasMoreInfo = useRef(true)
  const [visibleItems, setVisibleItems] = useState<any[]>([]) // 列表显示的数组
  const [op, setOp] = useState(null)

  const getQueryParams: () => OperationParams = useCallback(() => {
    return {
      env: config.isTest || config.isDev ? 'test' : 'prod',
      roomId: Number(roomId),
      start: (page.current - 1) * PAGE_SIZE, // 起始位置
      end: page.current * PAGE_SIZE - 1, // 结束位置
    }
  }, [page])

  //首次获取列表数据
  const initData = async () => {
    setLoading(true)
    try {
      const list: RecordItem[] = await historyState.getList(getQueryParams(), true)
      hasMoreInfo.current = list?.length >= PAGE_SIZE
      page.current = page.current + 1
    } catch (error) {
      console.error('获取历史记录失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    initData()
  }, [])

  // 统一处理时间戳转换
  const handleTimeStamp = (timestamp: any) => {
    const now = dayjs()
    const diffDays = now.diff(timestamp, 'day')
    const diffSeconds = dayjs().diff(timestamp, 'second')
    if (diffDays >= 1) {
      return timestamp.format('YYYY-MM-DD HH:mm:ss')
    } else {
      return diffSeconds < 60 ? (diffSeconds === 0 ? '刚刚' : `${diffSeconds} 秒前`) : dayjs(timestamp).fromNow()
    }
  }

  // list列表转换适配时间轴组件结构
  useEffect(() => {
    const list = historyState.list
    const formatItems = convertToTimelineItems(list)
    setVisibleItems(formatItems)
  }, [historyState.list, open])

  // 转换历史记录数组为时间轴组件的数据源
  // 在 convertToTimelineItems 函数中修改渲染部分
  const convertToTimelineItems = (historyList: RecordItem[]) => {
    return (
      historyList?.map(item => {
        const actInfo = ActTypeMap[item.act] || ActTypeMap.unknown
        const timestamp = dayjs(item.timestamp)
        const timeLabel = handleTimeStamp(timestamp)

        return {
          dot: actInfo.icon,
          color: actInfo.color,
          children: (
            <div key={timestamp.toString()}>
              <Flex vertical gap={2} className={styles.historyItem}>
                <Flex gap={5} align="center">
                  <Avatar
                    // size="small"
                    shape="square"
                    src={item?.user?.avatar}
                    alt={item?.user?.userName || '未知用户'}
                  />
                  <div className={styles.name} title={item?.user?.userName}>
                    <p>{item?.user?.userName || '未知'}</p>
                    <span className={styles.time} title={timestamp.format('YYYY-MM-DD HH:mm:ss')}>
                      {timeLabel}
                    </span>
                  </div>
                </Flex>
                <Space style={{color: `#666`, fontSize: `10px`}}>
                  {item.act.charAt(0).toUpperCase() + item.act.slice(1)}
                  <Tag
                    bordered={false}
                    color={actInfo.color}
                    className={styles.operation}
                    onClick={() => {
                      setOp(item?.op)
                    }}
                  >
                    {actInfo.label}
                  </Tag>
                </Space>
              </Flex>
            </div>
          ),
        }
      }) || []
    )
  }

  //加载更多
  const loadMore = useCallback(async () => {
    //如果正在加载或者没有下一页了，就不再发送接口
    if (loading || !hasMoreInfo.current) return
    setLoading(true)

    try {
      const params = getQueryParams()
      const list: RecordItem[] = await historyState.getList(getQueryParams(), false)
      hasMoreInfo.current = list?.length >= PAGE_SIZE
      page.current = page.current + 1
    } catch (error) {
      console.error('加载更多数据失败:', error)
    } finally {
      setLoading(false)
    }
  }, [page, hasMoreInfo])

  // 关闭 详细记录Modal
  const handleCancel = () => {
    setOp(null) // 可选：清空 op
  }

  const Tips = () => {
    const lastedInfo = historyState.list?.[0]
    return lastedInfo ? (
      <Flex
        className={styles.tipsContainer}
        wrap={10}
        vertical
        title={'操作历史记录'}
        onClick={() => {
          setOpen(true)
        }}
      >
        <Space style={{fontSize: `10px`}}>
          {/* <ClockCircleOutlined style={{fontSize: `14px`}} /> */}
          <Avatar shape="square" src={lastedInfo?.user?.avatar} />
          <div style={{lineHeight: `15px`}}>
            <span>{lastedInfo?.user?.userName}</span>
            <br />
            {dayjs(lastedInfo?.timestamp).fromNow()}
          </div>
        </Space>
      </Flex>
    ) : null
  }

  return (
    <>
      <Tips />
      <Drawer
        key={open ? 'fresh' : 'old'}
        className={styles.drawerContainer}
        width="250px"
        title="操作历史"
        onClose={() => {
          setOpen(false)
          page.current = 1
        }}
        destroyOnClose
        open={open}
      >
        <div className={styles.timelineContainer}>
          <Modal
            destroyOnClose
            open={op !== null}
            onCancel={handleCancel}
            width={
              {
                xs: '90%',
                sm: '90%',
                md: '80%',
                lg: '80%',
                xl: '70%',
                xxl: '70%',
              } as any
            }
            footer={
              <Button type="primary" onClick={handleCancel}>
                关闭
              </Button>
            }
          >
            <Editor
              language={'json'}
              width={'100%'}
              height={'500px'}
              value={JSON.stringify(op)}
              onMount={editor => {
                setTimeout(() => {
                  editor.getAction('editor.action.formatDocument')?.run()
                }, 0)
              }}
            />
          </Modal>
          <FixedSizeList
            className={styles.timeLineList}
            height={840}
            itemCount={visibleItems.length}
            itemSize={80} // 从 50 调整为 80
            width={'100%'}
            itemData={visibleItems}
            useIsScrolling
            onScroll={({
              scrollOffset,
              scrollUpdateWasRequested,
            }: {scrollOffset: number; scrollUpdateWasRequested: boolean}) => {
              if (!scrollUpdateWasRequested) {
                const scrollHeight = visibleItems.length * 50
                const remainingScroll = scrollHeight - scrollOffset - 850
                if (remainingScroll < 100 && !loading) {
                  loadMore()
                }
              }
            }}
          >
            {({index, style, data}: {index: number; style: React.CSSProperties; data: typeof visibleItems}) => {
              const item = data[index]
              return (
                <div style={style} className={styles.timelineItem}>
                  <Timeline>
                    <Timeline.Item dot={item.dot} color={item.color}>
                      {item.children}
                    </Timeline.Item>
                  </Timeline>
                </div>
              )
            }}
          </FixedSizeList>
          {loading && (
            <div className={styles.loadingWrapper}>
              <Spin tip="加载中..." />
            </div>
          )}
        </div>
      </Drawer>
    </>
  )
}
