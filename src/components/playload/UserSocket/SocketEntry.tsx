import {App} from '@astro/ui/ui'
import {useEffect} from 'react'
import userStore, {type UserStore, type UserType} from 'src/components/common/user/userStore'
import uSocket, {logger} from 'src/components/playload/UserSocket/uSocket'
import pageStore, {type PageStore} from 'src/components/playload/editPanel/pageStore'
// import {subscribe} from 'valtio'
import {pagesBus} from 'src/utils/eventBus'
import {subscribeKey} from 'valtio/utils'
import historyStore from './OperationList/historyStore'
import pageCentral from './pageCentral'
//
const connect = (id: string, userInfo: UserType) => {
  uSocket.setup({id, userInfo}).connect()
}
/**
 * 历史补充事件、非实时同步、需要同步状态的事件 如
 */
const historyActions: {[key: string]: any} = {
  /**
   *  * saveProjectConfig 调用 api、成功保存后需要通过 history 广播到各个用户进行同步、并清除操作记录、屏蔽保存按钮
   */
  saveProjectConfig() {
    pageStore.removeFormOp()
  },
}
//
export const SocketEntry = ({projectId}: {projectId: string}) => {
  const pageState = pageStore.state
  const userState = userStore.state
  let modalInstance: any
  const {modal} = App.useApp()
  useEffect(() => {
    if (!userState.user.uid || !projectId) return
    logger.debug('userState.user.uid', userState.user.uid, projectId)
    //
    let unsubscribe: any
    let funsubscribe: any
    let unSubscribeKeyPageKey: any
    let unSubscribeKeyComponentKey: any
    let unSubscribeHistory: any
    //
    const reconnect = () => {
      if (!uSocket.isConnected && projectId && userState.user.uid) {
        logger.debug('reconnect', userState.user.uid, projectId)
        connect(projectId, userStore.clone(userState.user))
      }
    }
    //
    if (projectId && userState.user.uid) {
      connect(projectId, userStore.clone(userState.user))
      //
      uSocket.readyEvent = () => {
        const {pageKey, componentKey} = pageStore
        logger.debug('readyEvent', pageKey, componentKey)
        uSocket.sendJoinRoom({componentKey, pageKey})
      }
      //
      uSocket.openEvent = d => {
        logger.debug('modalInstance openEvent', d)
        const {roomPages} = d
        const {pages, createTime, user} = roomPages
        // logger.debug('openEvent roomPages', user)
        userStore.setEditorUser(user || {}, roomPages.createTime)

        uSocket.roomPages = {pages, createTime}
        pagesBus.publish('fnishGetProjectPagesAction')
        if (modalInstance && modalInstance.destroy) {
          modalInstance.destroy()
          modalInstance = undefined
        }
      }
      uSocket.closeEvent = d => {
        const status = uSocket.closeStatus
        console.log('modalInstance', d, uSocket.reconnect, status)
        if (uSocket.reconnect > 0 && modalInstance) {
          const reNum = uSocket.reconnectMax - uSocket.reconnect
          modalInstance.update({
            content:
              reNum > 0 ? `正在重连,剩余${uSocket.reconnectMax - uSocket.reconnect}次` : `无法连接、请稍候再试！`,
          })
        } else {
          let title = `webSocket 连接异常 ${d.code}`
          let content = '正在分析错误信息...'
          switch (status) {
            case 'server_error':
              content = '系统即将自动重连...'
              break
            case 'auth_faild':
              title = `webSocket 无权访问 ${d.code}`
              content = '请登录后再试、如登录有仍有问题、请联系管理员!'
              break
            case 'client_id_need':
              title = `webSocket 版本异常 ${d.code}`
              content = '缺少 client_id 参数、请清理缓存再试!'
              break
          }
          modalInstance = modal.warning({
            title,
            content,
            okText: '重新连接',
            // okButtonProps: {style: {display: status === 'server_error' ? 'none' : 'block'}},
            okButtonProps: {style: {display: 'none'}},
            onOk: () => {
              reconnect()
            },
          })
        }
      }
      //
      uSocket.onlineEvent = users => {
        logger.debug('online ', users)
        userState.onlineUsers(users)
      }
      // 同步所有状态
      uSocket.historyEvent = d => {
        logger.debug('historyEvent', d, d.act)
        if (d.user) {
          userStore.setEditorUser(d.user, Date.now())
        }
        if (d.act) {
          historyStore.addRecordItem({...d})
          // 通过 history 触发没实时同步事件的 补充函数
          if (historyActions[d.act]) {
            historyActions[d.act]()
          }
        }
      }
      // 只同步非自己的信息
      uSocket.pagesEvent = async (d: any) => {
        pageCentral.sync = true
        logger.debug('pages pagesEvent', d, d.act)
        const {act, op} = d
        if (act && op) {
          const fn = (pageState as any)[act]
          await fn(...op)
        }
        pageCentral.sync = false
      }
      unsubscribe = pagesBus.subscribe('pages', d => {
        userStore.setOwnEditorUser()
        uSocket.sendPages(d)
      }).unSubscribe
      // unsubscribe = cb.unSubscribe
      // 合并数据到最新 pages 没请求到数据以及没建立socket连接都不处理
      funsubscribe = pagesBus.subscribe('fnishGetProjectPagesAction', async () => {
        // uSocket.sendPages(d)

        // if (Object.keys(pageStore.pages).length === 0 || !uSocket.isConnected) return
        if (!uSocket.isConnected) return
        const {roomPages} = uSocket
        /**
         * NOTE 利用 入口确保pages 请求完再进来 socket
         * 补充请求 pages 避免时序问题导致 错误判断
         * 如果 pages 确实为空 而 socket 不为空 则全部标红
         */
        // if (
        //   Object.keys(pageStore.pages).length === 0 &&
        //   pageStore.projectId &&
        //   roomPages &&
        //   roomPages.pages &&
        //   Object.keys(roomPages.pages).length > 0
        // ) {
        //   await pageState.getProjectPagesAction(pageStore.projectId)
        // }
        /**
         * 把 pageStore 同步都 socket
         */
        if (!roomPages.createTime || roomPages.createTime === 0) {
          roomPages.createTime = Date.now()
          roomPages.pages = pageStore.clone(pageStore.pages)
          pageCentral.forceSync(roomPages)
        } else {
          logger.debug('fnishGetProjectPagesAction roomPages syncPages', roomPages)
          pageState.syncPages(roomPages.pages)
        }
        const {pageKey, componentKey} = pageStore
        logger.debug('pageKey', pageKey, pageState.pageKey)
        logger.debug('componentKey', componentKey, pageState.componentKey)
        // 已存在的 pageKey
        // logger.debug('roomPages.pages', Object.keys(roomPages.pages), Object.keys(roomPages.pages).includes(pageKey))
        if (roomPages.pages && !roomPages.pages[pageKey]) {
          pageState.setDefaultPageIndex(roomPages.pages)
        }
      }).unSubscribe
      // funsubscribe = fcb.unSubscribe
      //
      unSubscribeKeyPageKey = subscribeKey(pageStore, 'pageKey', () => {
        const {pageKey, componentKey} = pageStore
        logger.debug('pageKey', pageKey, componentKey)
        uSocket.send({action: 'option', componentKey, pageKey})
      })
      unSubscribeKeyComponentKey = subscribeKey(pageStore, 'componentKey', () => {
        const {pageKey, componentKey} = pageStore
        logger.debug('componentKey', pageKey, componentKey)
        uSocket.send({action: 'option', componentKey, pageKey})
      })
      //
      unSubscribeHistory = pagesBus.subscribe('history', d => {
        // userStore.setOwnEditorUser()
        // uSocket.sendPages(d)
        console.log('pagesBusHistory', d)
        uSocket.sendHistory(d)
      }).unSubscribe
      // tab重新激活时 连接 webSocket
      document.addEventListener('visibilitychange', reconnect)
    }

    return () => {
      if (!userState.user.uid || !projectId) return
      //
      document.removeEventListener('visibilitychange', reconnect)
      //
      logger.debug('uSocket disConnect')
      uSocket.disConnect()
      //
      unsubscribe && unsubscribe()
      unsubscribe = undefined
      //
      funsubscribe && funsubscribe()
      funsubscribe = undefined
      //
      unSubscribeKeyPageKey && unSubscribeKeyPageKey()
      unSubscribeKeyPageKey = undefined
      //
      unSubscribeKeyComponentKey && unSubscribeKeyComponentKey()
      unSubscribeKeyComponentKey = undefined
      //
      unSubscribeHistory && unSubscribeHistory()
      unSubscribeHistory = undefined
      //
      uSocket.resetRoomPages()
    }
  }, [userState.user.uid, projectId])

  return <></>
}

export default SocketEntry
