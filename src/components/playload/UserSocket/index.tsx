import {RedoOutlined} from '@astro/ui/icon'
import {Avatar, Button, Popconfirm, Tooltip} from '@astro/ui/ui'
import {memo, useEffect} from 'react'
import userStore from 'src/components/common/user/userStore'
import config from 'src/config'
import pageStore from '../editPanel/pageStore'
type Props = {
  pageKey?: string // 用户当前选中的页面
  componentKey?: number // 用户当前选中的组件
  size?: number // 头像尺寸
  maxCount?: number // 最大显示头像
}
const {isOpenSocket} = config

export const OnlineUsers = (props: Props) => {
  if (!isOpenSocket) return <></>
  const {pageKey = '', componentKey = -1, size, maxCount = 5} = props
  const userState = userStore.state
  const userList = userState.getOnlineUserList(pageKey, componentKey)

  useEffect(() => {
    // console.log('getOnlineUserList', userList)
  }, [userList, userState.users])
  return userList.length > 0 ? (
    <Avatar.Group
      size={size}
      max={{
        count: maxCount,
        style: {color: '#f56a00', backgroundColor: '#fde3cf'},
      }}
    >
      {userList.map(u => {
        return (
          <Tooltip key={u.uid} title={u.userName} placement="top">
            {u.avatar ? <Avatar src={u.avatar} /> : <Avatar style={{backgroundColor: '#f56a00'}}>{u.nickname}</Avatar>}
          </Tooltip>
        )
      })}
    </Avatar.Group>
  ) : (
    <></>
  )
}
export default memo(OnlineUsers)
export const resetPagesData = async () => {
  if (!pageStore.projectId) return
  await pageStore.getProjectPagesAction(pageStore.projectId, false, true)
  await pageStore.getComponentForm()
}
export const ResetPages = () => {
  return isOpenSocket ? (
    <Popconfirm onConfirm={resetPagesData} description={'重置到操作前的版本'} title={'确认重置'}>
      {/* <Tooltip title={'重置到操作前的版本'}> */}
      <Button title="回撤到上一次保存数据" variant="filled" color="default" icon={<RedoOutlined />} />
      {/* </Tooltip> */}
    </Popconfirm>
  ) : (
    <></>
  )
}
