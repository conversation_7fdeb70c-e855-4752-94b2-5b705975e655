// import {useEffect} from 'react'
// import userStore from 'src/components/common/user/userStore'
// import {proxy, useSnapshot} from 'valtio'
// import {bind} from 'valtio-yjs'
// import {WebsocketProvider} from 'y-websocket'
// import * as Y from 'yjs'
// const ydoc = new Y.Doc()
// const ymap = ydoc.getMap('astro.users')
// // const mesgMap = proxy({} as Record<string, string>)

// export const YSocket = ({projectId, userState}: any) => {
//   useEffect(() => {
//     const ws = new WebsocketProvider('ws://localhost:1234', 'roomid', ydoc, {
//       connect: false,
//     })
//     ws.connect()
//     const unbind = bind(userStore as any, ymap)
//     ws.on('status', (event: any) => {
//       console.log(event.status) // logs "connected" or "disconnected"
//       if (event.status === 'connected') {
//         userState.onlineUserAdd(userState.user)
//       }
//       if (event.status === 'disconnected') {
//         userState.onlineUserAdd(userState.user)
//       }
//     })
//     ws.on('sync', (e: any) => {
//       console.log('sync', e)
//     })
//     ws.on('connection-close', (e: any) => {
//       console.log('connection-close', e)
//     })
//     ws.on('connection-error', (e: any) => {
//       console.error('connection-error', e)
//     })
//     return () => {
//       ws.disconnect()
//       unbind()
//     }
//   }, [userState.user.uid, projectId])
//   return <></>
// }

// export default YSocket
