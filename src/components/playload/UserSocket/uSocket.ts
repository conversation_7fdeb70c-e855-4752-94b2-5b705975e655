import config from 'src/config'
import {Logger} from 'src/utils/logger'
export type CloseStatusType = 'auth_faild' | 'client_id_need' | 'server_error'
//
export const logger = new Logger({channel: 'uSocket', level: config.isDev ? 'debug' : 'info'})
export class USocket {
  ws!: WebSocket
  isConnected = false
  reconnect = 0
  reconnectMax = 10
  roomId = 0
  uid = 0
  userInfo: any = {}
  clientId: string = this.generateClientId() // 添加客户端唯
  closeStatus?: CloseStatusType
  setup({
    id,
    userInfo,
    // onlineEvent, pagesEvent, readyEvent
  }: any) {
    this.roomId = id
    this.userInfo = userInfo
    this.uid = userInfo.uid
    return this
  }
  disConnect() {
    logger.debug('disConnect', this.isConnected, this.ws?.close)
    if (this.isConnected) this.ws?.close()
  }
  connect() {
    const ws = new WebSocket(config.getSocketByRoomId(this.roomId))
    this.ws = ws
    ws.onopen = this.onopen
    ws.onclose = this.onclose
    ws.onmessage = this.onmessage
    ws.onerror = this.onerror
  }
  // 生成客户端唯一标识
  generateClientId() {
    return `client-${Date.now()}-${this.uid}-${Math.random().toString(36).substring(2, 9)}`
  }

  sendJoinRoom({componentKey, pageKey}: any) {
    this.send({action: 'joinRoom', componentKey, pageKey})
  }
  onopen = (e: Event) => {
    logger.info('connected', e)
    this.isConnected = true
    this.reconnect = 0
    this.readyEvent(e)
  }
  onclose = (e: CloseEvent) => {
    logger.warn('disconnected', e, e.code)
    //
    if (e.code === 403) {
      this.closeStatus = 'auth_faild'
      logger.error('close', '登录信息有误!')
    } else if (e.code === 400) {
      this.closeStatus = 'client_id_need'
      logger.error('close', 'clientId 不能为空!')
    }
    /**
     * 关闭时间白名单
     * 1005 正常关闭
     * 1006 非正常关闭
     * 403 登录信息有误
     * 400 clientId 不能为空
     */
    if ([1006, 403, 400].includes(e.code)) {
      this.closeEvent(e)
    }
    this.isConnected = false
    //
    if ([1006].includes(e.code) && this.reconnect < this.reconnectMax) {
      this.closeStatus = 'server_error'
      this.reconnect++
      setTimeout(() => {
        this.connect()
      }, 1000)
    }
  }
  onmessage = (e: MessageEvent) => {
    const d = this.toJSON(e.data)
    logger.debug('message', d.action)
    if (d.action === 'online') {
      logger.debug('uSocket online', d)
      this.onlineEvent(d.users)
    } else if (d.action === 'drain') {
    }
    //多开导致误删的情况
    else if (d.action === 'close' && d.uid === this.uid) {
      this.send({action: 'joinRoom', ...this.userInfo})
    } else if (d.action === 'pages') {
      // logger.debug('pages', d.sendId, d.sendTime, d.roomId)
      this.historyEvent(d)
      if (this.clientId !== d.clientId) this.pagesEvent(d)
    } else if (d.action === 'open') {
      logger.debug('open', d)
      this.openEvent(d)
    } else if (d.action === 'history') {
      logger.debug('history', d)
      this.historyEvent(d)
    }
  }
  // pageMsgId = ''
  sendPages(d: any) {
    // this.pageMsgId = `${this.roomId}-${this.uid}-${Date.now()}`
    this.send({
      action: 'pages',
      //  pageMsgId: this.pageMsgId,
      ...d,
    })
  }
  sendHistory(d: any) {
    this.send({
      action: 'history',
      ...d,
    })
  }
  onerror = (e: Event) => {
    this.isConnected = false
    console.error(e)
    this.ws.close()
  }
  onlineEvent = (users: any) => {}
  pagesEvent = (d: any) => {}
  historyEvent = (d: any) => {}
  openEvent = (d: any) => {}
  closeEvent = (d: any) => {}
  readyEvent = (d: any) => {}
  toString(d: object) {
    let cb
    try {
      cb = JSON.stringify(d)
    } catch (e) {
      cb = '{}'
    }
    return cb
  }
  send(d: object) {
    if (this.isConnected && this.ws.send && this.ws.readyState) {
      const sendMsg = this.toString({
        ...d,
        clientId: this.clientId,
        // roomId: this.roomId,
        // sendId: this.uid,
        // sendTime: Date.now(),
      })
      // logger.debug(sendMsg)
      this.ws.send(sendMsg)
    }
  }
  toJSON(d: string) {
    let cb
    try {
      cb = JSON.parse(d)
    } catch (e) {
      cb = {}
    }
    return cb
  }
  roomPages: any = {
    pages: {},
    createTime: 0,
  }
  resetRoomPages() {
    this.roomPages = {
      pages: {},
      createTime: 0,
    }
  }
}
export default new USocket()
