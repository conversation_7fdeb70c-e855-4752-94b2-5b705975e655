import {pagesBus} from 'src/utils/eventBus'
import pageStore from '../editPanel/pageStore'
export class PageCentral {
  sync = false
  removeComponent(k: number, pk: string) {
    pageStore.setFormOp(pk)
    this.send({op: [k, pk], act: 'removeComponent'})
  }
  addComponent(component: any, pk: string) {
    pageStore.setFormOp(pk)
    this.send({op: [component, pk], act: 'addComponent'})
  }
  updateComponentItem(k: number, v: any, pk: string, ck: number) {
    pageStore.setFormOp(pk)
    this.send({op: [k, v, pk, ck], act: 'updateComponentItem'})
  }
  updateComponent(component: any, pk: string, ck: number) {
    pageStore.setFormOp(pk)
    this.send({op: [component, pk, ck], act: 'updateComponent'})
  }
  updateComponents(components: any[], pk: string) {
    pageStore.setFormOp(pk)
    this.send({op: [components, pk], act: 'updateComponents'})
  }
  // d, pk, ck, sformType
  saveFormDataConfig(d: any, pk: string, ck: number, sformType: 'component' | 'page') {
    // console.log(`[version] saveFormDataConfig`, d)
    pageStore.setFormOp(pk, ck)
    this.send({op: [d, pk, ck, sformType], act: 'saveFormDataConfig'})
  }
  editPage(k: string, v: any, oldKey?: string, extend = false) {
    pageStore.setFormOp(k)
    this.send({op: [k, v, oldKey, extend], act: 'editPage'})
  }
  addPage(k: string, v: any) {
    pageStore.setFormOp(k)
    this.send({op: [k, v], act: 'addPage'})
  }
  deletePage(k: string) {
    pageStore.setFormOp(k)
    this.send({op: [k], act: 'deletePage'})
  }
  forceSync(roomPages: any) {
    // for (const k in roomPages.pages) {
    //   pageStore.setFormOp(k, -1)
    // }
    this.send({op: [roomPages], act: 'forceSync'})
  }
  updateProjectInfo(k: string) {
    this.send({op: [k], act: 'updateProjectInfo'})
  }
  send(o: any) {
    if (!this.sync) pagesBus.publish('pages', o)
  }
}
export default new PageCentral()
