.popover {
    :global {
        .ant-popover {
            z-index: 1000;
        }
    }
}

.cascader {
    :global {
        .ant-cascader-dropdown {
            z-index: 1400;
        }

        .ant-popover {
            z-index: 1500 !important;
        }
    }
}

.agentCard {
    width: 300px;

    .bgCard {
        position: absolute;
        // top: 0;
        left: 0;
        background: url(https://hd-static.yystatic.com/9220987787019592.png);
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        opacity: 0.2;
        height: -webkit-fill-available;
        width: -webkit-fill-available;
    }

    .ant-form-item {
        margin-bottom: 0px;
    }

}

.showHtmlCascader {
    display: flex;
    max-width: 600px;
    word-wrap: break-word;

    border-radius: 8px;
    border: 1px solid #ccc;
    padding: 2px 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    overflow: auto;
}

.previewImage {
    width: 300px;
}