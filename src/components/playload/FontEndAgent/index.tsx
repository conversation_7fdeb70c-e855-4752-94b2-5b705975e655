import {BaiduOutlined, CloseOutlined} from "@astro/ui/icon"
import {<PERSON><PERSON>, Card, Cascader, type CascaderProps, Flex, FloatButton, type GetProp, Image, Popover, Select, Space, message} from "@astro/ui/ui"
import {useEffect, useMemo, useState} from "react"
import {TextArea} from "src/components/common/Editor"
import AuthFigma from "src/components/common/FrontEndAi/AuthFigma"
import figmaStore from "src/components/common/FrontEndAi/store"
import {getCode, getCodeSign, getPreviewImage, getSearchParams} from "src/services/f2cFigma"
import f2cStore from "../editPanel/f2cStore"
import pageStore from "../editPanel/pageStore"
import './index.scss'
type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]
const urlReg = /^((https?):\/\/)?([\w-]+(\.[\w-]+)*)(:\d+)?(\/[\w\u4e00-\u9fa5\-\.\/?%&=]*)?$/
const typeOp = [{
  label: "打标",
  value: "1",
}, {
  label: "页面",
  value: "2",
}]
interface Option {
  key: string
  value: string
  label: string
  itemKey?: string
  targetValue?: any
  children?: Option[]
}
function isHtmlStr(str: string): boolean {
  const htmlRegex = /<[^>]+>/
  return htmlRegex.test(str)
}
function findF2cConfigData(obj: any): any {
  if (obj && typeof obj === 'object') {
    // 检查当前对象是否包含 f2c_config_data
    if (obj.f2c_config_data !== undefined) {
      return obj.f2c_config_data
    }

    // 递归检查对象的所有属性
    for (const key in obj) {
      // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
      if (obj.hasOwnProperty(key)) {
        const result = findF2cConfigData(obj[key])
        if (result !== undefined) {
          return result
        }
      }
    }
  }

  // 如果没有找到，返回 undefined
  return undefined
}

const FontEndAgent = () => {
  const pageState = pageStore.state
  const figmaState = figmaStore.state
  const f2cState = f2cStore.state

  const [init, setInit] = useState(true)
  const [previewUrl, setPreviewUrl] = useState('')
  const [loading, setLoading] = useState(false)
  const [openPopover, setOpenPopover] = useState(false)

  const [figmaLink, setFigmaLink] = useState('')
  const [selType, setSelType] = useState('2')
  const [selCom, setSelCom] = useState('')
  const [selHtml, setSelHtml] = useState('')
  const [comHtmlInfo, setComHtmlInfo] = useState({"uniqueNum": '', "path": []})
  const [callBackMsg, setCallBackMsg] = useState('')
  const [allForm, setAllForm] = useState([])

  const hide = () => {
    setCallBackMsg('')
    setPreviewUrl('')
    setOpenPopover(false)
  }
  const selOpCom = useMemo(() => {
    const arr = []
    if (!('path' in pageState.page) && !pageState.page?.components?.length) {
      arr.push({key: 'Empty', label: '无', value: 'Empty'})
    } else {
      arr.push({key: 'ALL', label: '全部', value: 'ALL'})
      if ('path' in pageState.page) {
        arr.push({key: 'page', label: pageState.page.name || '', value: `pageKey_${pageState.page.pageKey}` || ''})
      }
      if (pageState.page?.components?.length) {
        pageState.page.components?.forEach((component, index) => {
          arr.push({key: `com_${index}`, label: component.name || '', value: component.uniqueNum || ''})
        })
      }
    }
    return arr
  }, [pageState.page])


  //--------------
  const isRelevantDataConfig = (type: any): boolean => {
    return (
      type === 'AstroTypes.HtmlF2CId' ||
      type === 'AstroTypes.RichEditor' ||
      type === 'AstroTypes.FrontEndAi' ||
      type === 'AstroTypes.Code'
    )
  }
  const getOptionItem = (form: any, com: any, id: string, firstOn = false) => {
    const ops: Option[] = []
    let i = 0
    for (const key in form) {
      const formItem: any = form[key]
      const comItem: any = com[key]
      if (isRelevantDataConfig(formItem.type) || formItem.type === 'AstroTypes.Group') {
        const idOp = `${id}-${i++}`
        const item: Option = {key: idOp, value: idOp, label: formItem.name, itemKey: key}
        if (formItem.type === 'AstroTypes.Group') {
          const children = []
          for (let i = 0; i < comItem?.length; i++) {
            const comItemC = comItem[i]
            const childrenItem = getOptionItem(formItem.value, comItemC, `${item.value}-${i}`)
            if (childrenItem?.length > 0) {
              children.push({key: `${item.value}-${i}`, value: `${item.value}-${i}`, label: `第${i + 1}个`, itemKey: i + '', children: childrenItem})
            }
          }
          if (children?.length > 0) {
            item.children = children
          }
        } else {
          const targetValue = typeof comItem === 'object' ? JSON.stringify(comItem) : comItem == '' ? '暂无内容' : comItem
          item.targetValue = targetValue
        }
        if (firstOn) {
          if (item.targetValue || item?.children) {
            const parentItem = {key: idOp, value: idOp, label: formItem.collapseName, children: [item]}
            ops.push(parentItem)
          }
        } else {
          ops.push(item)
        }
      }
    }
    return ops
  }
  const getAllComHtmlText = () => {
    const options: any[] = []
    if (!init) {
      for (let i = 0; i < allForm?.length; i++) {
        const cpt = allForm[i]
        if ('form' in cpt) {
          const form = cpt['form']
          const com = cpt['com']
          const dataConfig = com?.['dataConfig'] || {}
          const children = getOptionItem(form, dataConfig, i + '', true)
          if (children?.length > 0) {
            const comItem: Option = {key: `com_${i}`, value: `com_${i}`, label: com['name'], itemKey: com['uniqueNum'] || com['pageKey'], children}
            options.push(comItem)
          }
        }
      }
      // console.log('###options', options)
    }
    if (options?.length <= 0) {
      options.push({key: 'Empty', label: '无', value: 'Empty'})
    }
    return options
  }
  //---------------
  const selOpHtml = useMemo(() => {
    const op = getAllComHtmlText()
    return op
  }, [allForm])
  const onChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    // console.log(e.target.value)
    setFigmaLink(e.target.value)
  }
  const getFigmaPreview = async () => {
    if (!urlReg.test(figmaLink)) return
    try {
      const previewResponse = await getPreviewImage(getSearchParams(figmaLink, figmaState.acessToken, 'png'))
      if (previewResponse?.data) {
        const images = previewResponse?.data?.images
        // 获取 images 对象中第一个元素的值
        const firstImageUrl = Object.values(images)[0] as string
        setPreviewUrl(firstImageUrl)
      } else {
        setCallBackMsg('加载预览图失败')
      }
    } finally {
    }
  }
  async function getFigmaHtml() {
    if (urlReg.test(figmaLink)) {
      setLoading(true)
      try {
        // 获取预览图
        getFigmaPreview()

        // 生成代码
        const fileResponse: any = await getCode(getSearchParams(figmaLink, figmaState.acessToken, 'html'))
        if (fileResponse.ok) {
          const html = await fileResponse.text()
          f2cState.changeComDataCfgV(comHtmlInfo?.uniqueNum, comHtmlInfo?.path, html)
        } else {
          const info = JSON.parse(await fileResponse.text())
          message.error(`${info?.msg}:  ${info?.detail}`)

          // 授权码失效，重新申请授权
          if (info.code === 403) {
            figmaState.cleanAuth()
          }
        }
      } finally {
        setLoading(false)
      }
    } else {
      message.error('请输入Figma链接')
    }
  }

  async function getFigmaSign() {
    if (urlReg.test(figmaLink)) {
      setLoading(true)
      try {
        // 获取预览图
        getFigmaPreview()
        setCallBackMsg('')
        const fileResponse: any = await getCodeSign(getSearchParams(figmaLink, figmaState.acessToken))
        // console.log('fileResponse', fileResponse)
        if (fileResponse.ok) {
          const info = JSON.parse(await fileResponse.text())
          console.log('fileResponse info', info)
          let f2cConfigData = findF2cConfigData(info?.nodes)
          f2cConfigData = typeof f2cConfigData === 'string' ? JSON.parse(f2cConfigData) : f2cConfigData
          f2cConfigData && f2cConfigData?.length > 0 && dealF2CData(f2cConfigData[0])
        } else {
          const info = JSON.parse(await fileResponse.text())
          message.error(`${info?.msg || info?.message},  ${info?.detail}`)
          setCallBackMsg(info?.msg || info?.message)

          // 授权码失效，重新申请授权
          if (info.code === 403) {
            figmaState.cleanAuth()
          }
        }
      } finally {
        setLoading(false)
      }
    } else {
      message.error('请输入Figma链接')
    }
  }
  function dealF2CData(info: any) {
    const {configId, groupId, version} = info
    if (!selCom || selCom == 'ALL' || selCom == 'Empty') {
      // pageState.editPage(pageState.pageKey, {groupId: value}, pageState.pageKey, true)//看默认是都改还是都不改
      f2cState.changeF2cInfo(groupId)
    } else {
      f2cState.setUpdataComF2CInfo({configId, version})
    }
  }
  const onSure = async () => {
    if (selType == '1') {
      getFigmaSign()
    } else {
      getFigmaHtml()
    }
  }
  function getComSort(uniqueNum: string) {
    let sort = -1
    const component = pageState.page.components?.find(component => component.uniqueNum == uniqueNum)
    if (component) {
      sort = component.sort != undefined ? component.sort : -1
    }
    return sort
  }
  const onTypeChange = (value: string) => {
    setSelType(value)
  }
  const onSelComChange = async (value: string) => {
    setSelCom(value)
    if (value != '') {
      if (value.startsWith('pageKey_')) {
        const pageKey = value.replace('pageKey_', '')
        pageState.setPage(pageKey)
        pageState.resetComponent()
        pageState.SyncPageRemoteForm()
      } else {
        const sort = getComSort(value)
        if (sort > -1) {
          pageState.setComponent(sort)
          await pageStore.getComponentForm()
          await pageStore.getComponentSlotForm()
        }
      }
    }
  }
  const onChangeCascader = (value: string, selectedOptions: any) => {
    setSelHtml(value)
    if (selectedOptions && selectedOptions?.length > 0) {
      const targetValue = selectedOptions[selectedOptions.length - 1]?.targetValue
      const path: any = []
      let uniqueNum = ''
      for (let i = 0; i < selectedOptions.length; i++) {
        const item = selectedOptions[i]
        if (i == 0) {
          uniqueNum = item?.itemKey
        } else if (i != 1) {
          path.push({itemKey: item?.itemKey})
        }
      }
      setComHtmlInfo({uniqueNum, path})
      console.log('###onChangeCascader', targetValue, selectedOptions, selectedOptions[selectedOptions.length - 1])
    } else {
      console.log('###onChangeCascader no')
      message.error('选择内容异常，请重新选择~')
    }
  }
  const getAllForm = async () => {
    setInit(true)
    const formList: any = await f2cState.getAllForm()
    setAllForm(formList)
    setInit(false)
  }
  useEffect(() => {
    getAllForm()
  }, [pageState.page.components, pageState.pageKey])


  const hoverContent = (option: any) => {
    const targetValue = option?.targetValue || ''
    const _isHtml = isHtmlStr(targetValue)
    return _isHtml ? <div
      className='showHtmlCascader'
      // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
      dangerouslySetInnerHTML={{__html: targetValue}}
    /> : <div className='showHtmlCascader'>{targetValue}</div>
  }
  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      (option) => (option.label as string).toLowerCase().indexOf(inputValue.toLowerCase()) > -1,
    )

  const contentPopover = () => {
    return <Card key='angetCard' className="agentCard" title="前端君" type="inner" size="small" extra={<CloseOutlined onClick={hide} />}>
      <div className="bgCard" />
      < Flex gap={"small"} vertical >
        {previewUrl ? <Image align="center" justify="center" src={previewUrl} /> : <></>
        }
        <Space wrap>
          <Select
            key='typeSelect'
            size="small"
            defaultValue={typeOp[0]}
            style={{width: 70}}
            // disabled
            value={selType}
            onChange={onTypeChange}
            options={typeOp}
          />
          {selType == '1' ?
            <Select
              key='comSelect'
              size="small"
              style={{width: 100}}
              value={selCom == '' || selCom == 'Empty' ? selOpCom[0].value : selCom}
              onChange={onSelComChange}
              options={selOpCom}
            />
            :
            <Cascader
              key='htmlCascader'
              className='cascader'
              placement='topLeft'
              disabled={init}
              value={selHtml == '' || selHtml == 'Empty' ? (selOpHtml[0]?.value || '暂无数据') : selHtml}
              size="small"
              expandTrigger='hover'
              style={{width: 100}}
              options={selOpHtml}
              showSearch={{filter}}
              optionRender={(option: any) => option?.targetValue ? (
                <Popover
                  style={{width: 500}}
                  content={() => hoverContent(option)}
                  title="当前内容"
                  trigger="hover"
                >
                  {option.label}
                </Popover>

              ) : <>{option.label}</>}
              onSearch={(value: any) => console.log(value)}
              onChange={onChangeCascader}
            />
          }
        </Space>
        <TextArea placeholder="输入Figma地址" allowClear onChange={onChange} />
        <Flex justify={'flex-end'}>
          {callBackMsg ? <div style={{color: '#fa541c'}}>{callBackMsg}</div> : <></>}
          {!figmaState.isAuth ? <AuthFigma /> :
            <Button type="primary" size="small" style={{width: 100}} loading={loading} onClick={onSure}>
              生成
            </Button>}
        </Flex>
      </Flex >
    </Card >
  }
  const handleOpenChange = (newOpen: boolean) => {
    setPreviewUrl('')
    setOpenPopover(newOpen)
  }
  return (
    <Popover
      placement="leftBottom"
      zIndex={1000}
      content={() => {
        return contentPopover()
      }}
      trigger="click"
      open={openPopover}
      onOpenChange={handleOpenChange}
    >
      <FloatButton
        type="primary"
        style={{insetInlineEnd: 24}}
        icon={<BaiduOutlined />}
      />
    </Popover>
  )
}
export default FontEndAgent
