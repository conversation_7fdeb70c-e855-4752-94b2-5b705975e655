import {Flex} from '@astro/ui/ui'
import React, {useState, useEffect} from 'react'
import ReactD<PERSON><PERSON>iewer from 'react-diff-viewer'
import {getProjectConfig} from 'src/services/astro'
import pageStore from '../../editPanel/pageStore'
import styles from './index.module.scss'

const DiffInfo = () => {
  const pageState = pageStore.state
  const [lastestSaveData, setLastestSaveData] = useState<any>(null)
  const [lastestPublishData, setLastestPublishData] = useState<any>(null)

  useEffect(() => {
    ;(async () => {
      const {data: res1} = await getProjectConfig({
        projectId: pageState.projectId,
        versionId:
          Number(pageState.curLockVersion) > 0 ? pageState.curLockVersion : pageState.versionList?.[0]?.versionId,
      })
      if (res1 && res1.result === 200) {
        setLastestPublishData(res1?.data)
      }

      const {data: res2} = await getProjectConfig({projectId: pageState.projectId})
      if (res2 && res2.result === 200) {
        setLastestSaveData(res2?.data)
      }
    })()
  }, [pageState.versionList])

  return (
    <div className={styles.container}>
      <div className={styles.legend}>
        <div className={`${styles.item} ${styles.old}`}>
          {Number(pageState.curLockVersion) > 0
            ? `线上：当前对外锁定版本（${pageState.curLockVersion}）`
            : `线上：当前对外默认最新版本（${pageState.versionList?.[0]?.versionId || '无发布版本'}）`}
        </div>
        <div className={`${styles.item} ${styles.new}`}>准备发布版本</div>
      </div>

      {!pageState.curLockVersion && !pageState.versionList?.[0]?.versionId ? (
        <Flex align={'center'} justify={'center'} className={styles.emptyTip}>
          暂无已发布版本，这将是第一次发布
        </Flex>
      ) : lastestSaveData && lastestPublishData ? (
        JSON.stringify(lastestSaveData) === JSON.stringify(lastestPublishData) ? (
          <Flex align={'center'} justify={'center'} className={styles.emptyTip}>
            当前版本与线上版本完全一致，无任何差异
          </Flex>
        ) : (
          <ReactDiffViewer
            oldValue={JSON.stringify(lastestPublishData, null, 2)}
            newValue={JSON.stringify(lastestSaveData, null, 2)}
            splitView={true}
            showDiffOnly={false}
            // leftTitle="线上版本"
            // rightTitle="准备发布版本"
          />
        )
      ) : (
        <span>暂无发布信息</span>
      )}
    </div>
  )
}

export default DiffInfo
