.container {
  padding: 20px 0;
  margin: 0 auto;

  .legend {
    position: sticky;
    top: 0;
    color: #666;
    display: flex;
    gap: 20px;
    z-index: 2;
    padding-bottom: 20px;
    background-color: #fff;

    .item {
      display: flex;
      align-items: center;
      width: 50%;

      &::before {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
      }

      &.old::before {
        background-color: #f5222d;
      }

      &.new::before {
        background-color: #52c41a;
      }
    }
  }

  .loading {
    text-align: center;
    color: #999;
  }

  .emptyTip {
    height: 600px;
  }

  :global {
    .diff-viewer {
      border-radius: 4px;
      border: 1px solid #f0f0f0;

      .title-block {
        background-color: #fafafa;
        padding: 8px 12px;
        border-bottom: 1px solid #f0f0f0;
      }

      .line {
        font-size: 13px;
        line-height: 20px;
        padding: 4px 12px;
      }
    }
  }
}