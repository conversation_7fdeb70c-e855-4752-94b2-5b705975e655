import { CloudUploadOutlined, DiffOutlined } from '@astro/ui/icon'
import { Alert, Button, Col, Flex, Form, Input, Modal, Popconfirm, Radio, Row, Tooltip, message } from '@astro/ui/ui'
import { memo, useEffect, useState } from 'react'
import config from 'src/config'
import pageStore from '../editPanel/pageStore'
import { VersionModal } from '../VersionModal'
import DiffInfo from './DiffInfo'

export const PublishType = {
  LASTEST: 'lastest', // 普通发布最新版
  RELEASE: 'release', // 正式发布稳定版
}

export const PublishDrawer = (props: any) => {
  const [form] = Form.useForm()
  const { isModalOpen, setIsModalOpen } = props
  const [publishType, setPublishType] = useState(PublishType.LASTEST)
  const pageState = pageStore.state

  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 4 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 20 },
    },
  }

  const handleOk = async () => {
    // setIsModalOpen(false);
    const env = (form as any).getFieldValue('env')
    const publishRemark = (form as any).getFieldValue('publishRemark')
    const publishType = (form as any).getFieldValue('publishType')

    if (!env) {
      message.error('请选择发布环境')
      return
    }
    if (pageStore.isFormOp) {
      message.error('请先保存已修改内容')
      return
    }
    console.log(`env`, env)
    const res = await pageStore.publish(env, publishRemark)
    if (res?.data?.result == 200) {
      message.success(`发布${env}成功`)
      publishType === PublishType.RELEASE && (await pageState.lockProjectVersion(res?.data?.data))
      setIsModalOpen(false)
      pageStore.queryVersionList() // 更新版本列表
    } else {
      message.error(res?.data?.reason)
    }
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }

  useEffect(() => {
    // console.log(`pageStore`, pageStore.pages, Object.keys(pageStore.pages || {}))
  }, [pageStore.pages])

  const Notice = (): React.ReactNode => {
    return (
      <div>
        {pageStore.pages ? (
          Object.values(pageStore.pages).map((item, index) => {
            return (
              <Row gutter={[16, 16]} key={index}>
                <Col span={12}>{Object.keys(pageStore.pages || {})[index]}</Col>
                <Col span={6}>{item.online ? '可见' : '屏蔽'}</Col>
              </Row>
            )
          })
        ) : (
          <></>
        )}
      </div>
    )
  }

  // 打开修改对比弹窗
  const handleDiffClick = () => {
    Modal.info({
      title: '修改对比',
      width: '90%',
      style: {
        maxWidth: '1300px',
        top: '50px',
      },
      bodyStyle: {
        height: '800px',
        overflow: 'auto',
        marginTop: '20px', // 为固定底部按钮留出空间
      },
      content: <DiffInfo />,
      // okText: '关闭',
      footer: null,
      closable: true,
      maskClosable: true,
    })
  }

  const AppVersion = memo(() => {
    const store = pageStore.state
    const [isModalOpen, setIsModalOpen] = useState(false)

    return (
      <>
        <Button
          color="primary"
          variant="filled"
          onClick={() => {
            setIsModalOpen(!isModalOpen)
          }}
          icon={<CloudUploadOutlined />}
        >
          {Number(store.curLockVersion) > 0
            ? `当前对外锁定版本（${store.curLockVersion}）`
            : `当前对外默认最新版本（${store.versionList?.[0]?.versionId || '无发布版本'}）`}
        </Button>
        <VersionModal
          isModalOpen={isModalOpen}
          setIsModalOpen={(versionModalOpen: boolean, closePublishModal?: boolean) => {
            setIsModalOpen(versionModalOpen)
            closePublishModal && handleCancel()
          }}
          key={'VersionModal'}
        />
      </>
    )
  })

  return (
    <>
      <Modal
        title="发布项目"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        cancelText="取消"
        width={560}
        footer={[
          <AppVersion key="version" />,
          <Button key="diff" icon={<DiffOutlined />} onClick={handleDiffClick}>
            修改对比
          </Button>,
          publishType === PublishType.LASTEST ? (
            <Button key="submit" type="primary" onClick={handleOk}>
              确认发布
            </Button>
          ) : (
            <Popconfirm
              key="submitRelease"
              title="确认正式发布？"
              description="将把对外访问的版本锁定为这次发布的版本"
              onConfirm={handleOk}
              okText="确认"
              cancelText="取消"
            >
              <Button type="primary">确认发布</Button>
            </Popconfirm>
          ),
        ]}
      >
        <Form
          form={form}
          {...formItemLayout}
          initialValues={{ env: config.env, remark: '', publishType: PublishType.LASTEST }}
          preserve={false}
        >
          <Form.Item label="环境" name="env" tooltip="选择发布环境" style={{ display: 'none' }}>
            <Radio.Group value={config.env} disabled={true}>
              <Radio value={'test'}>测试</Radio>
              <Radio value={'prod'}>生产</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="页面信息" tooltip="控制页面是否可以访问，通过页面配置的【是否上线】选项进行切换">
            <Alert message={<Notice />} />
          </Form.Item>

          <Form.Item label="发布备注" name="publishRemark">
            <Input />
          </Form.Item>

          <Form.Item
            label="发布方式"
            name="publishType"
            tooltip="发布并锁定版本可在发布成功后锁定到当前版本，确保线上用户看到稳定版本，避免因误操作导致的线上问题。"
          >
            <Radio.Group
              onChange={e => {
                setPublishType(e.target.value)
              }}
              options={[
                { value: PublishType.LASTEST, label: '正常发布' },
                { value: PublishType.RELEASE, label: '发布并锁定版本' },
              ]}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}
