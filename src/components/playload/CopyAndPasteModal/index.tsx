import {
  <PERSON><PERSON>,
  But<PERSON>,
  Collapse,
  Divider,
  Flex,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Table,
  Tooltip,
  Typography,
  message,
} from '@astro/ui/ui'
import type {CollapseProps, TableColumnsType} from '@astro/ui/ui'
import dayjs from 'dayjs'
import {memo, useCallback, useEffect, useRef, useState} from 'react'
import type React from 'react'
import userStore from 'src/components/common/user/userStore'
import pageStore from 'src/components/playload/editPanel/pageStore'
import config from 'src/config'
import {getProjectConfig, getVersionList} from 'src/services/astro'
import {RenderVersinListTable} from '../VersionModal'
import type {ProjectVersionInfo} from '../VersionModal/type'
import type {Page} from '../editPanel/types'
import RenderPagesTable from './RenderPagesTable'
import styles from './index.module.scss'
const {isDev} = config

const {Title, Paragraph} = Typography

const optionsSource = [
  {
    value: 'test',
    label: '测试环境项目',
  },
  {
    value: 'prod',
    label: '正式环境项目',
  },
]

export const CopyAndPasteModal = (props: any) => {
  const {isModalOpen, setIsModalOpen} = props
  const formRef = useRef(null)
  const [versionList, serVersionList] = useState<ProjectVersionInfo[]>([])
  const [projectInfo, setProjectInfo] = useState<any>()
  const pageState = pageStore.state
  const userState = userStore.state
  const sourceInfo = useRef<any>({})
  const [selectedPages, setSelectedPages] = useState<string[]>([])

  const selectedRowKeysRef = useRef<any>()
  const setSelectedRow = async (row: any) => {
    // 设置选择的版本行
    selectedRowKeysRef.current = row
    sourceInfo.current = {...sourceInfo.current, versionId: row?.[0]}
    if (sourceInfo.current?.versionId) await searchProject() // 有填写版本号，获取对应版本号的项目配置
  }

  useEffect(() => {
    // 产品和游客不能操作拷贝配置
    if (!pageState.projectId || userState.user.role === 'P' || userState.user.role === 'C') return
  }, [pageState.projectId])

  const handleCancel = () => {
    setProjectInfo(null)
    setIsModalOpen(false)
  }

  // 查询项目
  const searchProject = useCallback(async () => {
    const {sourceEnv, sourceProjectId, versionId} = sourceInfo.current
    if (!sourceProjectId) {
      setProjectInfo(null)
      message.error('请输入需要拷贝的项目Id')
      return
    }

    try {
      const {data: res} = await getProjectConfig({sourceEnv, projectId: sourceProjectId, versionId})
      if (res && res.result === 200) {
        setProjectInfo(res?.data)
        !versionId && queryProjectVersionList() // 没选versionId，说明没有请求过版本列表，需要发起请求
        return res?.data
      } else {
        message.error(res.reason || '查询失败')
      }
    } catch (error) {
      message.error(isDev ? '本地环境调用接口跨域' : '请求报错')
    }
  }, [sourceInfo])

  // 查询项目对应版本
  const queryProjectVersionList = useCallback(async () => {
    const {sourceEnv, sourceProjectId} = sourceInfo.current

    const {data: res} = await getVersionList({
      sourceEnv,
      projectId: sourceProjectId,
    })
    if (res && res.result === 200) {
      serVersionList(res.data)
    }
  }, [sourceInfo])

  // 确认拷贝
  const onOk = async (isIncrement = false) => {
    const allPages = projectInfo?.config?.pages
    // 只获取选中的页面
    const selectedPagesData = Object.entries(allPages).reduce((acc: any, [key, value]) => {
      if (selectedPages.includes(key)) {
        // 检查是否存在重复的 pageKey
        let newKey = key
        if (isIncrement && pageState.pages[key]) {
          let suffix = 1
          // 循环查找可用的 key
          while (pageState.pages[`${key}_${suffix}`]) {
            suffix++
          }
          newKey = `${key}_${suffix}`
        }
        acc[newKey] = value
      }
      return acc
    }, {})

    // 覆盖拷贝 / 增量拷贝
    const roomPages = {
      pages: isIncrement ? {...pageState.pages, ...selectedPagesData} : selectedPagesData,
      createTime: new Date().getTime(),
    }
    pageState.forceSync(roomPages)
    Object.keys(selectedPagesData).map(pageKey => {
      // 同步更新操作列表
      pageState.setFormOp(pageKey ?? '', -1)
    })
    pageState.setDefaultPageIndex(selectedPagesData)
    handleCancel()
  }

  return (
    <>
      <Modal
        title="拷贝项目全部配置"
        width={700}
        open={isModalOpen}
        onCancel={handleCancel}
        destroyOnClose={true}
        footer={null}
      >
        <Form
          ref={formRef}
          onFinish={async (value: any) => {
            sourceInfo.current = value
            setSelectedRow(0) // 重置版本选择
            searchProject()
          }}
        >
          <Divider orientation="left" style={{borderColor: `#D9D9D9`, color: '#848484', fontSize: '12px'}}>
            输入其它项目的projectId
          </Divider>

          <Space.Compact style={{width: '100%'}}>
            <Form.Item name="sourceEnv" style={{width: '36%'}} initialValue="prod">
              <Select options={optionsSource} />
            </Form.Item>
            <Form.Item name="sourceProjectId" style={{width: '100%'}}>
              <Input placeholder="输入你需要拷贝配置的项目projectId" />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                查询
              </Button>
            </Form.Item>
          </Space.Compact>
        </Form>

        {projectInfo && (
          <Flex vertical={true} gap={10}>
            <Title level={5} style={{marginTop: '10px', marginBottom: '-4px'}}>
              请确认拷贝的配置信息
            </Title>
            <Alert
              message={
                <>
                  <Paragraph>创建者: {projectInfo?.project?.creator}</Paragraph>
                  <Paragraph>项目名称: {projectInfo?.project?.actName}</Paragraph>
                  <Paragraph>项目描述: {projectInfo?.project?.description}</Paragraph>
                </>
              }
            />

            <Collapse
              items={[
                {
                  forceRender: true,
                  label: '选择拷贝的版本（不勾选则使用最新版本）',
                  children: (
                    <RenderVersinListTable
                      key={projectInfo?.project?.actId}
                      versionList={versionList}
                      setSelectedRow={setSelectedRow}
                      selectedRowKeysRef={selectedRowKeysRef}
                    />
                  ),
                },
              ]}
            />

            <Collapse
              items={[
                {
                  forceRender: true,
                  label: '选择拷贝的页面（默认全部页面）',
                  children: (
                    <RenderPagesTable
                      key={projectInfo?.project?.actId}
                      pages={projectInfo?.config?.pages}
                      selectedPages={selectedPages}
                      setSelectedPages={setSelectedPages}
                    />
                  ),
                },
              ]}
            />
            <Flex justify="flex-end" gap={10}>
              <Tooltip title="将覆盖当前项目的所有页面配置">
                <Button
                  type="default"
                  onClick={() => {
                    onOk(false)
                  }}
                  style={{width: '100px', marginTop: '10px'}}
                >
                  覆盖拷贝
                </Button>
              </Tooltip>

              <Tooltip title="将保留当前项目的页面配置，仅添加选中的新页面,【注意】相同路径的页面将被覆盖">
                <Button
                  type="primary"
                  onClick={() => {
                    onOk(true)
                  }}
                  style={{width: '100px', marginTop: '10px'}}
                >
                  增量拷贝
                </Button>
              </Tooltip>
            </Flex>
          </Flex>
        )}
      </Modal>
    </>
  )
}
