import {Table} from '@astro/ui/ui'
import {type FC, useEffect, useState} from 'react'
import type {Pages} from 'src/components/playload/editPanel/types'

interface RenderPagesTableProps {
  pages: Pages
  selectedPages: string[]
  setSelectedPages: any
}

const RenderPagesTable: FC<RenderPagesTableProps> = ({pages, selectedPages, setSelectedPages}) => {
  const dataSource = Object.entries(pages || {}).map(([key, page]) => ({
    key,
    name: page.name,
    path: key,
  }))

  useEffect(() => {
    // 默认全选
    setSelectedPages(dataSource.map(item => item.key))
  }, [pages])

  const columns = [
    {
      title: '页面名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '页面路径',
      dataIndex: 'path',
      key: 'path',
    },
  ]

  return (
    <Table
      rowSelection={{
        type: 'checkbox',
        selectedRowKeys: selectedPages,
        onChange: selectedRowKeys => {
          setSelectedPages(selectedRowKeys as string[])
        },
      }}
      columns={columns}
      dataSource={dataSource}
      pagination={false}
    />
  )
}

export default RenderPagesTable
