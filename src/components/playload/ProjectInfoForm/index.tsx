import {CheckOutlined, CloseOutlined} from '@astro/ui/icon'
import {
  ModalForm,
  ProForm,
  ProFormCheckbox,
  ProFormDateTimeRangePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormList,
  ProFormRadio,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@astro/ui/pro'
import {Button, Collapse, Flex, Popconfirm, Tag, message} from '@astro/ui/ui'
import dayjs from 'dayjs'
import {useEffect, useState} from 'react'
import {useNavigate} from 'react-router-dom'
import {ApplyServerAuth, type ServerInfo, checkServerAuth, getServerList} from 'src/components/AdminTable/util/config'
import ImgUpload from 'src/components/common/ImgUpload'
import appStore from 'src/components/dashboard/AppStore'
import config from 'src/config'
import type {AuthUser, IUrlParams, ProjectType} from 'src/config/project'
import {astroBusiness, astroTags, renderOptions, statTagsOptions} from 'src/config/project'
import DebounceSearchUser from '../DebounceSearchUser'
import ParamsConfig from '../editPanel/toolPanel/ParamsConfig'

const isProd = !config.isTest
interface IProjectInfoForm {
  trigger: any
  info?: ProjectType
  refresh?: boolean
}
const requiredProps = {
  required: true,
  rules: [{required: true, message: '这是必填项'}],
}
function ProjectInfoForm(props: IProjectInfoForm) {
  const {trigger, info, refresh} = props
  const [imageUrl, setImageUrl] = useState()
  const [authUsers, setAuthUsers] = useState<AuthUser[]>([])
  const [projectUrlParams, setProjectUrlParams] = useState({})
  const [activeCollapseKeys, setActiveCollapseKeys] = useState<string[]>([])
  const [serverList, setServerList] = useState<ServerInfo[]>([])
  const [serverAuth, setServerAuth] = useState(true)
  const [testServerAuth, setTestServerAuth] = useState(true)
  const appListState = appStore.state
  const navigate = useNavigate()

  useEffect(() => {
    setAuthUsers(info?.authUsers ?? [])
  }, [info?.authUsers])

  useEffect(() => {
    setProjectUrlParams(info?.projectConfig?.projectUrlParams ?? {})
  }, [info?.projectConfig?.projectUrlParams])

  useEffect(() => {
    getServerList(isProd).then(res => {
      if (res.code === 0) {
        setServerList(res.data)
      }
    })
  }, [])
  useEffect(() => {
    info?.serverKey && checkServer(info.serverKey)
  }, [info?.serverKey])
  const checkServer = (serverKey: string) => {
    checkServerAuth({serverKey}).then(res => {
      if (res.code === 0) {
        setTestServerAuth(res?.data?.tableAuth)
      }
    })
    checkServerAuth({serverKey}, true).then(res => {
      if (res.code === 0) {
        setServerAuth(res?.data?.tableAuth)
      }
    })
  }
  const hiddenProject = async () => {
    const bool = await appListState.doHide(info?.id ?? 0)
    if (bool) {
      navigate(`/`)
    }
  }

  return (
    <ModalForm
      width="530px"
      // layout="horizontal"
      title={info ? '编辑项目' : '新建项目'}
      labelWidth="auto"
      trigger={trigger}
      onFinish={async (values: any) => {
        const {
          actName,
          description,
          tags,
          business,
          envOpen,
          time,
          actId,
          svcAppId,
          actDocumentUrl,
          uiUrl,
          reduceDays,
          workDays,
          renderType,
          directId,
          cdnStatus,
          statTags,
          bookmarks,
          serverKey,
          stat,
        } = values
        // const [beginTime, endTime] = time
        const timeObj = time
          ? {
              beginTime: dayjs(time[0]).valueOf(),
              endTime: dayjs(time[1]).valueOf(),
            }
          : {}
        const params = {
          actId,
          actName,
          svcAppId,
          actDocumentUrl,
          uiUrl,
          reduceDays,
          workDays,
          description,
          tags,
          business,
          renderType,
          avatar: imageUrl ?? info?.avatar,
          authUsers: authUsers ? authUsers : info?.authUsers,
          projectConfig: {...info?.projectConfig, projectUrlParams, envOpen},
          directId,
          cdnStatus,
          statTags,
          bookmarks,
          serverKey,
          stat,
          ...timeObj,
        }
        return await appListState.doSaveProject(info ? {...params, id: info.id} : params, refresh)
        // return true // 保存失败不关闭弹窗
      }}
      submitter={{
        render: (props, defaultDom) =>
          info
            ? [
                <Popconfirm
                  key="deleteBtn"
                  title="删除项目"
                  description={() => {
                    return (
                      <>
                        <p>点击确定将删除项目（可通过后端找回）</p>
                      </>
                    )
                  }}
                  onConfirm={hiddenProject}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="primary" danger>
                    删除项目
                  </Button>
                </Popconfirm>,

                ...defaultDom,
              ]
            : [...defaultDom],
      }}
      initialValues={
        info
          ? {
              actId: info.actId,
              svcAppId: info.svcAppId,
              actName: info.actName,
              actDocumentUrl: info.actDocumentUrl,
              reduceDays: info.reduceDays,
              workDays: info.workDays,
              uiUrl: info.uiUrl,
              description: info.description,
              time:
                info.beginTime && info.endTime
                  ? [
                      dayjs(info.beginTime).format('YYYY-MM-DD HH:mm:ss'),
                      dayjs(info.endTime).format('YYYY-MM-DD HH:mm:ss'),
                    ]
                  : undefined,
              tags: info.tags,
              business: info.business,
              envOpen: info?.projectConfig && 'envOpen' in info.projectConfig ? info.projectConfig.envOpen : true,
              renderType: info.renderType,
              cdnStatus: info.cdnStatus,
              bookmarks: info.bookmarks || [],
              avatar: [
                {
                  status: 'done',
                  url: info.avatar,
                  // url: info.avatar || '/default.png',
                },
              ],
              statTags: info.statTags,
              stat: info.stat,
            }
          : {envOpen: true, stat: true}
      }
    >
      <ProForm.Group>
        <ProFormText {...requiredProps} width="md" name="actName" label="项目名称" placeholder="请输入名称" />
      </ProForm.Group>

      <Flex gap={10}>
        <ProForm.Group>
          <ProFormCheckbox.Group
            {...requiredProps}
            name="tags"
            layout="horizontal"
            label="功能模块"
            options={astroTags}
          />
          {/* <ProFormCheckbox.Group
            {...requiredProps}
            name="business"
            layout="horizontal"
            label="项目类型"
            options={astroBusiness}
          /> */}

          <ProFormSelect
            {...requiredProps}
            name="business"
            label="项目类型"
            mode="multiple"
            options={astroBusiness}
            style={{width: '100%'}}
            tooltip="新业务类型请如流联系 陈伟浩 添加"
          />

          <ProFormSwitch
            name="envOpen"
            label="是否需要平台环境参数"
            checkedChildren={<CheckOutlined />}
            unCheckedChildren={<CloseOutlined />}
            tooltip="开启后，url地址会带上环境参数（参数内容可以自定义）"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ImgUpload value={info ? info.avatar : ''} onChange={setImageUrl} />
        </ProForm.Group>
      </Flex>

      <Collapse
        activeKey={activeCollapseKeys}
        onChange={setActiveCollapseKeys}
        size="small"
        items={[
          {
            key: '0',
            forceRender: true,
            label: '更多配置选项',
            children: (
              <>
                <ProForm.Group>
                  <ProFormText width="md" name="description" label="项目描述" placeholder="请输入描述" />
                </ProForm.Group>

                <ProForm.Group>
                  <ProFormDateTimeRangePicker
                    // {...requiredProps}
                    // width={370}
                    width="md"
                    showTime={{format: 'HH:mm:ss'}}
                    format="YYYY-MM-DD HH:mm:ss"
                    name="time"
                    label="项目开始结束时间"
                    placeholder={['开始时间', '结束时间']}
                  />
                </ProForm.Group>

                {!info && (
                  <ProForm.Group>
                    <ProFormText
                      width="sm"
                      name="directId"
                      label="指定项目id"
                      placeholder="请输入指定项目id"
                      tooltip="新增项目时可以指定创建项目的id"
                    />
                  </ProForm.Group>
                )}

                <Flex gap={10}>
                  <ProForm.Group>
                    <ProFormText width="sm" name="actId" label="项目活动ID" placeholder="请输入项目活动ID" />
                  </ProForm.Group>

                  {info?.svcAppId && (
                    <ProForm.Group>
                      <ProFormText width="sm" name="svcAppId" label="活动service通道appid" disabled={true} />
                    </ProForm.Group>
                  )}
                </Flex>

                <Flex gap={10}>
                  <ProForm.Group>
                    <ProFormSelect
                      width="sm"
                      name="cdnStatus"
                      label="是否需要预热CDN"
                      options={[
                        {label: '是', value: 1},
                        {label: '否', value: 0},
                      ]}
                    />
                  </ProForm.Group>

                  <ProFormRadio.Group
                    fieldProps={{
                      buttonStyle: 'solid',
                    }}
                    radioType="button"
                    name="renderType"
                    // initialValue={info && info.renderType ? info.renderType : defaultRenderType}
                    label="渲染器"
                    options={renderOptions}
                  />
                </Flex>

                <ProForm.Group>
                  <Flex align={'center'} justify={'center'}>
                    <ProForm.Item label="可操作用户" tooltip="只填自己代表仅限自己可操作">
                      <DebounceSearchUser
                        authUsers={authUsers}
                        setAuthUsers={(value: any) => {
                          setAuthUsers(value)
                        }}
                      />
                    </ProForm.Item>
                  </Flex>
                </ProForm.Group>

                <Collapse
                  size="small"
                  // ghost={true}
                  items={[
                    {
                      key: '1',
                      forceRender: true,
                      label: '项目参数配置',
                      children: (
                        <ParamsConfig
                          projectUrlParams={projectUrlParams}
                          updateProjectUrlParams={(paramsData: IUrlParams) => {
                            setProjectUrlParams(paramsData)
                            setActiveCollapseKeys(activeCollapseKeys.filter(key => key !== '0'))
                            message.success('参数编辑成功，请点击确定按钮更新项目配置')
                          }}
                          type="project"
                        />
                      ),
                    },
                    /* 后台权限配置*/
                    {
                      key: '2',
                      forceRender: true,
                      label: '后台权限配置',
                      children: (
                        <>
                          <ProFormDependency name={['tags', 'serverKey']}>
                            {({tags, serverKey}) => (
                              <>
                                {/* 权限选择 */}
                                {tags?.includes(4) ? (
                                  // info?.serverKey ? (
                                  //   <p>
                                  //     当前选择权限:
                                  //     {serverList.find(item => item.serverKey === info.serverKey)?.serverName || info.serverKey}
                                  //   </p>
                                  // ) : (
                                  <ProFormRadio.Group
                                    name="serverKey"
                                    initialValue={info?.serverKey}
                                    layout="horizontal"
                                    label="权限选择"
                                    options={serverList.map(item => ({
                                      label: item.serverName,
                                      value: item.serverKey,
                                    }))}
                                    disabled={info?.serverKey}
                                    onChange={(e: {target: {value: any}}) => {
                                      const server = e.target.value
                                      checkServer(server)
                                    }}
                                  />
                                  // )
                                ) : (
                                  <p>非后台项目无需配置</p>
                                )}
                                {serverKey && !testServerAuth && (
                                  <div>
                                    <a href={ApplyServerAuth(serverKey, false)} target="_blank" rel="noreferrer">
                                      测试环境暂无权限，点击前往【
                                      {serverList.find(i => i.serverKey === serverKey)?.serverName || serverKey}
                                      】申请
                                    </a>
                                  </div>
                                )}
                                {serverKey && !serverAuth && (
                                  <div>
                                    <a href={ApplyServerAuth(serverKey, true)} target="_blank" rel="noreferrer">
                                      正式环境暂无权限，点击前往【
                                      {serverList.find(i => i.serverKey === serverKey)?.serverName || serverKey}
                                      】申请
                                    </a>
                                  </div>
                                )}
                              </>
                            )}
                          </ProFormDependency>
                        </>
                      ),
                    },
                  ]}
                ></Collapse>
              </>
            ),
          },
          {
            key: '3',
            forceRender: true,
            label: '项目数据记录',
            children: (
              <>
                <Flex gap={10}>
                  <ProFormSelect
                    name="statTags"
                    label="分类标签"
                    mode="tags"
                    options={statTagsOptions}
                    style={{width: '216px'}}
                    fieldProps={{
                      tagRender: (props: any) => {
                        const option = statTagsOptions.find(item => item.value === props.value)
                        return (
                          <Tag
                            color={option?.color}
                            closable={props.closable}
                            onClose={props.onClose}
                            style={props.style}
                          >
                            {props.label}
                          </Tag>
                        )
                      },
                    }}
                  />

                  <ProFormSwitch
                    name="stat"
                    label="是否纳入统计"
                    tooltip="启用后，该项目效率提升数据将被计入统计系统，作为平台整体效能评估依据"
                    checkedChildren="是"
                    unCheckedChildren="否"
                  />
                </Flex>

                <Flex gap={10}>
                  <ProForm.Group>
                    <ProFormText
                      width="sm"
                      name="actDocumentUrl"
                      label="需求文档链接"
                      placeholder="请填入需求文档链接"
                    />
                  </ProForm.Group>

                  <ProForm.Group>
                    <ProFormText width="sm" name="uiUrl" label="设计稿链接" placeholder="请填入设计稿链接" />
                  </ProForm.Group>
                </Flex>

                <Flex gap={10}>
                  <ProForm.Group>
                    <ProFormDigit width="sm" name="reduceDays" label="提效减少人天" placeholder="请填入提效减少人天" />
                  </ProForm.Group>

                  <ProForm.Group>
                    <ProFormDigit width="sm" name="workDays" label="实际排期人天" placeholder="请填入实际排期人天" />
                  </ProForm.Group>
                </Flex>

                <ProFormList
                  tooltip="可以添加书签记录与项目有关的链接"
                  name="bookmarks"
                  label="书签列表"
                  creatorButtonProps={{
                    creatorButtonText: '新增书签',
                  }}
                >
                  {(f, index, action) => {
                    return (
                      <ProForm.Group key={index}>
                        <ProFormText
                          name="label"
                          label="标签名称"
                          placeholder="请输入标签名称"
                          rules={[{required: true, message: '请输入标签名称'}]}
                        />
                        <ProFormText
                          name="link"
                          label="链接地址"
                          placeholder="请输入链接地址"
                          rules={[
                            {required: true, message: '请输入链接地址'},
                            {type: 'url', message: '请输入有效的URL'},
                          ]}
                        />
                      </ProForm.Group>
                    )
                  }}
                </ProFormList>
              </>
            ),
          },
        ]}
      ></Collapse>
    </ModalForm>
  )
}

export default ProjectInfoForm
