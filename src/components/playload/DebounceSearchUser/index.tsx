import {Flex, Select, Spin, message} from '@astro/ui/ui'
import type {SelectProps} from '@astro/ui/ui'
import debounce from 'lodash.debounce'
import {useEffect, useMemo, useRef, useState} from 'react'
import type {AuthUser} from 'src/config/project'
import {posterUser} from 'src/services/user'

export interface DebounceSelectProps<ValueType = any>
  extends Omit<SelectProps<ValueType | ValueType[]>, 'options' | 'children'> {
  fetchOptions: (search: string) => Promise<ValueType[]>
  debounceTimeout?: number
}

function DebounceSelect<ValueType extends {key?: string; label: React.ReactNode; value: string | number} = any>({
  fetchOptions,
  debounceTimeout = 800,
  ...props
}: DebounceSelectProps<ValueType>) {
  const [fetching, setFetching] = useState(false)
  const [options, setOptions] = useState<ValueType[]>([])
  const fetchRef = useRef(0)

  const debounceFetcher = useMemo(() => {
    // 查询用户
    const loadOptions = (value: string) => {
      fetchRef.current += 1
      const fetchId = fetchRef.current
      setOptions([])
      setFetching(true)

      fetchOptions(value).then(newOptions => {
        if (fetchId !== fetchRef.current) {
          // for fetch callback order
          return
        }

        setOptions(newOptions)
        setFetching(false)
      })
    }

    return debounce(loadOptions, debounceTimeout)
  }, [fetchOptions, debounceTimeout])

  return (
    <Select
      labelInValue
      filterOption={false}
      onSearch={debounceFetcher}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      {...props}
      options={options}
    />
  )
}

interface UserValue {
  label: string
  value: string
}

const fetchUserList: any = async (username: string) => {
  const formattedUsername = username.startsWith('dw_') ? username : `dw_${username}`
  return posterUser(formattedUsername).then((res: any) => {
    const data = res?.data
    if (data?.result == 1) {
      return [
        {
          key: data?.info?.uid,
          value: data?.info?.uid,
          label: data?.info?.passport,
        },
      ]
    } else {
      message.error(data?.msg || '查询失败')
    }
  })
}

type Props = {
  authUsers: AuthUser[]
  setAuthUsers: any
}

const DebounceSearchUser = (props: Props) => {
  const [values, setValues] = useState<UserValue[]>()

  useEffect(() => {
    const temp: UserValue[] = []
    props?.authUsers?.map(item => {
      temp.push({
        value: String(item.uid),
        label: item.passport,
      })
    })
    setValues(temp)
  }, [props?.authUsers])

  return (
    <DebounceSelect
      mode="multiple"
      value={values}
      placeholder="请输入拼音查询"
      fetchOptions={fetchUserList}
      onChange={(newValue: UserValue[]) => {
        const temp: AuthUser[] = []
        newValue.map((item: UserValue) => {
          temp.push({
            uid: Number(item?.value),
            passport: item?.label,
          })
        })
        props?.setAuthUsers(temp)
      }}
      style={{width: 350}}
    />
  )
}

export default DebounceSearchUser
