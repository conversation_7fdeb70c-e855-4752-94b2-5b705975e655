import {Flex, Form, Select} from '@astro/ui/ui'
import config from 'src/config'

const EnvFontSizeConfig = () => {
  // 统一管理字号配置
  const fontSizeConfigs = [
    {
      key: 'pc',
      label: 'web页',
      width: 100,
      defaultSize: '60',
      sizeInfo: config.pcFontSizeInfo,
    },
  ]

  // 生成选项列表
  const generateOptions = (sizeInfo: Record<string, string>) => {
    return [
      {value: null, label: `不配置${sizeInfo}字号`},
      ...Object.values(sizeInfo).map(size => ({
        label: size,
        value: size,
      })),
    ]
  }

  return (
    <Flex align={'flexStart'} gap={10}>
      <Form.Item label="根节点配置" />

      {fontSizeConfigs.map(({key, label, width, defaultSize, sizeInfo}) => (
        <Form.Item key={key} name={['ext', 'rootFontSize', key]} label={label} initialValue={null}>
          <Select options={generateOptions(sizeInfo)} style={{width}} mode="tags" maxCount={1} />
        </Form.Item>
      ))}
    </Flex>
  )
}

export default EnvFontSizeConfig
