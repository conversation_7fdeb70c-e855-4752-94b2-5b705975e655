import {ChromeOutlined} from '@astro/ui/icon'
import {Button, Flex, QRCode, Space, message} from '@astro/ui/ui'
import {useEffect, useState} from 'react'
import pageStore from 'src/components/playload/editPanel/pageStore'
import PageParams from 'src/components/playload/editPanel/toolPanel/PageParams_old'
import config from 'src/config'
import {appendLinkParam, copy} from 'src/utils'

const QRCodeBox: any = QRCode

export const EnvPreviewContent = () => {
  const pageState = pageStore.state
  const [qrlink, setQrLink] = useState(`${config.renderOnline}/${pageState.projectId}/${pageState.pageKey}`)

  useEffect(() => {
    const paramsData = pageState.projectInfo?.projectConfig?.urlParams?.[pageState.pageKey] ?? {}
    const envInfo = pageState.templateControl
    let newLink = `${config.renderOnline}/${pageState.projectId}/${pageState.pageKey}`
    Object.keys(paramsData).map(key => {
      if (!paramsData[key]?.select?.length) return
      newLink = appendLinkParam(newLink, key, paramsData[key]?.select?.join(','))
    })
    if (envInfo?.curEnvKey) {
      newLink = appendLinkParam(newLink, envInfo?.curEnvKey, envInfo.curEnvValue)
    }
    setQrLink(newLink)
  }, [pageState.projectInfo?.projectConfig?.urlParams, pageState.templateControl])

  return (
    <Flex gap={10}>
      <Space direction="vertical" align="center">
        <QRCodeBox value={qrlink} />
        <div>发布后可预览</div>

        <Button
          title="点击可复制pc web浏览器预览链接"
          onClick={() => {
            copy(appendLinkParam(qrlink, 'device', 'pc'))
            message.success('复制成功')
          }}
          icon={<ChromeOutlined />}
        >
          Web页
        </Button>
        <div>点击对应图标复制访问链接</div>
      </Space>

      <PageParams />
    </Flex>
  )
}
