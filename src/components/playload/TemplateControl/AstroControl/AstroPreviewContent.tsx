import {ChromeOutlined, Html5Outlined, WindowsOutlined} from '@astro/ui/icon'
import {Button, Checkbox, Flex, QRCode, Space, message} from '@astro/ui/ui'
import {useEffect, useState} from 'react'
import pageStore from 'src/components/playload/editPanel/pageStore'
import config from 'src/config'
import {appendLinkParam, copy} from 'src/utils'
import ParamsConfig from '../../editPanel/toolPanel/ParamsConfig'

const QRCodeBox: any = QRCode

export const AstroPreviewContent = () => {
  const pageState = pageStore.state
  const [qrlink, setQrLink] = useState(`${config.renderOnline}/${pageState.projectId}/${pageState.pageKey}`)
  const [openAfterCopy, setOpenAfterCopy] = useState(false) // 添加开关状态

  useEffect(() => {
    let newLink = `${config.renderOnline}/${pageState.projectId}/${pageState.pageKey}`

    const envInfo = pageState.templateControl
    // 平台参数，优先级最低
    if (envInfo?.hasEnv && envInfo?.curEnvKey) {
      newLink = appendLinkParam(newLink, envInfo?.curEnvKey, envInfo.curEnvValue)

      // 测试环境默认带上versionId = -1
      if (envInfo?.curEnvKey === 'ServerTest' && envInfo?.curEnvValue === '1') {
        newLink = appendLinkParam(newLink, 'versionId', '-1')
      }
    }
    // 先添加项目级参数（优先级较低）
    const projectParams = pageState.projectInfo?.projectConfig?.projectUrlParams ?? {}
    Object.entries(projectParams).forEach(([key, value]: [string, any]) => {
      if (value?.select?.length) {
        newLink = appendLinkParam(newLink, key, value.select.join(','))
      }
    })
    // 后添加页面级参数（优先级更高，会覆盖同名的项目参数）
    const pageParams = pageState.projectInfo?.projectConfig?.urlParams?.[pageState.pageKey] ?? {}
    Object.entries(pageParams).forEach(([key, value]: [string, any]) => {
      if (value?.select?.length) {
        // 页面参数会覆盖项目参数
        newLink = appendLinkParam(newLink, key, value.select.join(','))
      }
    })

    setQrLink(newLink)
  }, [
    pageState.projectInfo?.projectConfig?.urlParams,
    pageState.projectInfo?.projectConfig?.projectUrlParams,
    pageState.templateControl,
  ])

  const handleCopy = (device: string) => {
    const link = appendLinkParam(qrlink, 'device', device)
    copy(link)
    message.success(`${link}  复制成功`)
    if (openAfterCopy) {
      window.open(link, '_blank')
    }
  }

  return (
    <Flex gap={10}>
      <Space direction="vertical" align="center">
        <QRCodeBox value={qrlink} />
        <Flex vertical align={'center'} gap={4}>
          <span>发布后可查看</span>

          <Checkbox checked={openAfterCopy} onChange={e => setOpenAfterCopy(e.target.checked)}>
            复制并打开页面？
          </Checkbox>
        </Flex>

        <Flex gap={10}>
          <Button title="点击可复制h5移动端预览链接" onClick={() => handleCopy('h5')} icon={<Html5Outlined />}></Button>
          <Button
            title="点击可复制pcyy inner内嵌页预览链接"
            onClick={() => handleCopy('inner')}
            icon={<WindowsOutlined />}
          ></Button>
          <Button
            title="点击可复制pc web浏览器预览链接"
            onClick={() => handleCopy('pc')}
            icon={<ChromeOutlined />}
          ></Button>
        </Flex>
        <div>点击对应图标复制访问链接</div>
      </Space>

      <ParamsConfig type="page" />
    </Flex>
  )
}
