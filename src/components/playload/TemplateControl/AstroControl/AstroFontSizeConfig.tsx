import {Flex, Form, Select} from '@astro/ui/ui'
import config from 'src/config'

const AstroFontSizeConfig = () => {
  // 统一管理字号配置
  const fontSizeConfigs = [
    {
      key: 'h5',
      label: 'h5页',
      width: 160,
      defaultSize: '750',
      sizeInfo: config.h5FontSizeInfo,
    },
    {
      key: 'inner',
      label: '内嵌页',
      width: 90,
      defaultSize: '60',
      sizeInfo: config.innerFontSizeInfo,
    },
    {
      key: 'pc',
      label: 'web页',
      width: 90,
      defaultSize: '60',
      sizeInfo: config.pcFontSizeInfo,
    },
  ]

  // 生成选项列表
  const generateOptions = (sizeInfo: Record<string, string>) => {
    return [
      {value: null, label: `不配置${sizeInfo}字号`},
      ...Object.values(sizeInfo).map(size => ({
        label: size,
        value: size,
      })),
    ]
  }

  return (
    <Flex align={'flexStart'} gap={10}>
      <Form.Item label="根节点配置" />

      {fontSizeConfigs.map(({key, label, width, defaultSize, sizeInfo}) => (
        <Form.Item key={key} name={['ext', 'rootFontSize', key]} label={label} initialValue={sizeInfo[defaultSize]}>
          <Select options={generateOptions(sizeInfo)} style={{width}} mode="tags" maxCount={1} />
        </Form.Item>
      ))}
    </Flex>
  )
}

export default AstroFontSizeConfig
