import {CloseCircleOutlined, CopyOutlined} from '@astro/ui/icon'
import {type FormListActionType, ModalForm, ProForm, ProFormGroup, ProFormList, ProFormText} from '@astro/ui/pro'
import {Flex} from '@astro/ui/ui'
import {useRef} from 'react'
import appStore from 'src/components/dashboard/AppStore'
import type {IEnvInfo} from 'src/config/project'
import pageStore from '../editPanel/pageStore'

interface IEnvInfoForm {
  trigger: any
  info?: IEnvInfo
  refresh?: boolean
}
const requiredProps = {
  required: true,
  rules: [{required: true, message: '这是必填项'}],
}
function EnvInfoForm(props: IEnvInfoForm) {
  const {trigger, info, refresh} = props
  const appListState = appStore.state
  const pageState = pageStore.state
  const actionRef =
    useRef<
      FormListActionType<{
        name: string
      }>
    >()
  return (
    <ModalForm
      width="530px"
      title={'环境参数配置'}
      labelWidth="auto"
      trigger={trigger}
      modalProps={{
        destroyOnClose: true,
      }}
      onFinish={async (values: any) => {
        const {envKey, envValueList} = values
        const params = {
          envKey,
          envValueList,
        }
        const projectInfo = pageState.replaceEnvInfo(params)
        return await appListState.doSaveProject(projectInfo, refresh)
        // return true // 保存失败不关闭弹窗
      }}
      submitter={{
        render: (props, defaultDom) => [...defaultDom],
      }}
      initialValues={
        info
          ? {
              envKey: info?.envKey,
              envValueList: info.envValueList,
            }
          : {}
      }
    >
      <ProForm.Group>
        <ProFormText
          {...requiredProps}
          initialValue={'ServerTest'}
          width="md"
          name="envKey"
          disabled
          label="环境参数"
          tooltip="Astro平台的环境参数key，需要环境判断的可以组件内自行接入"
          placeholder="请输入环境参数的key值"
        />
      </ProForm.Group>

      <ProForm.Group>
        <ProFormList
          name="envValueList"
          label="环境参数列表"
          initialValue={[
            {
              value: '0',
              desc: '正式',
            },
            {
              value: '1',
              desc: '测试',
            },
          ]}
          // copyIconProps={false}
          // deleteIconProps={false}
          copyIconProps={{Icon: CopyOutlined, tooltipText: '复制此项到末尾'}}
          deleteIconProps={{
            Icon: CloseCircleOutlined,
            tooltipText: '删除',
          }}
          itemRender={({listDom, action}, {record}) => {
            const list = actionRef.current?.getList()
            console.log('list,action', list, action, record)
            return (
              <ProFormGroup key="group">
                {record?.value == 0 || record?.value == 1 ? (
                  <Flex style={{display: 'flex', alignItems: 'flex-end'}} gap={10}>
                    <ProFormText name="value" label="值" disabled />
                    <ProFormText name="desc" label="说明" disabled />
                  </Flex>
                ) : (
                  <Flex style={{display: 'flex', alignItems: 'flex-end'}} gap={10}>
                    <ProFormText
                      name="value"
                      label="值"
                      rules={[
                        {
                          validator: async (_, value) => {
                            if (value == null || value == 0 || value == 1) {
                              return Promise.reject(new Error('值不能为空，或者是0和1'))
                            }
                          },
                        },
                      ]}
                    />
                    <ProFormText name="desc" label="说明" rules={[{required: true, message: '这是必填项'}]} />
                    {action}
                  </Flex>
                )}
              </ProFormGroup>
            )
          }}
          actionRef={actionRef}
        >
          {/* <ProFormGroup key="group">
            <Flex gap={10}>
              <ProFormText name="value" label="值" />
              <ProFormText name="desc" label="说明" />
            </Flex>
          </ProFormGroup> */}
        </ProFormList>
      </ProForm.Group>
    </ModalForm>
  )
}

export default EnvInfoForm
