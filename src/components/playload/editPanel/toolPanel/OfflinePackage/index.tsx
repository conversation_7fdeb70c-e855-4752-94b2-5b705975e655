import {Button, Flex, List, Switch, message} from '@astro/ui/ui'
import React, {useState, useEffect, useRef} from 'react'
import {appConfigPush} from 'src/services/material'
import pageStore from '../../pageStore'

const OfflinePackage = () => {
  const [resourceList, setResourceList] = useState([])
  const resourceNameArray = useRef([]) // 获取前端render中使用的js和css文件
  const [isPush, setIsPush] = useState(false)
  const [isAll, setIsAll] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const pageState = pageStore.state

  const postOfflinePackage = async () => {
    setIsLoading(true)

    try {
      const res = await appConfigPush({
        projectId: pageState.projectId,
        pageKey: pageState.pageKey,
        push: isPush,
        all: isAll,
        urls: resourceNameArray.current,
      })
      if (res?.data?.result === 200) {
        const preview = JSON.parse(res?.data?.data?.preview ?? '[]')
        setResourceList(preview.map((item: any) => item?.url))
        message.success(`${isPush ? '上传' : '检索'}成功`)
      } else {
        message.error(res?.data.reason ?? `${isPush ? '上传' : '检索'}失败`)
      }
    } catch (error) {
      message.error(`${isPush ? '上传' : '检索'}失败`)
      console.error(error)
    }

    setIsLoading(false)
  }

  useEffect(() => {
    window.addEventListener('message', async (e: any) => {
      if (e.data && e.data.type === 'previewResource') {
        console.log('###收到previewResource消息', e.data)
        const payload = e.data?.payload
        resourceNameArray.current = payload.filter((item: string) => /astro\/js|astro\/css|@empjs/.test(item)) || []
      }
    })
  }, [])

  useEffect(() => {
    const iframe: any = document.querySelector('iframe[title="preview"]')
    if (iframe) {
      // 向preview获取resource数据
      iframe?.contentWindow.postMessage({type: 'checkResource'}, '*')
      console.log('###发送checkResource请求')
    }
  }, [])

  const check = () => {
    postOfflinePackage()
  }

  return (
    <Flex vertical gap={20}>
      <Flex gap={25} align={'center'}>
        <Flex gap={4}>
          <span>是否全量</span>
          <Switch
            checkedChildren="检索全部资源"
            unCheckedChildren="只检索js和css资源"
            value={isAll}
            onChange={setIsAll}
          />
        </Flex>

        <Flex gap={4}>
          <span>是否推送</span>
          <Switch checkedChildren="检索并推送" unCheckedChildren="只进行检索" value={isPush} onChange={setIsPush} />
        </Flex>

        <Button loading={isLoading} style={{width: 170}} type="primary" onClick={check}>
          检索{isPush ? '并上传' : ''}当前页面资源
        </Button>
      </Flex>

      {resourceList.length ? (
        <List
          style={{maxHeight: 200, overflowY: 'auto'}}
          size="small"
          bordered
          dataSource={resourceList}
          renderItem={(item: string) => (
            <List.Item>
              <a target="_blank" href={item} rel="noreferrer">
                {item}
              </a>
            </List.Item>
          )}
        />
      ) : null}
    </Flex>
  )
}
export default OfflinePackage
