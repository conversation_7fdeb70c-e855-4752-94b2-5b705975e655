import {Button, <PERSON>lapse, Flex, Form, Input, InputNumber, Modal, Popconfirm, Space, Switch, message} from '@astro/ui/ui'
import {Children, useRef} from 'react'
import userStore from 'src/components/common/user/userStore'
import FontSizeConfig from 'src/components/playload/TemplateControl/FontSizeConfig'
import pageStore from 'src/components/playload/editPanel/pageStore'
import {getPkgVersionByUrl, pkgNameParse} from 'src/utils'
import f2cStore from '../f2cStore'
import OfflinePackage from './OfflinePackage'

const HandlePageModal = (props: any) => {
  const formRef = useRef<any>(null)
  const pageState = pageStore.state
  const userState = userStore.state
  const f2cState = f2cStore.state
  const {handleType, setHandleType} = props
  const isEditing = handleType === 'edit'

  // const [templateList, setTemplateList] = useState([{value: null, label: '不使用模板'}])

  /* useEffect(() => {
    ;(async () => {
      // 获取 物料库清单
      const list = await getMLayout()
      console.log(`getMLayout`, list)
      const arr = [...templateList]
      list.map((layout: Component) => {
        const temp: any = {
          value: JSON.stringify({
            pkgName: pkgNameParse(layout.url || ''),
            path: layout.path,
          }),
          label: layout.name,
        }
        arr.push(temp)
      })
      console.log(`template list`, arr)
      setTemplateList(arr)
    })()
  }, []) */

  /*  useEffect(() => {
    if (pageState.page?.url && isEditing) {
      const rs = JSON.stringify({
        url: pageState.page.url,
        scope: pageState.page.scope,
        path: pageState.page.path,
      })
      templateListHandle(rs)
    }
  }, [isEditing]) */

  // 取消操作
  const handleCancel = () => {
    setHandleType('none')
  }
  const getFromDefault = () => {
    return {
      url: null,
      scope: null,
      path: null,
      online: true,
      pageKey: null,
      isDark: false,
      sort: Object.keys(pageState.pages).length + 1,
    }
  }
  const getPageDefaultTemplate = () => {
    if (pageState.page.url && pageState.page.path) {
      const rs = JSON.stringify({
        pkgName: pkgNameParse(pageState.page.url || ''),
        path: pageState.page.path,
      })
      return rs
    } else {
      return null
    }
  }

  const getPageDefaultTemplateVersion = () => {
    if (pageState.page?.url) {
      return getPkgVersionByUrl(pageState.page.url)
    } else {
      return '0.0.0'
    }
  }

  const handleFinish = async (value: any) => {
    // const oldDataConfig = pageState.clone(pageState?.page?.dataConfig ?? {})
    // 检查 是否用重复的 pageKey
    if (Object.keys(pageState.pages).includes(value.pageKey)) {
      if (!isEditing || (isEditing && value.pageKey !== pageState.pageKey)) {
        message.error('页面路径不能跟已有路径重复')
        return
      }
    }

    let {h5 = null, inner = null, pc = null} = value.ext.rootFontSize

    // 兼容select可填写时的数组格式转换
    if (Array.isArray(h5)) {
      h5 = h5?.[0]
    }
    if (Array.isArray(inner)) {
      inner = inner?.[0]
    }
    if (Array.isArray(pc)) {
      pc = pc?.[0]
    }

    const fontSizeConfig = {h5, inner, pc}

    // 只有当至少有一个值不为null时才序列化
    if (Object.values(fontSizeConfig).some(v => v !== null)) {
      value.ext.rootFontSize = JSON.stringify(fontSizeConfig)
    } else {
      value.ext.rootFontSize = null
    }

    // value.online = value.online || true
    // const template = JSON.parse(value.template)
    // value.pkgName = template?.pkgName
    // value.path = template?.path
    // value.url = `${config.unpkg}/${value.pkgName}@${value.template_version}/dist/emp.js`
    // const newScopeData = await getEMPJsonByNameAndVersion(value.pkgName, value.template_version)
    // if (newScopeData?.data?.id) {
    //   // console.log(`newScopeData`, newScopeData.data.id)
    //   value.scope = newScopeData.data.id
    // } else {
    //   value.url = ''
    //   value.scope = ''
    // }

    // //
    // delete value.template
    // delete value.template_version
    // delete value.pkgName

    // await pageState.SyncPageRemoteForm(value)

    if (handleType === 'add') {
      pageState.addPage(value.pageKey, pageState.clone(value))
    } else {
      // value.dataConfig = Object.assign(value?.dataConfig || {}, oldDataConfig)
      pageState.editPage(value.pageKey, value, pageState.pageKey, true)
    }
    /*  const d = await pageState.saveProjectConfig()
    if (d.err) {
      message.error('保存失败')
    } else {
      message.success('保存成功')
    } */
    /**
     * TODO 建议独立方法处理
     */
    // const groupId = value?.groupId
    // // 新建时根据groupid生成组件，编辑时判断当前组件列表为空才生成组件，组件列表不为空则更新样式
    // if (groupId && ((isEditing && (pageState.page.components ?? []).length <= 0) || !isEditing)) {
    //   await f2cState.syncf2cInfo(groupId)
    // } else if (groupId) {
    //   f2cState.getListGroupLatestVersion(groupId)
    // }
    // 设置小红点
    setHandleType('none')
  }

  const handleConfirm = () => {
    if (Object.keys(pageState.pages).length <= 1) {
      message.error('请保留至少一个页面')
      return
    }
    pageState.deletePage(pageStore.pageKey)
    // await pageState.saveProjectConfig()
    // message.success('删除成功')
    setHandleType('none')
  }

  /*  const templateListHandle = async (v: any) => {
    console.log(`[version]templateListHandle`, v)
    const info: any = JSONParse(v)
    const pkgName = info?.pkgName || pkgNameParse(info?.url || '')
    console.log(`[version]templateListHandle pkgName`, pkgName)
    if (!pkgName) {
      setVersionList([{value: '0.0.0', label: '0.0.0'}])
      ;(formRef.current as any).setFieldsValue({
        template_version: '0.0.0',
      })
      return
    } else {
      setVersionList([])
    }
    const versionList = await getDeployVersionByName(pkgName)
    if (versionList?.data?.versions) {
      const listOptions: any = versionList.data.versions.reverse().map((item: string) => {
        return {value: item, label: item}
      })
      setVersionList(listOptions)
      let finalVersion = getPageDefaultTemplateVersion()
      if (!versionList.data.versions.includes(finalVersion)) {
        finalVersion = versionList.data.versions[0]
      }
      ;(formRef.current as any).setFieldsValue({
        template_version: finalVersion,
      })
    }
  } */

  return (
    <Modal
      width={680}
      prefixCls="pageConfigModal"
      open={handleType !== 'none'}
      title={isEditing ? '编辑页面' : '新增页面'}
      footer={null}
      closable={true}
      maskClosable={true}
      onCancel={handleCancel}
      okButtonProps={{loading: pageState.submitProjectConfig}}
      onCancel={handleCancel}
      closeIcon={true}
    >
      <Form
        ref={formRef}
        disabled={pageState.submitProjectConfig === true}
        initialValues={(() => {
          const config = isEditing
            ? {
                ...getFromDefault(),
                ...pageState.clone(pageState.page),
                pageKey: pageState.pageKey,
              }
            : {
                ...getFromDefault(),
              }

          // 解析已有的rootFontSize配置
          if (isEditing && config.ext?.rootFontSize) {
            try {
              const parsedFontSize = JSON.parse(config.ext.rootFontSize ?? '{}')
              config.ext.rootFontSize = {
                h5: parsedFontSize.h5 || null,
                inner: parsedFontSize.inner || null,
                pc: parsedFontSize.pc || null,
              }
            } catch (e) {
              config.ext.rootFontSize = {
                h5: null,
                inner: null,
                pc: null,
              }
            }
          }

          console.log(`[version]initialValues`, config)
          return config
        })()}
        layout="horizontal"
        // okButtonProps={{loading: pageState.submitProjectConfig}}
        onFinish={async (value: any) => {
          handleFinish(value)
        }}
      >
        <Form.Item label="页面名称" name="name" rules={[{required: true}]}>
          <Input placeholder="如：宣发页" />
        </Form.Item>
        <Form.Item label="页面路径" name="pageKey" rules={[{required: true}]}>
          <Input placeholder="如：testpage" pattern="^[a-zA-Z0-9]*$" title="只能输入字母和数字" />
        </Form.Item>

        <Flex vertical gap={10}>
          <Collapse
            size="small"
            items={[
              {
                key: '1',
                forceRender: true,
                label: '更多页面配置',
                children: (
                  <>
                    <FontSizeConfig />

                    <Flex gap={20}>
                      <Form.Item name="online" label="是否上线">
                        <Switch />
                      </Form.Item>

                      <Form.Item
                        name="sort"
                        label="排序"
                        tooltip="页面在列表中的排序，数字越小越靠前，只在平台展示有效"
                      >
                        <InputNumber />
                      </Form.Item>

                      <Form.Item
                        name="isDark"
                        label="深色模式预览"
                        tooltip="开启后，页面将使用深色模式，只在平台预览有效"
                      >
                        <Switch />
                      </Form.Item>
                    </Flex>

                    <Collapse
                      size="small"
                      // bordered={false}
                      items={[
                        {
                          forceRender: true,
                          label: 'APP页面离线包配置',
                          children: <OfflinePackage />,
                        },
                      ]}
                    />
                  </>
                ),
              },
            ]}
          />
        </Flex>

        <Form.Item style={{textAlign: 'right', marginTop: '20px', marginBottom: '0'}}>
          <Space>
            {isEditing && (
              <Popconfirm
                onConfirm={handleConfirm}
                okButtonProps={{loading: pageState.submitProjectConfig}}
                title={'确认删除页面？'}
              >
                <Button color="danger" variant="solid">
                  删除
                </Button>
              </Popconfirm>
            )}

            {/* <Button color="default" variant="outlined" onClick={handleCancel}>
              取消
            </Button> */}
            <Button type="primary" htmlType="submit" loading={pageState.submitProjectConfig}>
              确定
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  )
}
export default HandlePageModal
