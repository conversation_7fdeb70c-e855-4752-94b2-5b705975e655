import {ImportOutlined} from '@astro/ui/icon'
import {Flex} from '@astro/ui/ui'
import {Button, Modal, Radio, Select, Space} from '@astro/ui/ui'
import {message} from '@astro/ui/ui'
import Editor from '@monaco-editor/react'
import {useEffect, useRef, useState} from 'react'
import pageStore from 'src/components/playload/editPanel/pageStore'
import drawerComponentStore from './DrawerComponent/componentStore'

const AddComponent = (props: any) => {
  const {configType = 'component'} = props
  const componentState = drawerComponentStore.state
  const pageState = pageStore.state
  const CodeType = props.codeType ?? ['json', 'javascript', 'html', 'scss']
  const [isModalOpen, setIsModalOpen] = useState(false)
  const editorRef = useRef(null)

  const showModal = () => {
    setIsModalOpen(true)
  }

  const handleOk = () => {
    setIsModalOpen(false)
    const componentStr = (editorRef && editorRef.current.getValue()) || ''
    const component = JSON.parse(componentStr)
    const componentItem = componentState.clone(component)
    if (configType === 'component' && !componentItem?.pageKey) {
      componentState.addComponent(componentItem, pageState)
    } else if (configType === 'page' && componentItem?.pageKey) {
      componentState.updatePageLayout(componentItem, pageState)
    } else {
      message.error('请填入正确的配置信息')
    }
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }

  // 适配传入的是kv对象
  const getInputValue = () => {
    if (typeof props.value === 'object') {
      return JSON.stringify(props.value)
    }
    return props.value
  }

  const defaultLang = props.initLang ? props.initLang : CodeType?.[0]
  const [lang, setLang] = useState(defaultLang)

  return (
    <>
      <span
        onClick={showModal}
        style={{marginRight: '5px', cursor: 'pointer'}}
        title={configType === 'page' ? '导入模板配置' : '导入组件'}
      >
        <ImportOutlined />
      </span>
      <Modal
        title={configType === 'page' ? '导入模板配置' : '导入组件'}
        width={600}
        open={isModalOpen}
        onCancel={handleCancel}
        destroyOnClose={true}
        footer={
          <Button key="ok" type="primary" onClick={handleOk}>
            确认
          </Button>
        }
      >
        <Editor
          language={lang}
          onChange={(d: any) => {
            if (lang === 'json') {
              // 获取编辑器实例并触发格式化命令
              const editor = editorRef.current
              if (editor) {
                editor.trigger('source', 'editor.action.formatDocument', undefined)
                props?.onChange && props?.onChange(editor.getValue())
              }
            } else {
              props?.onChange && props?.onChange(d)
            }
          }}
          width={'100%'}
          height={'500px'}
          value={getInputValue()}
          onMount={editor => {
            editorRef.current = editor
          }}
        />
      </Modal>
    </>
  )
}
export default AddComponent
