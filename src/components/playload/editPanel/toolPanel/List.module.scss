:global {
  .ant-card .ant-card-head-title {
    text-align: left;
  }

  .ant-card-small>.ant-card-body {
    padding: 0 4px;
  }

  .ant-list .ant-list-item .ant-list-item-action {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-inline-start: 0;
    margin-block-start: 6px;
  }

}

.pageConfiglist {
  margin: 10px auto;

  :global {
    .ant-avatar-group .ant-avatar {
      border: 0;
    }

    .ant-list-item .ant-list-item-action>li {
      padding: 0 14px;
    }
  }

  .title {
    position: relative;
  }

  .titleTag {
    font-size: 10px;
    position: absolute;
    top: -15px;
    left: -15px;
    font-weight: normal;
    background-color: #1677ff;
    color: #fff;
    padding: 0 5px;
    border-radius: 3px;
  }

  .tag {
    font-size: 12px;
    font-weight: normal;
    // color: #1677ff;
    color: #222;
    max-width: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowarp;
    display: block;
  }

  .item {
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    // flex-flow: wrap;

    .label {
      // max-width: 130px;
      // width: 100%;
      font-size: 12px;
      line-height: 20px;
      @include text-overflow();
    }

    .pageKey {
      // max-width: 80px;
      text-align: left;
      @include text-overflow();
    }
  }

  .activeItem {
    // background-color: rgba(136, 172, 224, 0.5);
    background-color: rgb(207, 227, 242);
    font-weight: bold;
  }
}

.ribbon {
  position: absolute;
  top: -10px;
  // right: -5px;
  // cursor: pointer;
  font-size: 10px;
}

.f2cIcon {
  content: url('https://f2c.yy.com/rspress-icon.png');
  width: 13px;
  height: 13px;
}