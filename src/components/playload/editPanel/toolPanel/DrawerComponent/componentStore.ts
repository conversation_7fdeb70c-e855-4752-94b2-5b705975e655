import pageStore, {type PageStore} from 'src/components/playload/editPanel/pageStore'
import type {Component, Page} from 'src/components/playload/editPanel/types'
import {searchMComponent} from 'src/services/api'
import type {searchKey} from 'src/services/api'
import {BaseStore} from 'src/store'
import {deepAssign} from 'src/utils'
import type {SubComponentStore} from '../SubComponentPanel/subComponentStore'

class ComponentStore extends BaseStore {
  componentList: Component[] = []
  total = 0
  selectIndex = -1
  searchComponentLoading = false
  async searchComponent(searchKey: searchKey) {
    console.log(
      'searchComponent tagTypeId',
      searchKey.tagTypeId,
      'componentType',
      searchKey.componentType,
      'tagIdsStr',
      searchKey.tagIds,
      'keywordStr',
      searchKey.keyword,
      'pageSize',
      searchKey.pageSize,
      'pageNo',
      searchKey.pageNo,
      'projectTag',
      searchKey.projectTag,
    )
    if (this.searchComponentLoading) return
    this.searchComponentLoading = true
    const componentRes = await searchMComponent(searchKey)
    this.componentList = componentRes?.list || []
    this.total = componentRes?.total || 1
    this.searchComponentLoading = false
  }
  async searchProjectComponent(project?: string) {
    console.log('searchProjectComponent project', project)
    const componentRes = await searchMComponent({project: project})
    return componentRes
  }
  async addComponent(component: Component, pageState: PageStore) {
    component.uniqueNum = `${pageState.pageKey}_${component.id}_${new Date().getTime()}`
    component.slotKey = ''
    pageState.addComponent(component)
    await pageState.getComponentForm()
  }
  async addSubComponent(component: Component, pageState: PageStore, subComponentState: SubComponentStore) {
    component.uniqueNum = `${pageState.pageKey}_${pageState.componentKey}_${component.id}_${new Date().getTime()}`
    component.slotKey = ''
    subComponentState.addComponent(component)
  }
  async updatePageLayout(component: Component | null, pageState: PageStore) {
    const page = pageStore.clone(pageState.page) as Page
    if (!component) {
      // 清除 page 里面 所有 layout 与 dataConfig 的信息
      page.url = ''
      page.path = ''
      page.scope = ''
      page.dataConfig = {}
    } else {
      const {url, path, scope, dataConfig} = component
      page.url = url
      page.path = path
      page.scope = scope
      page.dataConfig = deepAssign({}, {...page.dataConfig, ...(dataConfig || {})})
      //
      await pageState.SyncPageRemoteForm(page)
    }
    await pageState.editPage(pageState.pageKey, page, pageState.pageKey, true)
  }

  setSelectIndex(index: number) {
    this.selectIndex = index
  }
}

export default new ComponentStore()
