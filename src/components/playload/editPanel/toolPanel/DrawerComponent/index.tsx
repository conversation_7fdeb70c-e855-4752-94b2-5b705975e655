import { LayoutOutlined, PlusOutlined } from '@astro/ui/icon'
import { CheckCard, ProCard } from '@astro/ui/pro'
import { Button, Drawer, Empty, Flex, type SelectProps, message } from '@astro/ui/ui'

import React, { useEffect, useRef, useState } from 'react'
import { defaultPng } from 'src/components/Material/common/utils'
import SearchSelector from 'src/components/common/SearchSelector'
import { Loading } from 'src/components/common/status'
import pageStore from 'src/components/playload/editPanel/pageStore'
import type { Component } from 'src/components/playload/editPanel/types'
import { pkgNameParse } from 'src/utils'

import debounce from 'lodash.debounce'

import componentStore from './componentStore'
import './index.module.scss'
import subComponentStore from '../SubComponentPanel/subComponentStore'
interface AddComponentModalProps {
  defaultSelectSearch?: SelectProps['options']
  configType?: 'page' | 'component' | 'subComponent'
}

// 组件卡片组件
const ComponentCard = ({ data, index, configType, pageState, componentState }: any) => {
  const imgRef = useRef<HTMLImageElement>(null)
  if (configType === 'page') {
    // const pkgName = pkgNameParse(data?.url)
    const { url, path } = pageState.page
    const { selectIndex, setSelectIndex } = componentState
    if (url && data?.url && data.path === path) {
      const pagePkgName = pkgNameParse(url)
      const componentPkgName = pkgNameParse(data.url)
      if (pagePkgName === componentPkgName && selectIndex < 0) setSelectIndex(index)
    }
  }
  return (
    <div>
      {/* <Col span={8}> */}
      <CheckCard
        value={index}
        cover={
          <div>
            <img
              ref={imgRef}
              className="itemCardImg"
              alt={data?.title}
              src={data?.img || '/default.png'}
              onError={e => {
                console.log('Error loading image:', e)
                const target = imgRef.current
                if (target && (!target.dataset.error || target.dataset.error === 'false')) {
                  target.src = defaultPng
                  target.dataset.error = 'true'
                }
              }}
            />
            <p className="itemTitle">{data?.name || '组件标题'}</p>
          </div>
        }
      />
    </div>
    //  </Col>
  )
}

const ComponentList = ({ configType, debounceAddComponent }: any) => {
  const componentState = componentStore.state
  const pageState = pageStore.state
  if (componentState.searchComponentLoading) {
    return <Loading />
  } else if (componentState.componentList.length === 0) {
    return <Empty />
  }
  return (
    <div className="astro-add-component">
      <CheckCard.Group
        value={componentState.selectIndex}
        onChange={(index: any) => {
          debounceAddComponent(componentStore.clone(componentState.componentList[index]))
        }}
        size="small"
      >
        {/* <Row gutter={[16, 0]}> */}
        <div className="comList">
          {componentState.componentList.map((item, index) => (
            <ComponentCard
              key={`item-${index}`}
              index={index}
              componentState={componentState}
              pageState={pageState}
              configType={configType}
              data={componentStore.clone(item)}
            />
          ))}
        </div>
        {/* </Row> */}
      </CheckCard.Group>
    </div>
  )
}

const DrawerComponent = ({ configType = 'component' }: AddComponentModalProps) => {
  const componentState = componentStore.state
  const pageState = pageStore.state
  const subComponentState = subComponentStore.state
  const IconName = configType === 'page' ? LayoutOutlined : PlusOutlined
  const [open, setOpen] = React.useState<boolean>(false)
  // const [componentItem, setComponentItem] = useState<Component | null>(null)

  const openDrawer = () => {
    componentState.setSelectIndex(-1)
    setOpen(true)
  }
  const handleAddComponent = async (componentItem: Component) => {
    if (!componentItem) {
      if (configType === 'page') {
        await componentState.updatePageLayout(componentItem, pageState)
      }
      return setOpen(false)
    }

    if (configType === 'component') {
      await componentState.addComponent(componentItem, pageState)
    } else if (configType === 'subComponent') {
      await componentState.addSubComponent(componentItem, pageState, subComponentState)
    } else if (configType === 'page') {
      await componentState.updatePageLayout(componentItem, pageState)
    }
    setOpen(false)
  }

  const debounceAddComponent = debounce(handleAddComponent, 200)

  return (
    <>
      <Drawer
        closable={false}
        title={
          <Flex justify={'space-between'}>
            <div className="searchTitle">
              <SearchSelector open={open} configType={configType} />
              {/* <Button
                type="primary"
                onClick={() => {
                  debounceAddComponent()
                }}
                style={{
                  marginLeft: '15px',
                }}
                disabled={!componentItem}
              >
                确定
              </Button> */}
            </div>
          </Flex>
        }
        prefixCls={'drawerComponent'}
        width={600}
        // destroyOnClose
        // closable={false}
        placement="right"
        open={open}
        onClose={() => setOpen(false)}
      >
        <ProCard>
          <Flex gap={10} vertical>
            <ComponentList configType={configType} debounceAddComponent={debounceAddComponent} />
          </Flex>
        </ProCard>
      </Drawer>
      <IconName
        style={pageState?.page?.url && configType === 'page' ? { color: '#1677ff' } : {}}
        onClick={openDrawer}
        title={configType === 'page' ? '添加模板' : '添加组件'}
      />
    </>
  )
}

export default DrawerComponent
