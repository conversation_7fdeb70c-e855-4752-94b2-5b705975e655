:global {

  .drawerComponent {
    // z-index: 10002 !important;
    z-index: 1002 !important; // 超过1000会导致下拉菜单，全局message提示框等层级不够高的问题


    // .addComponentModal-body {
    //   padding: 0 20px;
    //   @include scroll-bar(2px, #c1bfbf, 0.5, 0.8);
    // }
  }

  .ant-pro-checkcard {
    width: 100%;
  }

  .componentCard {
    .ant-card-body {
      padding: 14px;

      .ant-card-meta-detail,
      .ant-card-meta-description {
        @include text-overflow();
      }
    }
  }


  .componentCardItem {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 8px 8px 0 0;
  }

  .itemCardImg {
    height: 150px;
    object-fit: contain;
    border-radius: 0 !important;
    // transition: all 0.3s;

    // &:hover {
    //   transform: scale(1.1);
    // }
  }

  .tagList {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-top: -5px;
  }

  .astro-add-component {
    .itemTitle {
      font-size: 12px;
      text-align: center;
    }

    .ant-pro-checkcard-cover {
      padding-inline: 0px;
      padding-block: 0px;
    }
  }

  .searchTitle {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
  }

  .comList {
    display: block;
    column-count: 3;
    width: 100%;
  }
}