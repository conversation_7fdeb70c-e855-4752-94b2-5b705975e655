import {QuestionCircleOutlined} from '@astro/ui/icon'
import {Flex, Tag, Tooltip} from '@astro/ui/ui'
import pageStore from '../pageStore'

const PlatformParams = () => {
  const pageState = pageStore.state
  const templateControl = pageState.templateControl

  return (
    <Flex gap={10}>
      <Flex align="center" gap={4}>
        <span>平台参数</span>
        <Tooltip title="平台预设的环境参数，可通过顶部参数面板设置修改平台参数">
          <QuestionCircleOutlined style={{color: '#999'}} />
        </Tooltip>
      </Flex>
      <Flex wrap="wrap" gap={8}>
        {templateControl?.hasEnv ? (
          <Tag key={templateControl.curEnvKey} color="red">
            {templateControl.curEnvKey} = {templateControl.curEnvValue}
          </Tag>
        ) : (
          <>无</>
        )}
      </Flex>
    </Flex>
  )
}

export default PlatformParams
