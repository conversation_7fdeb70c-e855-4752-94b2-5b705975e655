.container {
  position: relative;
  border: 1px dashed gray;
  border-radius: 8px;
  padding: 5px 10px;
  min-height: 50px;
  cursor: move;

  .ribbon {
    position: absolute;
    top: -25px;
    right: -18px;
    cursor: pointer;
    font-size: 10px;
  }

  .subComponentItem {
    text-align: left;
    margin-left: 10px;
    font-size: 11px;
    color: rgb(155, 149, 149);
  }

}

.popover {
  font-size: 12px;
}

.select {
  font-size: 12px;
  width: 90%;

  [class^="ant-select"],
  [class*=" ant-select"] {
    font-size: 12px;
  }
}