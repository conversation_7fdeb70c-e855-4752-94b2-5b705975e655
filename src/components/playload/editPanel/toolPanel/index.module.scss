:global {
  .ant-card .ant-card-head-title {
    text-align: left;
  }

  .ant-card-small>.ant-card-body {
    padding: 0 4px;
  }
}

.cardWrap {
  width: 90%;
  margin: 10px auto 0;

  .pageConfiglist {
    margin: 10px auto;

    .item {
      padding: 10px 8px;
      border-radius: 6px;
      cursor: pointer;
    }

    .activeItem {
      background-color: #f0f0f0;
      font-weight: bold;
    }
  }
}

.debug {
  padding: 20px;

  li,
  .ant-ribbon-text {
    font-size: 12px !important;
  }

  h3 {
    font-size: 14px;
    margin: 0;
    padding: 0;
  }
}

.f12 {
  font-size: 12px;
}

.hoverTag {
  margin-right: 0;
  border: none;
  border-radius: 6px;
  background: rgba(207, 227, 242, 0.3);

  &:hover {
    background: rgba(207, 227, 242, 0.5);
  }
}