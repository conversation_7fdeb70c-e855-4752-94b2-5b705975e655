import {Button, Flex, Image, Modal, Table, Tag, message} from '@astro/ui/ui'
import {useEffect, useState} from 'react'
import {defaultPng} from 'src/components/Material/common/utils'
import VersionSelect from 'src/components/common/VersionSelect'
import pageStore from 'src/components/playload/editPanel/pageStore'
import {getEMPJsonByNameAndVersion} from 'src/services/unpkg'
import {getPkgVersionByUrl, pkgNameParse} from 'src/utils'
import type {Component} from '../types'

interface VersionManagementModalProps {
  open: boolean
  onCancel: () => void
  pageKey: string
}

interface ComponentRecord {
  name: string
  img: string
  path: string
  scope: string
  slotKey: string | null
  url: string
  index: number
  project?: string
  selectedVersion?: string // 添加选中的版本
}

const VersionManagementModal = ({open, onCancel, pageKey}: VersionManagementModalProps) => {
  const [loading, setLoading] = useState(false)
  const [componentList, setComponentList] = useState<ComponentRecord[]>([])
  const [selectedComponent, setSelectedComponent] = useState<number | null>(null)
  const [versionChanges, setVersionChanges] = useState<Record<number, string>>({}) // 记录每个组件选择的版本
  const [batchUpdating, setBatchUpdating] = useState(false) // 批量更新状态

  const pageState = pageStore.state
  const page = pageState.page

  useEffect(() => {
    if (open && pageKey) {
      fetchComponentList()
    }
  }, [open, pageKey])

  const fetchComponentList = async () => {
    setLoading(true)
    try {
      if (page && page.components && page.components.length > 0) {
        const list = page.components.map((component, index) => ({
          name: component.name || '未命名组件',
          img: component.img || defaultPng,
          path: component.path || '',
          scope: component.scope || '',
          slotKey: component.slotKey || '',
          url: component.url || '',
          project: component.project || pkgNameParse(component.url || ''),
          selectedVersion: getPkgVersionByUrl(component.url || ''),
          index,
        }))
        list.push({
          name: page.name || '未命名页面',
          img: defaultPng,
          path: page.path || '',
          scope: page.scope || '',
          slotKey: '',
          url: page.url || '',
          project: pkgNameParse(page.url || ''), // 从path中提取project信息
          selectedVersion: getPkgVersionByUrl(page.url || ''),
          index: page.components.length,
        })
        setComponentList(list)
      } else {
        setComponentList([])
      }
    } catch (error) {
      console.error('获取组件列表失败:', error)
      message.error('获取组件列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 根据project属性对数据进行分组
  const groupByProject = (data: ComponentRecord[]) => {
    const groupedData: Record<string, ComponentRecord[]> = {}

    data.forEach(item => {
      const project = item.project || pkgNameParse(page.url || '') || '未分类'
      if (project !== '未分类') {
        if (!groupedData[project]) {
          groupedData[project] = []
        }
        groupedData[project].push(item)
      }
    })

    // 转换为Table需要的格式
    return Object.entries(groupedData).map(([project, items]) => {
      // 获取该分组下第一个组件的当前版本作为默认版本
      const defaultVersion = items.length > 0 && items[0].url ? getPkgVersionByUrl(items[0].url) : ''

      return {
        project,
        key: project,
        children: items,
        defaultVersion, // 添加默认版本信息
      }
    })
  }

  // 处理版本选择
  const handleVersionSelect = async (index: number, version: string) => {
    setVersionChanges(prev => ({
      ...prev,
      [index]: version,
    }))

    // 更新组件列表中的选中版本
    setComponentList(prev => prev.map(item => (item.index === index ? {...item, selectedVersion: version} : item)))
  }

  // 处理项目分类版本统一切换
  const handleGroupVersionSelect = async (project: string, version: string) => {
    // 找出该项目下的所有组件
    const projectComponents = componentList.filter(
      item => (item.project || pkgNameParse(page.url || '') || '未分类') === project,
    )

    // 为该项目下的所有组件设置相同的版本
    const newVersionChanges = {...versionChanges}
    projectComponents.forEach(component => {
      if (component.url) {
        newVersionChanges[component.index] = version
      }
    })
    setVersionChanges(newVersionChanges)

    // 更新组件列表中的选中版本
    setComponentList(prev =>
      prev.map(item => {
        if ((item.project || pkgNameParse(page.url || '') || '未分类') === project && item.url) {
          return {...item, selectedVersion: version}
        }
        return item
      }),
    )

    message.success(`已为 ${project} 下的所有组件选择版本 ${version}`)
  }

  // 批量更新版本
  const batchUpdateVersions = async () => {
    if (Object.keys(versionChanges).length === 0) {
      message.info('没有需要更新的组件版本')
      return
    }

    setBatchUpdating(true)
    try {
      // 初始化为当前页面下的所有组件的克隆，确保可修改
      const updatedComponents = page?.components ? [...page.components.map(comp => pageStore.clone(comp))] : []
      let pageNeedsUpdate = false
      let updatedPage = null

      // 遍历所有需要更新的组件
      for (const [indexStr, version] of Object.entries(versionChanges)) {
        const index = Number.parseInt(indexStr)
        const component = index === (page?.components && page?.components.length) ? page : page?.components?.[index]

        if (!component || !component.url) continue

        const pkgName = pkgNameParse(component.url)
        const componentPath = component.path || ''

        // 获取新版本的作用域信息
        const empData = await getEMPJsonByNameAndVersion(pkgName, version)

        if (empData?.data?.exposes && empData.data.exposes.some((item: any) => item.path === componentPath)) {
          const componentClone = pageStore.clone(component)
          const oldVersion = getPkgVersionByUrl(componentClone.url || '')
          componentClone.scope = empData.data.id
          componentClone.url = componentClone.url?.replace(oldVersion, version)

          // 区分页面和组件
          if (componentClone.pageKey) {
            // 更新页面
            pageNeedsUpdate = true
            updatedPage = componentClone
          } else {
            // 在updatedComponents中查找对应组件的索引
            updatedComponents.splice(index, 1, componentClone)
          }
        } else {
          message.error(`${version}版本不存在${componentPath}的组件`)
        }
      }

      // 批量更新组件
      if (updatedComponents.length > 0) {
        pageStore.updateComponents(updatedComponents)

        // 如果页面也需要更新，需要同步更新页面中的组件信息
        if (updatedPage?.components) {
          updatedPage.components = updatedComponents
        }

        // 并行处理所有组件的表单获取
        await Promise.all(updatedComponents.map(component => pageStore.getComponentForm(component)))
      }

      // 更新页面
      if (pageNeedsUpdate && updatedPage) {
        await pageStore.editPage(pageKey, updatedPage, pageKey, true)
      }

      if (updatedComponents.length > 0) {
        message.success(`已执行${Object.keys(versionChanges).length}个组件的版本`)
        setVersionChanges({})
        onCancel()
      }
    } catch (error) {
      message.error('批量更新版本失败')
    } finally {
      setBatchUpdating(false)
    }
  }

  const columns = [
    {
      title: '包名',
      dataIndex: 'project',
      key: 'project',
      width: 380,
      fixed: 'left' as const,
    },
    {
      title: '组件',
      dataIndex: 'path',
      key: 'path',
      width: 130,
      ellipsis: true,
      fixed: 'left' as const,
    },
    {
      title: '当前版本',
      dataIndex: 'url',
      key: 'currentVersion',
      width: 150,
      render: (url: string) => (url ? getPkgVersionByUrl(url) : '-'),
    },
    {
      title: '组件目标版本',
      key: 'targetVersion',
      width: 320,
      render: (_: any, record: any) => {
        // 如果是父级行（项目分类），显示项目统一切换版本的选择器
        if (record.children && record.children.length > 0) {
          // 找出该项目下第一个有URL的组件作为参考
          const referenceComponent = record.children.find((item: ComponentRecord) => item.url)
          if (referenceComponent) {
            return (
              <Flex vertical gap={4}>
                <VersionSelect
                  currentSelectedComponent={
                    referenceComponent.index === (page?.components?.length || 0)
                      ? page
                      : page?.components?.[referenceComponent.index]
                  }
                  showSubmitButton={false}
                  onVersionSelect={version => handleGroupVersionSelect(record.project, version)}
                  selectedVersion={record.defaultVersion}
                />
              </Flex>
            )
          }
        } else if (record.url) {
          // 如果是子行（组件），显示组件版本选择器
          return (
            <VersionSelect
              currentSelectedComponent={
                record.index === (page?.components?.length || 0) ? page : page?.components?.[record.index]
              }
              showSubmitButton={false}
              onVersionSelect={version => handleVersionSelect(record.index, version)}
              selectedVersion={record.selectedVersion}
            />
          )
        }
        return null
      },
    },
    {
      title: '组件图片',
      dataIndex: 'img',
      key: 'img',
      width: 100,
      render: (img: string) =>
        img ? (
          <Image
            src={img || defaultPng}
            alt="组件图片"
            width={50}
            height={50}
            fallback={defaultPng}
            style={{objectFit: 'contain'}}
          />
        ) : null,
    },
    {
      title: '组件名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },

    {
      title: '作用域',
      dataIndex: 'scope',
      key: 'scope',
      width: 400,
    },
    {
      title: '插槽',
      dataIndex: 'slotKey',
      key: 'slotKey',
      width: 200,
      render: (slotKey: string | null) => (slotKey ? <Tag color="green">{slotKey}</Tag> : '-'),
    },
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      width: 600,
      ellipsis: true,
    },
  ]

  return (
    <Modal
      title="页面组件列表"
      open={open}
      onCancel={onCancel}
      width={1000}
      footer={[
        <Button
          key="update"
          type="primary"
          onClick={batchUpdateVersions}
          loading={batchUpdating}
          disabled={Object.keys(versionChanges).length === 0}
        >
          批量更新版本 ({Object.keys(versionChanges).length})
        </Button>,
        <Button key="cancel" onClick={onCancel}>
          关闭
        </Button>,
      ]}
    >
      <Flex vertical gap={16}>
        <div>当前页面: {pageKey}</div>
        {selectedComponent !== null && (
          <div>已选择组件: {componentList.find(v => v.index === selectedComponent)?.name}</div>
        )}
        <Table
          rowKey={(record: {key?: string; index?: number}) => record.key || `${record.index}`}
          columns={columns}
          dataSource={groupByProject(componentList)}
          loading={loading}
          pagination={false}
          scroll={{x: 1000, y: 400}}
          rowClassName={(record: any) => (record.index === selectedComponent ? 'ant-table-row-selected' : '')}
          expandable={{
            defaultExpandAllRows: true,
          }}
        />
      </Flex>
    </Modal>
  )
}

export default VersionManagementModal
