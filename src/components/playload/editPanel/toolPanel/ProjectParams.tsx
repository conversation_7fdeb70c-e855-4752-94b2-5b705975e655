import {QuestionCircleOutlined} from '@astro/ui/icon'
import {Flex, Tag, Tooltip} from '@astro/ui/ui'
import pageStore from '../pageStore'

const ProjectParams = () => {
  const pageState = pageStore.state
  const projectUrlParams = pageState.projectInfo?.projectConfig?.projectUrlParams

  const hasParams = projectUrlParams && Object.keys(projectUrlParams).length > 0
  const hasSelectedParams = hasParams && Object.values(projectUrlParams).some((value: any) => value?.select?.length > 0)

  return (
    <Flex gap={10}>
      <Flex align="center" gap={4}>
        <span>项目参数</span>
        <Tooltip title="项目统一添加的参数，可通过项目设置修改项目参数">
          <QuestionCircleOutlined style={{color: '#999'}} />
        </Tooltip>
      </Flex>
      <Flex wrap="wrap" gap={8}>
        {hasSelectedParams ? (
          Object.entries(projectUrlParams).map(
            ([key, value]: [string, any]) =>
              value?.select?.length > 0 && (
                <Tag key={key} color="blue">
                  {key} = {value.select.join(',')}
                </Tag>
              ),
          )
        ) : (
          <>无</>
        )}
      </Flex>
    </Flex>
  )
}

export default ProjectParams
