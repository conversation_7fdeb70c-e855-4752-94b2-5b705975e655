import {QuestionCircleOutlined} from '@astro/ui/icon'
import {Flex, Input, Popconfirm, Popover, Space} from '@astro/ui/ui'
import {useState} from 'react'
import f2cStore from '../f2cStore'
import pageStore from '../pageStore'

export const F2CContent = ({close}: any) => {
  const pageState = pageStore.state
  const f2cState = f2cStore.state
  const [open, setOpen] = useState(false)
  const [newGroupId, setNewGroupId] = useState<any>()

  const dealF2CData = async (value: string) => {
    const curGroupId = pageState.pages[pageState.pageKey]?.groupId
    setNewGroupId(value)
    if (!curGroupId || curGroupId == value) {
      serchF2CInfo(value)
    } else {
      setOpen(true)
    }
  }
  const dealPopconfirm = (groupId: string, change = true) => {
    serchF2CInfo(groupId, change)
  }
  const serchF2CInfo = async (value: string, change = true) => {
    change && pageState.editPage(pageState.pageKey, {groupId: value}, pageState.pageKey, true)
    // 新建时根据groupid生成组件，编辑时判断当前组件列表为空才生成组件，组件列表不为空则更新样式
    if (value) {
      f2cState.changeF2cInfo(value)
    }
    close?.()
    setOpen(false)
  }
  return (
    <Flex gap={10}>
      <Space direction="horizontal" align="center">
        <div style={{fontWeight: 'bold'}}>{f2cState.hasCom ? '使用F2C换肤' : '使用F2C创建页面'}</div>
        <Popover key={'F2CContent'} content="请输入F2C生成的groupId" trigger="hover">
          <QuestionCircleOutlined style={{cursor: 'pointer'}} />
        </Popover>
        :
        <Popconfirm
          title="警告"
          description="当前goupId跟之前的不一致，是否重新匹配?"
          open={open}
          onConfirm={() => {dealPopconfirm(newGroupId)}}
          onCancel={() => {dealPopconfirm(newGroupId, false)}}
          okText="替换"
          cancelText="不替换"
        >
          <Input.Search
            placeholder="请输入F2C生成的groupId"
            defaultValue={pageState.page?.groupId}
            enterButton="确定"
            allowClear
            onSearch={dealF2CData}
          />
        </Popconfirm>
      </Space>
    </Flex>
  )
}
