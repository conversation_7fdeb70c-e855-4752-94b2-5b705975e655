import {FileAddOutlined, LinkOutlined, PlusOutlined} from '@astro/ui/icon'
import {Badge, Card, Flex, List, Tag, Typography} from '@astro/ui/ui'
import type React from 'react'
import {useState} from 'react'
import pageStore from 'src/components/playload/editPanel/pageStore'
import AddComponent from './AddComponent'
import ComponentList from './ComponentList'
import DrawerComponent from './DrawerComponent'
import PageList from './List'
import styles from './index.module.scss'

const DebugComp = () => {
  const debug = false
  const pageState = pageStore.state
  const data = [
    {k: 'PageName', v: pageState.page?.name},
    {k: 'PageKey', v: pageState.pageKey},
    {k: 'ComponentKey', v: pageState.componentKey},
    {k: 'CompSlotKey', v: pageState.component?.slotKey || '-'},
    {k: 'CompName', v: pageState.component?.name || '-'},
  ]
  return debug ? (
    <List
      className={styles.debug}
      header={<h3>Debug</h3>}
      size="small"
      dataSource={data}
      renderItem={item => (
        <Badge.Ribbon className={styles.f12} text={item.v}>
          <List.Item>{item.k}</List.Item>
        </Badge.Ribbon>
      )}
    />
  ) : (
    <></>
  )
}
const ToolPanel = () => {
  const pageState = pageStore.state
  const [handleType, setHandleType] = useState<'add' | 'edit' | 'none'>('none')

  return (
    <Flex vertical>
      {/* 书签配置 */}
      {pageState.projectInfo?.bookmarks?.length ? (
        <Card className={styles.cardWrap} title="" size="small" bodyStyle={{padding: '8px'}}>
          <Flex wrap="wrap" gap={2}>
            {(pageState.projectInfo?.bookmarks || []).map((item: {label: string; link: string}, index: number) => (
              <Typography.Link key={index} href={item.link} target="_blank">
                <Tag icon={<LinkOutlined />} className={styles.hoverTag}>
                  {item.label}
                </Tag>
              </Typography.Link>
            ))}
          </Flex>
        </Card>
      ) : null}

      {/* 页面配置 */}
      <Card
        className={styles.cardWrap}
        title="页面配置"
        size="small"
        extra={
          <FileAddOutlined
            title="添加页面"
            onClick={() => {
              setHandleType('add')
            }}
          />
        }
      >
        <PageList handleType={handleType} setHandleType={setHandleType} />
      </Card>

      {/* 组件配置 */}
      {pageState.pageKey && pageState.page ? (
        <Card
          className={styles.cardWrap}
          // title={`${pageState.page?.name} 组件配置`}
          title={`组件配置`}
          size="small"
          extra={
            <>
              <AddComponent />
              <DrawerComponent />
            </>
          }
        >
          {pageState.page?.components && <ComponentList key={`${pageState.pageKey}_componentList`} />}
        </Card>
      ) : (
        <></>
      )}

      <DebugComp />
    </Flex>
  )
}
export default ToolPanel
