import {QuestionCircleOutlined} from '@astro/ui/icon'
import {Button, Flex, Input, Modal, Tooltip} from '@astro/ui/ui'
import type React from 'react'
import {useState} from 'react'
import type {Component} from 'src/components/playload/editPanel/types'
import pageStore from '../pageStore'

const ComponentName = (props: {component: Component}) => {
  const {component} = props
  const pageState = pageStore.state
  const [openAliasModal, setOpenAliasModal] = useState(false)
  const [aliasValue, setAliasValue] = useState(component.alias || '')

  const handleAliasChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAliasValue(e.target.value)
  }

  const saveAlias = () => {
    pageState.updateComponentItem('alias', aliasValue)
    setOpenAliasModal(false)
  }

  const handleDoubleClick = () => {
    setOpenAliasModal(true)
    setAliasValue(component.alias || '')
  }

  return (
    <Flex vertical={true} gap={4} align="flex-start">
      <div onDoubleClick={handleDoubleClick} style={{cursor: 'pointer'}}>
        {component.alias ? (
          <Flex>
            <span>{component.alias}</span>
            <span style={{color: 'gray', fontSize: '10px', maxWidth: '100px'}}>（{component.name}）</span>
          </Flex>
        ) : (
          <>{component.name}</>
        )}
      </div>

      <Modal
        title={
          <Flex gap={6}>
            <span>设置组件别名</span>
            <Tooltip title="设置的组件别名仅在当前组件中生效">
              <QuestionCircleOutlined />
            </Tooltip>
          </Flex>
        }
        width={400}
        open={openAliasModal}
        onCancel={() => setOpenAliasModal(false)}
        footer={[
          <Button key="cancel" size="small" onClick={() => setOpenAliasModal(false)}>
            取消
          </Button>,
          <Button key="save" size="small" type="primary" onClick={saveAlias}>
            保存
          </Button>,
        ]}
      >
        <Input value={aliasValue} onChange={handleAliasChange} placeholder="请输入组件别名" autoFocus />
      </Modal>
    </Flex>
  )
}
export default ComponentName
