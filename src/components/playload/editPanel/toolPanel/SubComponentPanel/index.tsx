import { ApartmentOutlined } from '@astro/ui/icon'
import { <PERSON><PERSON>, <PERSON>, Drawer, <PERSON>lex, Popover } from '@astro/ui/ui'
import React, { useState, useEffect, useCallback } from 'react'
import './index.scss'
import pageStore from '../../pageStore'
import DrawerComponent from '../DrawerComponent'
import ComponentList from './ComponentList'
import ConfigurePanel from './ConfigurePanel'
import subComponentStore from './subComponentStore'

const SubComponentPanel = () => {
  const [isOpen, setIsOpen] = useState(false) // 子组件面板
  const pageState = pageStore.state
  const subComponentState = subComponentStore.state

  const handleCancel = () => {
    subComponentState.updateComponents([])
    setIsOpen(false)
  }

  const handleOk = useCallback(() => {
    // 提交子组件列表到组件
    pageState.updateComponentItem('subComponents', subComponentState.clone(subComponentState.components))

    // 提交完成，情况子组件列表，关闭滑出层
    subComponentState.updateComponents([])
    setIsOpen(false)
  }, [subComponentState.components, subComponentState.component.dataConfig, subComponentState.component.slotKey])

  const RenderContent = () => {
    useEffect(() => {
      if (!isOpen) return
      subComponentState.updateComponents(subComponentStore.clone(pageState?.component?.subComponents) ?? [])
    }, [isOpen, pageState?.component])

    return (
      <>
        {/* 子组件列表 */}
        <Flex vertical justify="space-between" gap={10}>
          <Card title={`子组件列表`} size="small" extra={<DrawerComponent configType="subComponent" />}>
            {(subComponentState.components?.length || 0) > 0 ? (
              <ComponentList key={`${pageState.pageKey}_${pageState.componentKey}_subComponentList`} />
            ) : (
              <div className="empty">暂无配置</div>
            )}
          </Card>

          <Flex justify="flex-end" gap={10}>
            <Button onClick={handleCancel}>取消</Button>
            <Button onClick={handleOk} type="primary">
              确认
            </Button>
          </Flex>
        </Flex>

        {/* 子组件配置面板 */}
        <ConfigurePanel />
      </>
    )
  }

  return (
    <Popover
      className="card-wrap"
      rootClassName="sub-component-popover"
      content={RenderContent()}
      trigger="click"
      placement="right"
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <ApartmentOutlined />
    </Popover>
  )
}
export default SubComponentPanel
