import update from 'immutability-helper'
import config from 'src/config'
import {BaseStore} from 'src/store'
import {subscribe} from 'valtio'
import {type Component, FormItemType} from '../../types'
const {isDev, localDevEmpConfig} = config

export class SubComponentStore extends BaseStore {
  constructor() {
    super()
    subscribe(this.formCache, () => {
      localStorage.setItem('subComponentFormCache', JSON.stringify(this.formCache))
    })
  }

  components?: Component[] // 子组件列表
  componentKey = -1 // 当前选中子组件key
  isOpenConfiure = false // 是否打开子组件配置面板
  isGetForm = false
  formCache: any = JSON.parse(localStorage.getItem('subComponentFormCache') || `{}`)
  sformType?: string = 'subComponent'

  get component() {
    const components: Component[] = this.components ?? []
    if (this.componentKey > -1 && components && components?.length > 0) {
      return components[this.componentKey]
    }
    return {}
  }

  get componentForm() {
    const {url, path}: any = this.component || {}
    if (url && path) return this.formCache[`${url}-${path}`] ? this.formCache[`${url}-${path}`] : []
    return []
  }

  // 更新子组件列表
  updateComponents(components: Component[]) {
    this.resetComponent()
    this.components = components
  }

  // 添加子组件
  addComponent(component: Component) {
    if (!this.components) {
      this.components = []
    }
    const index = this.components.length
    component.sort = index
    this.components.push(component)
  }

  // 子组件拖拽排序
  sortComponent(dragIndex: number, hoverIndex: number) {
    this.componentKey = -1
    if (!this.components) this.components = []
    this.components = update(this.components, {
      $splice: [
        [dragIndex, 1],
        [hoverIndex, 0, this.components[dragIndex]],
      ],
    })
    this.components?.map((v, k) => {
      v.sort = k
    })
  }

  // 选中子组件
  setComponent(k: number) {
    this.componentKey = k
  }

  async SyncPageRemoteForm(component?: Component) {
    await Promise.all([this.getComponentForm(component)])
  }

  // 选中子组件后获取子组件的配置
  async getComponentForm(component?: Component) {
    component = component && component.url ? component : this.component
    const {url, path}: any = component || {}
    if (!url) return
    let form = this.formCache[`${url}-${path}`] || []
    if (form.length === 0) {
      this.isGetForm = true
      let f_url = url.replace('/emp.js', '/astro/') + path.replace('./', '') + '.json'
      if (isDev && localDevEmpConfig?.empMap?.[f_url]) {
        f_url = localDevEmpConfig.empMap[f_url]
      }
      const d = await fetch(f_url)
        .then(d => d.json())
        .catch(e => {
          this.formCache[`${url}-${path}`] = []
        })
      form = d || []
      this.formCache[`${url}-${path}`] = form
    }
    if (!component.dataConfig && form.length > 0) {
      let dataConfig: any = {}
      const bizConfig: any = {}
      const serverConfig: any = {}
      for (const d of this.componentForm) {
        if (d.type === FormItemType.Biz) {
          Object.entries(d.props).forEach(([key, d]: any) => {
            bizConfig[key] = d.value
          })
        } else {
          Object.entries(d?.props || {}).forEach(([key, d]: any) => {
            dataConfig[key] = d.value
          })
        }
      }
      dataConfig = Object.assign(Object.assign(dataConfig, bizConfig), serverConfig)
      component.dataConfig = dataConfig
    } else if (component.dataConfig) {
      const temp_dataConfig = component.dataConfig
      form.forEach((item: any) => {
        Object.entries(item?.props ?? {}).forEach(([key, d]: any) => {
          if (temp_dataConfig[key] === undefined) {
            temp_dataConfig[key] = d.value
          }
        })
      })
      component.dataConfig = temp_dataConfig
    }
    this.isGetForm = false
  }

  // 更新组件字段，如slotKey插槽字段
  updateComponentItem(k: keyof Component, v: any, ck = this.componentKey) {
    if (ck > -1) {
      if (this.components && this.components.length > 0) {
        const component = this.components[ck]
        component[k] = v
      }
    }
  }

  updateComponent(component: Component, ck = this.componentKey) {
    const components = this.components as Component[]
    components[ck] = component
  }

  // 删除组件
  removeComponent(k: number) {
    this.components?.splice(k, 1)
    if (this.componentKey === k) {
      this.resetComponent()
    }
  }

  // 重置选中的子组件
  resetComponent() {
    this.componentKey = -1
  }

  // 设置是否显示子组件配置面板
  setIsOpenConfiure(bool: boolean) {
    this.isOpenConfiure = bool
  }

  // 右侧配置栏 ********************************************************************************************************
  get formDataConfig() {
    if (this.sformType === 'subComponent') {
      return this.component?.dataConfig
    }
    return {}
  }

  // 更新右侧配置面板
  async saveFormDataConfig(d: any, ck = this.componentKey, sformType = this.sformType) {
    if (sformType === 'subComponent') {
      const component = this.components?.[ck] ?? {}
      component.dataConfig = component.dataConfig ? component.dataConfig : {}
      for (const [k, v] of Object.entries(d) as any) {
        component.dataConfig[k] = v
      }
    }
  }
}

const subComponentStore = new SubComponentStore()
export default subComponentStore
