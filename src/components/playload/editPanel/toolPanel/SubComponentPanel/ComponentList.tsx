import {Flex} from '@astro/ui/ui'
import {DndProvider} from 'react-dnd'
import {HTML5Backend} from 'react-dnd-html5-backend'
import ComponentItem from './ComponentItem'
import styles from './ComponentList.module.scss'
import subComponentStore from './subComponentStore'

// 子组件列表
const ComponentList = () => {
  const subComponentState = subComponentStore.state

  const moveCard = (dragIndex: number, hoverIndex: number) => {
    subComponentState.sortComponent(dragIndex, hoverIndex)
  }

  // 确保在渲染 DndProvider 之前有组件数据
  if (!subComponentState.components?.length) {
    return null
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <Flex vertical gap={15} className={styles.container}>
        {subComponentState.components.map((component, index) => {
          return (
            <ComponentItem
              key={component?.uniqueNum}
              id={component?.uniqueNum}
              index={index}
              component={component}
              moveCard={moveCard}
              subComponentState={subComponentState}
            />
          )
        })}
      </Flex>
    </DndProvider>
  )
}

export default ComponentList
