.container {
  position: relative;
  border: 1px dashed gray;
  border-radius: 8px;
  padding: 5px 10px;
  min-height: 50px;
  cursor: move;

  .ribbon {
    position: absolute;
    top: -25px;
    right: -18px;
    cursor: pointer;
    font-size: 10px;
  }

  .btn {
    cursor: pointer;
  }
}

.name {
  width: 100%;
  height: 50px;
  @include text-overflow();
}

.popover {
  font-size: 12px;
}

.select {
  font-size: 12px;
  width: 90%;

  [class^="ant-select"],
  [class*=" ant-select"] {
    font-size: 12px;
  }
}

:global {
  .ant-badge {
    font-size: 12px;
  }
}