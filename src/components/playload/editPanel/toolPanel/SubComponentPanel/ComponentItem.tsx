import { DeleteOutlined, DragOutlined, EditOutlined, ExclamationOutlined, LinkOutlined } from '@astro/ui/icon'
import { Badge, Button, Flex, Popover, Select, Tooltip } from '@astro/ui/ui'
import classNames from 'classnames'
import type { Identifier, XYCoord } from 'dnd-core'
import { type FC, memo } from 'react'
import { useEffect, useRef, useState } from 'react'
import { useDrag, useDrop } from 'react-dnd'
import { Dot } from 'src/components/common/playload'
import pageStore, { type PageStore } from 'src/components/playload/editPanel/pageStore'
import type { Component } from 'src/components/playload/editPanel/types'
import { colors } from 'src/utils/colors'
import styles from './ComponentItem.module.scss'
import type { SubComponentStore } from './subComponentStore'

export const ItemTypes = {
  CARD: 'card',
}

export interface CardProps {
  id: any
  index: number
  component: Component
  moveCard: (dragIndex: number, hoverIndex: number) => void
  subComponentState: SubComponentStore
}

interface DragItem {
  index: number
  id: string
  type: string
}

// 子组件
const ComponentItem: FC<CardProps> = ({ id, component, index, moveCard, subComponentState }) => {
  const pageState = pageStore.state
  const ref = useRef<HTMLDivElement>(null)
  const [isHoverItem, setIsHoverItem] = useState(false)
  const [{ handlerId }, drop] = useDrop<DragItem, void, { handlerId: Identifier | null }>({
    accept: ItemTypes.CARD,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      }
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return
      }
      const dragIndex = item.index
      const hoverIndex = index

      // 替换位置不变，跳过
      if (dragIndex === hoverIndex) {
        return
      }

      //  确定在屏幕上的区域
      const hoverBoundingRect = ref.current?.getBoundingClientRect()

      // 获取垂直中间点
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2

      // 确定鼠标位置
      const clientOffset = monitor.getClientOffset()

      // 获取距顶部的像素
      const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top

      // 只有当鼠标越过一半项的高度时才执行移动操作
      // 当向下拖动时，只有在光标在50%以下时才移动
      // 当向上拖动时，只有在光标在50%以上时才移动

      // 向下拖动
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return
      }

      // 向上拖动
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return
      }

      moveCard(dragIndex, hoverIndex)
      // 替换位置
      item.index = hoverIndex
    },
  })

  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.CARD,
    item: () => {
      return { id, index }
    },
    collect: (monitor: any) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  const opacity = isDragging ? 0 : 1

  drag(drop(ref))

  // 选择子组件
  const selectItem = async () => {
    subComponentState.setComponent(index)
    await subComponentState.SyncPageRemoteForm()
    subComponentState.setIsOpenConfiure(true)
  }

  // 删除子组件
  const deleteItem = () => {
    subComponentState.removeComponent(index)
    setOpenDeleteComfirm(false)
  }

  // 选择组件的分区
  const associationItem = (value: string) => {
    subComponentState.setComponent(index)
    subComponentState.updateComponentItem('slotKey', value, index)
    setOpenAssociationPopover(false)
  }

  const [openDeleteComfirm, setOpenDeleteComfirm] = useState(false)
  const [openAssociationPopover, setOpenAssociationPopover] = useState(false)
  // const [selectedValue, setSelectedValue] = useState<string>(component.slotKey ?? '') // 设置组件选中的分区

  useEffect(() => {
    const hoverElement: any = ref.current ?? {}
    hoverElement.addEventListener('mouseenter', function () {
      setIsHoverItem(true)
    })

    hoverElement.addEventListener('mouseleave', function () {
      setIsHoverItem(false)
    })
  }, [])

  const getSlotRibbonColor = (slotKey: string): string => {
    const index = pageState.componentSlotOptions.findIndex(slot => slot.value === slotKey)
    if (index > colors.length - 1 || index <= 0) return colors[colors.length - 1]
    return colors[index]
  }
  const getSlotName = (slotKey: string) => {
    return pageState.componentSlotOptions?.find(item => item.value === slotKey)?.label || slotKey
  }

  return (
    <div ref={ref} title="可拖拽组件调整顺序">
      <Flex
        justify="space-between"
        align="center"
        className={styles.container}
        style={{
          opacity,
          background: index === subComponentState.componentKey ? 'rgb(207, 227, 242)' : 'none',
        }}
        data-handler-id={handlerId}
      >
        {/* 操作红点 */}
        <Flex align="center" onClick={selectItem} className={styles.name}>
          <Dot pageKey={pageStore.pageKey} componentKey={index} >
            <Flex vertical={true} gap={6} >
              {component?.name}
            </Flex>
          </Dot>
        </Flex>

        <Flex justify="flex-end">
          {/* 删除子组件 */}
          <Popover
            rootClassName={styles.popover}
            content={
              <Flex gap={10} justify="center">
                <Button
                  variant="solid"
                  onClick={e => {
                    setOpenDeleteComfirm(false)
                  }}
                >
                  取消
                </Button>
                <Button
                  color="danger"
                  variant="solid"
                  onClick={e => {
                    deleteItem()
                  }}
                >
                  删除
                </Button>
              </Flex>
            }
            title={<Flex justify="center">确认删除该子组件？</Flex>}
            trigger="click"
            open={openDeleteComfirm}
            onOpenChange={setOpenDeleteComfirm}
          >
            <span
              className={styles.btn}
              onClick={e => {
                setOpenDeleteComfirm(true)
              }}
            >
              <DeleteOutlined title="删除组件" />
            </span>
          </Popover>

          {/* 子组件关联插槽配置 */}
          <Popover
            rootClassName={classNames(styles.popover)}
            content={
              <Flex gap={10} justify="center">
                <Select
                  popupClassName={styles.select}
                  className={styles.select}
                  size="small"
                  options={subComponentState.clone(pageState.componentSlotOptions)}
                  value={component.slotKey}
                  onChange={associationItem}
                ></Select>
              </Flex>
            }
            title={<Flex justify="center">组件关联分区</Flex>}
            trigger="click"
            // trigger="hover"
            open={openAssociationPopover}
            onOpenChange={setOpenAssociationPopover}
          >
            <Badge.Ribbon
              className={styles.ribbon}
              placement={'end'}
              text={component.slotKey ? `${getSlotName(component.slotKey)}` : <Tooltip title="请选择插槽" color={'red'} key={'red'}><ExclamationOutlined /></Tooltip>}
              color={getSlotRibbonColor(component.slotKey || '')}
            ></Badge.Ribbon>
            <div
              onClick={() => {
                setOpenAssociationPopover(!associationItem)
              }}
            ></div>
          </Popover>
        </Flex>
      </Flex>
    </div>
  )
}

export default memo(ComponentItem)
