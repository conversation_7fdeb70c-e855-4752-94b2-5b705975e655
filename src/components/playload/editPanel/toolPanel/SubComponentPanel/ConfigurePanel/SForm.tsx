import {CaretRightOutlined} from '@astro/ui/icon'
import type {ProFormColumnsType} from '@astro/ui/pro'
import {Button, Collapse, type CollapseProps} from '@astro/ui/ui'
import userStore from 'src/components/common/user/userStore'
import {CollapseExtra, getFormItemConfig} from 'src/components/playload/editPanel/configurePanel/SForm'
import {FormItemType} from 'src/components/playload/editPanel/configurePanel/type'
import subComponentStore from '../subComponentStore'
import {SchemaForm, SchemaServerForm} from './SchemaForm'
import styles from './sForm.module.scss'
export const SubComponentForm = ({form}: any) => {
  return <SFConfig source={form} />
}

export const SFConfig = ({source}: any) => {
  const items: CollapseProps['items'] = []
  const userState = userStore.state
  const subComponentState = subComponentStore.state
  const dataConfig = subComponentStore.clone(subComponentState.formDataConfig || {})
  const onChange = (d: any) => {
    subComponentState.saveFormDataConfig(d)
  }

  source.forEach((d: any, k: number) => {
    const item = {
      key: `SubSF-${k}`,
      label: d.name,
      children:
        d.type === FormItemType.Server ? (
          <SchemaServerForm key={`SchemaFormItem-${k}`} />
        ) : (
          <SchemaForm
            key={`SchemaFormItem-${k}`}
            columns={setColumn(d)}
            onChange={onChange}
            disabled={false}
            dataConfig={dataConfig}
          />
        ),
      style: {
        background: '#fff',
      },
      //helpDocument是帮助文档的地址，如果layout没有这个就不会显示
      extra: <CollapseExtra k={k} type={d.type} helpDocument={d.helpDocument ? d.helpDocument : undefined} />,
    }
    // === 权限配置
    if (userState.user.role === 'P' && (d.type === FormItemType.UI || d.type === FormItemType.DevServer)) {
      // 产品 运营没权限查看 FormItemType.UI 或 FormItemType.DevServer
    } else {
      items.push(item)
    }
    // ===
  })
  return (
    <div className={styles.container}>
      {
        <Collapse
          expandIconPosition={'end'}
          items={items}
          bordered={false}
          expandIcon={({isActive}) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
          defaultActiveKey={`SubSF-0`}
        />
      }
    </div>
  )
}

export const setColumn = (sourceItem: any): ProFormColumnsType[] => {
  const c: ProFormColumnsType[] = []
  const {props} = sourceItem
  if (props) {
    for (const [k, v] of Object.entries(props) as any) {
      c.push(getFormItemConfig({v, k}))
    }
  }
  return c
}
