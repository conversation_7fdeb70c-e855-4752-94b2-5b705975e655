import { Drawer, message } from '@astro/ui/ui'
import React, { useState, useEffect, useCallback } from 'react'
import { NoSetting } from 'src/components/common/playload'
import { Loading } from 'src/components/common/status'
import VersionPanel from 'src/components/playload/editPanel/versionPanel'
import subComponentStore from '../subComponentStore'
import { SubComponentForm } from './SForm'

import './index.module.scss'
import { checkPathNameExist, getScopeInfo } from 'src/components/common/VersionSelect'
import { getPkgVersionByUrl, pkgNameParse } from 'src/utils'

const ConfigurePanel = () => {
  const subComponentState = subComponentStore.state

  const updateComponentVersion = useCallback(
    async (version: string, newScope: string) => {
      const currentSelectedComponent = subComponentState.component
      if (currentSelectedComponent) {
        const component = subComponentStore.clone(currentSelectedComponent)
        const oldVersion = getPkgVersionByUrl(component.url || '')
        component.scope = newScope
        component.url = component.url?.replace(oldVersion, version)

        subComponentStore.updateComponent(component)
        await subComponentStore.getComponentForm()
      }
    },
    [subComponentState.component],
  )

  const onVersionSelect = async (version: string) => {
    const tmpCmp = subComponentState.clone(subComponentState.component)
    console.log('onVersionSelect', version, tmpCmp)
    const pkgName = pkgNameParse(tmpCmp.url || '')
    const empData = await getScopeInfo(pkgName, version)
    if (checkPathNameExist(empData, tmpCmp.path)) {
      await updateComponentVersion(version, empData?.data?.id)
      // message.success(`已切换到版本:${version}`)
    } else {
      message.error(`${version}版本不存在${tmpCmp.path}的组件`)
    }
  }

  const Panel = () => {
    if (subComponentState.isGetForm) {
      return <Loading />
    } else if (subComponentState.componentKey > -1) {
      const form = subComponentState.clone(subComponentState.componentForm)
      return <SubComponentForm form={form} />
    } else {
      return <NoSetting />
    }
  }

  return (
    <Drawer
      title="子组件配置"
      onClose={() => {
        subComponentState.setIsOpenConfiure(false)
      }}
      open={subComponentState.isOpenConfiure}
    >
      <VersionPanel currentSelectedComponent={subComponentState.component} showSubmitButton={false} onVersionSelect={onVersionSelect} />
      {Panel()}
    </Drawer>
  )
}
export default ConfigurePanel
