import {
  DeleteOutlined,
  EditOutlined,
  LeftOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  RightOutlined,
} from '@astro/ui/icon'
import {ProFormCheckbox} from '@astro/ui/pro'
import {Button, Card, Checkbox, Collapse, Flex, Input, Space, Switch, Table, Tag, Tooltip, message} from '@astro/ui/ui'
import {useEffect, useState} from 'react'
import appStore from 'src/components/dashboard/AppStore'
import type {IEnvInfo, IUrlParams, UrlParams} from 'src/config/project'
import pageStore from '../pageStore'
import PlatformParams from './PlatformParams'
import ProjectParams from './ProjectParams'

const PageParams = (props: {
  type: 'page' | 'project'
  projectUrlParams?: IUrlParams
  updateProjectUrlParams?: any
}) => {
  const {type, projectUrlParams, updateProjectUrlParams} = props
  const pageState = pageStore.state
  const appListState = appStore.state
  const urlParams = pageState.projectInfo?.projectConfig?.urlParams

  const [showParamsPanel, setShowParamsPanel] = useState(false)
  const [paramsData, setParamsData] = useState<IUrlParams>(
    (type === 'page' ? urlParams?.[pageState.pageKey] : projectUrlParams) ?? {},
  )

  // 监听页面参数变更
  useEffect(() => {
    if (type === 'page') {
      setParamsData(urlParams?.[pageState?.pageKey] ?? {})
    }
  }, [urlParams?.[pageState.pageKey]])

  // 编辑中的参数值
  const [editingValue, setEditingValue] = useState<{
    key: string
    oldValue: string
    value: string
    label?: string
  } | null>(null)

  // 参数值表格配置
  const renderParamValueTable = (paramKey: string) => {
    const columns = [
      {
        title: '使用状态',
        dataIndex: 'selected',
        width: '20%',
        render: (_: any, record: any) => (
          <Checkbox
            checked={paramsData[paramKey]?.select?.[0] === record.value}
            onChange={e => {
              const temp = pageState.clone(paramsData)
              if (e.target.checked) {
                temp[paramKey].select = [record.value] // 修改为数组只包含当前值
              } else {
                temp[paramKey].select = [] // 取消选中时清空数组
              }
              setParamsData(temp)
            }}
          />
        ),
      },
      {
        title: '参数值',
        dataIndex: 'value',
        width: '25%',
        render: (_: any, record: any) => {
          const isEditing = editingValue?.key === paramKey && editingValue?.oldValue === record.value
          return isEditing ? (
            <Input
              value={editingValue.value}
              autoFocus
              placeholder="请输入参数值"
              onChange={(e: any) => {
                setEditingValue(prev => ({...prev!, value: e.target.value}))
              }}
            />
          ) : (
            <span>{record.value}</span>
          )
        },
      },
      {
        title: '备注',
        dataIndex: 'label',
        width: '35%',
        render: (_: any, record: any) => {
          const isEditing = editingValue?.key === paramKey && editingValue?.oldValue === record.value
          return isEditing ? (
            <Input
              value={editingValue.label}
              placeholder="请输入备注说明"
              onChange={(e: any) => {
                setEditingValue(prev => ({...prev!, label: e.target.value}))
              }}
            />
          ) : (
            <span>{record.label || '-'}</span>
          )
        },
      },
      {
        title: '操作',
        width: '20%',
        render: (_: any, record: any) => (
          <Space>
            {editingValue?.key === paramKey && editingValue?.oldValue === record.value ? (
              <>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    const temp = pageState.clone(paramsData)
                    const valueIndex = temp[paramKey].options.indexOf(record.value)
                    temp[paramKey].options[valueIndex] = editingValue.value

                    // 更新备注
                    if (!temp[paramKey].optionLabels) {
                      temp[paramKey].optionLabels = {}
                    }
                    temp[paramKey].optionLabels[editingValue.value] = editingValue.label

                    // 更新选中值
                    temp[paramKey].select = temp[paramKey].select.map((v: string) =>
                      v === record.value ? editingValue.value : v,
                    )
                    setParamsData(temp)
                    setEditingValue(null)
                  }}
                >
                  确认
                </Button>
                <Button size="small" onClick={() => setEditingValue(null)}>
                  取消
                </Button>
              </>
            ) : (
              <>
                <EditOutlined
                  onClick={() =>
                    setEditingValue({
                      key: paramKey,
                      oldValue: record.value, // 添加 oldValue
                      value: record.value,
                      label: record.label,
                    })
                  }
                />
                <DeleteOutlined
                  onClick={() => {
                    const temp = pageState.clone(paramsData)
                    temp[paramKey].options = temp[paramKey].options.filter((v: string) => v !== record.value)
                    temp[paramKey].select = temp[paramKey].select.filter((v: string) => v !== record.value)
                    setParamsData(temp)
                  }}
                />
              </>
            )}
          </Space>
        ),
      },
    ]

    const dataSource = (paramsData[paramKey]?.options || []).map((value: string) => ({
      value,
      key: value,
      label: paramsData[paramKey].optionLabels?.[value] || '',
      selected: paramsData[paramKey]?.select?.includes(value),
    }))

    return (
      <div>
        <Table
          columns={columns}
          dataSource={dataSource}
          pagination={false}
          size="small"
          onRow={() => ({
            onClick: (e: any) => e.stopPropagation(),
          })}
        />
        <Button
          type="dashed"
          block
          icon={<PlusOutlined />}
          disabled={editingValue !== null} // 添加disabled属性，当处于编辑状态时禁用
          onClick={() => {
            const temp = pageState.clone(paramsData)
            const newValue = '新参数值'
            temp[paramKey].options = [...temp[paramKey].options, newValue]

            // 初始化备注
            if (!temp[paramKey].optionLabels) {
              temp[paramKey].optionLabels = {}
            }
            temp[paramKey].optionLabels[newValue] = ''

            setParamsData(temp)
            setEditingValue({
              key: paramKey,
              oldValue: newValue,
              value: newValue,
              label: '',
            })
          }}
          style={{marginTop: 8}}
        >
          添加参数值
        </Button>
      </div>
    )
  }

  // 参数名配置
  const collapseItems = Object.entries(paramsData).map(([key, value]) => ({
    key,
    label: (
      <Flex align="center" gap={8} justify="space-between" style={{width: '100%'}}>
        <Flex align="center" gap={8}>
          <Checkbox
            checked={(value?.select ?? [])?.length > 0}
            onClick={e => e.stopPropagation()}
            onChange={e => {
              e.stopPropagation()
              const temp = pageState.clone(paramsData)
              if (e.target.checked) {
                temp[key].select = [temp[key].options[0]]
              } else {
                temp[key].select = []
              }
              setParamsData(temp)
            }}
          />
          <Tag
            color={(value?.select ?? [])?.length > 0 ? 'green' : ''}
            onClick={e => {
              e.stopPropagation()
              setEditingValue({
                key: key,
                oldValue: key,
                value: key,
              })
            }}
          >
            {editingValue?.key === key && editingValue?.oldValue === key ? (
              <Space>
                <Input
                  placeholder="请输入参数名"
                  value={editingValue.value}
                  style={{width: 120}}
                  onChange={(e: any) => {
                    e.stopPropagation()
                    setEditingValue(prev => ({...prev!, value: e.target.value}))
                  }}
                  onClick={(e: any) => e.stopPropagation()}
                />
                <Button
                  size="small"
                  type="primary"
                  onClick={e => {
                    e.stopPropagation()
                    const temp = pageState.clone(paramsData)

                    // 检查新参数名是否已存在
                    if (editingValue.value !== key && temp[editingValue.value]) {
                      message.error('参数名已存在，请修改后重试')
                      return
                    }

                    const oldData = temp[key]
                    delete temp[key]
                    temp[editingValue.value] = {
                      ...oldData,
                    }
                    setParamsData(temp)
                    setEditingValue(null)
                  }}
                >
                  确认
                </Button>
                <Button
                  size="small"
                  onClick={e => {
                    e.stopPropagation()
                    setEditingValue(null)
                  }}
                >
                  取消
                </Button>
              </Space>
            ) : (
              <span>{`${key} ${value?.select?.length ? '= ' + value.select.join(',') : ''}`}</span>
            )}
          </Tag>
        </Flex>

        {/* 操作区 */}
        <Flex gap={20}>
          {/* <EditOutlined title="编辑参数值" /> */}

          <DeleteOutlined
            title="删除参数名"
            onClick={e => {
              e.stopPropagation()
              const temp = pageState.clone(paramsData)
              delete temp[key]
              setParamsData(temp)
            }}
          />
        </Flex>
      </Flex>
    ),

    children: renderParamValueTable(key),
  }))

  const onAdd = () => {
    const temp = pageState.clone(paramsData)
    const newKey = `param_${Object.keys(temp).length + 1}`

    // 检查参数名是否已存在
    if (temp[newKey]) {
      message.error('参数名已存在，请修改后重试')
      return
    }

    temp[newKey] = {
      select: [],
      options: [],
      label: '',
    }
    setParamsData(temp)
    setEditingValue({
      key: newKey,
      oldValue: newKey,
      value: newKey,
      label: '',
    })
  }

  const onOk = async () => {
    if (type === 'project') {
      // 项目参数由项目表单统一移交
      updateProjectUrlParams(paramsData)
    } else {
      const projectInfo = pageState.updateUrlParams(paramsData)
      const updateOk = await appListState.doSaveProject(projectInfo, false)
      if (updateOk) {
        setShowParamsPanel(false)
      }
    }
  }

  return (
    <Flex vertical justify={'space-between'} style={{width: 430, minHeight: 300, height: '100%'}}>
      <Flex vertical gap={10} style={{flex: 1, overflow: 'auto'}}>
        {type === 'page' && <span>页面参数</span>}
        {Object.keys(paramsData).length === 0 ? (
          <Flex align="center" justify="center" style={{height: '260px'}}>
            暂无参数，请点击"增加参数"按钮添加
          </Flex>
        ) : (
          <Collapse items={collapseItems} />
        )}
      </Flex>

      <Flex gap={10} justify="flex-end">
        <Button onClick={onAdd}>增加参数</Button>
        <Button type="primary" onClick={onOk}>
          提交参数
        </Button>
      </Flex>
    </Flex>
  )
}

export default PageParams
