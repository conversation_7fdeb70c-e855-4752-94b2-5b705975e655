import {DeleteOutlined, EditOutlined, LeftOutlined, RightOutlined} from '@astro/ui/icon'
import {CellEditorTable, EditableProTable, ProTable} from '@astro/ui/pro'
import {Button, Flex, Input, Select, type SelectProps, Space, Table, Tag, message} from '@astro/ui/ui'
import type React from 'react'
import {useEffect, useMemo, useRef, useState} from 'react'
import appStore from 'src/components/dashboard/AppStore'
import type {IUrlParams, UrlParams} from 'src/config/project'
import pageStore from '../pageStore'
type TagRender = SelectProps['tagRender']

// 转换select options格式
const convertOptions = (arr: string[]): object[] => {
  return arr?.map(str => ({value: str}))
}

// select选中标签
const tagRender: TagRender = (props: any) => {
  const {label, value, closable, onClose} = props
  const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
    event.preventDefault()
    event.stopPropagation()
  }
  return (
    <Tag
      color={'cyan'}
      onMouseDown={onPreventMouseDown}
      closable={closable}
      onClose={onClose}
      style={{marginInlineEnd: 4}}
    >
      {label}
    </Tag>
  )
}

// 已废弃，使用新的paramsConfig组件进行参数选择交互
const PageParams = () => {
  const pageState = pageStore.state
  const urlParams = pageState.projectInfo?.projectConfig?.urlParams
  const [showParamsPanel, setShowParamsPanel] = useState(false)
  const [paramsData, setParamsData] = useState<IUrlParams>(urlParams?.[pageState.pageKey] ?? {})
  const appListState = appStore.state

  // 监听 urlParams 变化
  useEffect(() => {
    setParamsData(urlParams?.[pageState.pageKey] ?? {})
  }, [urlParams, pageState.pageKey])

  const [editingRow, setEditingRow] = useState<any>(null)

  const columns = [
    {
      title: '参数名',
      dataIndex: 'key',
      width: 100,
      align: 'center',
      render: (_: any, record: any) => {
        if (record.isNew) {
          return (
            <Input
              value={record.key}
              placeholder="请输入"
              onChange={(e: any) => {
                const newRow = {...record, key: e.target.value}
                setEditingRow(newRow)
              }}
            />
          )
        }
        return record.key
      },
    },

    // 修改参数值列的渲染逻辑
    {
      title: '参数值',
      dataIndex: 'value',
      align: 'center',
      width: 120,
      render: (_: any, record: any) => {
        return (
          <Select
            allowClear
            mode="tags"
            placeholder="请输入"
            notFoundContent={null}
            tagRender={tagRender}
            style={{width: 100}}
            value={record.isNew ? record.value : paramsData?.[record?.key]?.select}
            options={convertOptions(paramsData?.[record?.key]?.options ?? [])}
            onChange={(value: any) => {
              if (record.isNew) {
                const newRow = {...record, value}
                setEditingRow(newRow)
              } else {
                handleParamsChange(value, record)
              }
            }}
          />
        )
      },
    },
    {
      title: '备注',
      width: 100,
      dataIndex: 'label',
      align: 'center',
      render: (_: any, record: any) => {
        if (editingRow?.key === record.key) {
          return (
            <Input
              placeholder="请输入"
              defaultValue={record.label}
              onChange={(e: any) => {
                setEditingRow((prev: any) => ({...prev, label: e.target.value}))
              }}
            />
          )
        }
        return record.label
      },
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: 60,
      render: (_: any, record: any) => {
        if (editingRow?.key === record.key) {
          return (
            <Flex vertical align={'center'} gap={2}>
              <Button
                style={{width: '50px'}}
                size="small"
                type="primary"
                onClick={() => {
                  if (!editingRow.key || !editingRow.value?.length) {
                    message.error('参数名称和参数值必填')
                    return
                  }
                  if (paramsData[editingRow.key] && editingRow.isNew) {
                    message.error('参数名已存在')
                    return
                  }
                  const temp = pageState.clone(paramsData)
                  temp[editingRow.key] = {
                    select: editingRow.value,
                    options: editingRow.value,
                    label: editingRow.label || '无',
                  }
                  if (editingRow.isNew) {
                    setParamsData(temp)
                  } else {
                    delete temp[record.key]
                    setParamsData(temp)
                  }
                  setEditingRow(null)
                }}
              >
                确认
              </Button>
              <Button
                style={{width: '50px'}}
                size="small"
                onClick={() => {
                  setEditingRow(null)
                }}
              >
                取消
              </Button>
            </Flex>
          )
        }
        return (
          <Space>
            <DeleteOutlined onClick={() => handleDeleteRow(record.key)} />
          </Space>
        )
      },
    },
  ]

  // 获取参数表格
  const paramsTable = useMemo(() => {
    if (!paramsData) return []
    const temp: any = []
    Object.keys(paramsData)?.map(key => {
      temp.push({
        key,
        label: paramsData?.[key]?.label,
        value: paramsData?.[key]?.select,
      })
    })
    return temp
  }, [paramsData])

  // select下拉选择参数值
  const handleParamsChange = (value: any, record: any) => {
    const temp = pageState.clone(paramsData)
    if (!temp[record?.key]) return
    temp[record?.key].select = value
    temp[record?.key].options = Array.from(new Set([...temp[record.key].options, ...value])) // 更新下拉选项
    setParamsData(temp)
  }

  // 添加参数行
  const handleAddRow = () => {
    const newRow = {
      key: '',
      value: [],
      label: '',
      isNew: true,
    }
    setEditingRow(newRow)
  }

  // 删除参数行
  const handleDeleteRow = (rowKey: string) => {
    const temp = pageState.clone(paramsData)
    delete temp[rowKey]
    setParamsData(temp)
  }

  const onOk = async () => {
    const projectInfo = pageState.updateUrlParams(paramsData)
    const updateOk = await appListState.doSaveProject(projectInfo, false)
    if (updateOk) {
      setShowParamsPanel(false)
    }
  }

  return (
    <Space align="center">
      {showParamsPanel ? (
        <LeftOutlined
          title="页面参数配置"
          onClick={() => {
            setShowParamsPanel(!showParamsPanel)
          }}
        />
      ) : (
        <RightOutlined
          title="页面参数配置"
          onClick={() => {
            setShowParamsPanel(!showParamsPanel)
          }}
        />
      )}

      {showParamsPanel && (
        <Flex vertical={true} gap={10}>
          <Table
            style={{
              width: '400px',
              height: '220px',
              marginBottom: '20px',
            }}
            pagination={false}
            columns={columns}
            dataSource={editingRow ? [...paramsTable, editingRow] : paramsTable}
            rowKey={(record: any) => (record.isNew ? 'new' : record.key)}
            scroll={{y: 180}}
          />

          <Flex gap={10} justify={'flex-end'}>
            <Button onClick={handleAddRow}>增加参数</Button>
            <Button type="primary" onClick={onOk} title="点击确认保存参数配置">
              提交参数
            </Button>
          </Flex>
        </Flex>
      )}
    </Space>
  )
}
export default PageParams
