import {EditOutlined, EyeOutlined, HistoryOutlined, ToolOutlined} from '@astro/ui/icon'
import {Flex, List, Popover, type TabsProps} from '@astro/ui/ui'
import cx from 'classnames'
import {useEffect, useState} from 'react'
import {Dot} from 'src/components/common/playload'
import userStore from 'src/components/common/user/userStore'
import {PreviewContent} from 'src/components/playload/TemplateControl/PreviewContent'
import pageStore from 'src/components/playload/editPanel/pageStore'
import OnlineUsers from '../../UserSocket'
import f2cStore from '../f2cStore'
import AddComponent from './AddComponent'
import ComponentList from './ComponentList'
import DrawerComponent from './DrawerComponent'
import {F2CContent} from './F2CContent'
import styles from './List.module.scss'
import HandlePageModal from './PageModal'
import VersionManagementModal from './VersionManagementModal'
// import {getComponentInfo} from './VersionManager'

const PageList = (props: any) => {
  const {handleType, setHandleType} = props
  const store = pageStore.state
  const f2cState = f2cStore.state
  const userState = userStore.state
  const [pageList, setPageList] = useState<TabsProps['items']>([]) // 渲染的页面列表
  const [openF2CContent, setOpenF2CContent] = useState(false)
  const [isOpenVersionlist, setOpenVersionlist] = useState(false)
  const [isVersionManagementOpen, setIsVersionManagementOpen] = useState(false)

  // 将数据结构映射为组件结构
  useEffect(() => {
    pagesTransToList()
  }, [store.pages])

  const selectItem = async (index: number) => {
    store.setComponent(index)
    await store.getComponentForm()
  }

  const pagesTransToList = () => {
    const arr = []
    for (const key in store.pages) {
      const page = store.pages[key]
      const item = {
        key,
        value: key,
        label: page.name,
        sort: page.sort || 0,
        children: <ComponentList key={key} />,
      }
      arr.push(item)
    }
    // console.log('handleType', handleType)
    setPageList(
      arr.sort((a, b) => {
        return a.sort - b.sort
      }),
    )
    return arr
  }
  // const hasCom = useMemo(() => {
  //   if (
  //     store.pageKey &&
  //     (!store.page?.components || store.page?.components.length === 0) &&
  //     typeof store.page?.url === 'undefined'
  //   ) {
  //     return false
  //   }
  //   return true
  // }, [store.pageKey, store.page])

  const hideF2CContent = () => {
    setOpenF2CContent(false)
  }

  const handleOpenChange = (newOpen: boolean) => {
    setOpenF2CContent(newOpen)
  }
  const handleOpenVersionlist = () => {
    setOpenVersionlist(true)
  }
  const handleCancelVersionlist = () => {
    setOpenVersionlist(false)
  }

  const secondaryMenu = (item: any) => {
    return (
      <div style={{display: 'flex', justifyContent: 'flex-start', gap: '15px'}}>
        <DrawerComponent configType="page" key={'AddComponentModal'} />
        <ToolOutlined title={'组件版本'} key={'version'} onClick={handleOpenVersionlist} />
      </div>
    )
  }

  useEffect(() => {
    if (
      store.pageKey &&
      (!store.page?.components || store.page?.components.length === 0) &&
      typeof store.page?.url === 'undefined'
    ) {
      f2cStore.setHasCom(false)
      // return false
    } else {
      f2cStore.setHasCom(true)
    }
    // return true
  }, [store.pageKey, store.page])
  return (
    <>
      <List
        className={styles.pageConfiglist}
        // itemLayout="horizontal"
        itemLayout="vertical"
        dataSource={pageList}
        renderItem={(item: any) => (
          // <Badge.Ribbon
          //   className={styles.ribbon}
          //   color="#B0C4DE"
          //   placement={'end'}
          //   style={{fontSize: 10}}
          //   text={item.key}
          // >
          <List.Item
            onClick={() => {
              store.setPage(item.key)
              store.resetComponent()
              store.SyncPageRemoteForm()
              // alert(item.key)
            }}
            className={cx(styles.item, item.key === store.pageKey && styles.activeItem)}
            actions={
              item.key === store.pageKey
                ? [
                  // <span key={'pageTag'} className={styles.tag} title={item.key}>
                  //   {item.key}
                  // </span>,
                  <Popover key={'PreviewContent'} content={PreviewContent} trigger="click">
                    <EyeOutlined />
                  </Popover>,
                  <DrawerComponent configType="page" key={'AddComponentModal'} />,
                  <span key="list-loadmore-edit">
                    <EditOutlined
                      title="编辑页面"
                      onClick={() => {
                        store.setPage(item.key)
                        store.resetComponent()
                        setHandleType('edit')
                      }}
                    />
                  </span>,
                  <span key="version-management">
                    <HistoryOutlined
                      title="版本管理"
                      onClick={e => {
                        e.stopPropagation() // 阻止事件冒泡，避免触发List.Item的点击事件
                        setIsVersionManagementOpen(true)
                      }}
                    />
                  </span>,
                  <Popover
                    key={'F2CContent'}
                    content={<F2CContent close={hideF2CContent} />}
                    open={openF2CContent}
                    onOpenChange={handleOpenChange}
                    trigger="click"
                    placement="right"
                  >
                    <div title={f2cState.hasCom ? '使用F2C换肤' : '使用F2C创建页面'} className={styles.f2cIcon} />
                  </Popover>,
                  <AddComponent key={'page-config-copy'} configType={'page'} />,
                ]
                : []
            }
          >
            <Flex className={styles.title} align="center" justify="space-between" wrap="wrap" gap={6}>
              {item.key === store.pageKey && <span className={styles.titleTag}>{item.key}</span>}
              <Dot pageKey={item.key}>
                <div className={styles.label}>{item.label}</div>
              </Dot>
              <OnlineUsers key={'OnlineUsers'} pageKey={item.key} size={24} maxCount={5} />
            </Flex>
          </List.Item>
          // </Badge.Ribbon>
        )}
      />
      {/* <Modal title="组件版本信息" open={isOpenVersionlist} onCancel={handleCancelVersionlist} footer={null}>
        {getComponentInfo()}
      </Modal> */}
      {/* 版本管理模态框 */}
      <VersionManagementModal
        open={isVersionManagementOpen}
        onCancel={() => setIsVersionManagementOpen(false)}
        pageKey={store.pageKey}
      />
      {/* 编辑页面弹窗 */}
      {handleType !== 'none' && <HandlePageModal handleType={handleType} setHandleType={setHandleType} />}
    </>
  )
}
export default PageList
