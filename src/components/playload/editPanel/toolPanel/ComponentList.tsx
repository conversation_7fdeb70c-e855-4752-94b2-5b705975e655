import {Flex} from '@astro/ui/ui'
import {useEffect, useState} from 'react'
import {DndProvider} from 'react-dnd'
import {HTML5Backend} from 'react-dnd-html5-backend'
import pageStore from 'src/components/playload/editPanel/pageStore'
import ComponentItem from './ComponentItem'

const ComponentList = (props: any) => {
  const pageState = pageStore.state

  const moveCard = (dragIndex: number, hoverIndex: number) => {
    pageState.sortComponent(dragIndex, hoverIndex)
    // 设置小红点
  }

  // 确保在渲染 DndProvider 之前有组件数据
  if (!pageState.page.components?.length) {
    return null
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <Flex vertical={true} gap={15} style={{margin: '20px auto', padding: '0 10px'}}>
        {pageState.page.components?.map((component, index) => {
          return (
            <ComponentItem
              key={component?.uniqueNum}
              id={component?.uniqueNum}
              index={index}
              component={component}
              moveCard={moveCard}
              pageState={pageState}
            />
          )
        })}
      </Flex>
    </DndProvider>
  )
}
export default ComponentList
