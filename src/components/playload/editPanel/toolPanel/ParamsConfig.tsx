import {LeftOutlined, RightOutlined} from '@astro/ui/icon'
import {Flex, Space} from '@astro/ui/ui'
import {useState} from 'react'
import type {IUrlParams} from 'src/config/project'
import pageStore from '../pageStore'
import PageParams from './PageParams'
import PlatformParams from './PlatformParams'
import ProjectParams from './ProjectParams'

const ParamsConfig = (props: {
  type: 'page' | 'project'
  projectUrlParams?: IUrlParams
  updateProjectUrlParams?: any
}) => {
  const {type, projectUrlParams, updateProjectUrlParams} = props
  const pageState = pageStore.state
  const templateControl = pageState.templateControl
  const [showParamsPanel, setShowParamsPanel] = useState(false)

  return (
    <Space align="center">
      {type === 'page' ? (
        showParamsPanel ? (
          <LeftOutlined title="页面参数配置" onClick={() => setShowParamsPanel(!showParamsPanel)} />
        ) : (
          <RightOutlined title="页面参数配置" onClick={() => setShowParamsPanel(!showParamsPanel)} />
        )
      ) : null}

      {((type === 'page' && showParamsPanel) || type === 'project') && (
        <Flex vertical gap={20}>
          {type === 'page' && (
            <Flex gap={40}>
              {/* 平台参数 */}
              {templateControl?.hasEnv && <PlatformParams />}

              {/* 项目参数 */}
              {pageState.projectInfo?.projectConfig?.projectUrlParams && <ProjectParams />}
            </Flex>
          )}

          {/* 页面参数 */}
          <PageParams type={type} projectUrlParams={projectUrlParams} updateProjectUrlParams={updateProjectUrlParams} />
        </Flex>
      )}
    </Space>
  )
}

export default ParamsConfig
