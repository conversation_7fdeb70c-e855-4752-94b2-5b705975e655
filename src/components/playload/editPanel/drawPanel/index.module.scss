.container {
  width: 375px;
  height: 100%;
  margin: auto;
}

.dark {
  background-color: #333;
  background-image: linear-gradient(rgba(255, 255, 255, .2) 1px, transparent 0), linear-gradient(90deg, rgba(255, 255, 255, .2) 1px, transparent 0);
  background-position: 0 0, 0 0, 0 0, 0 0;
  background-repeat: repeat, repeat, repeat, repeat;
  background-size: 15px 15px, 15px 15px, 75px 75px, 75px 75px;
  background-attachment: scroll, scroll, scroll, scroll;
  background-origin: padding-box, padding-box, padding-box, padding-box;
  background-clip: border-box, border-box, border-box, border-box;

  .darkTips {
    position: absolute;
    top: 200px;
    left: 10%;
    color: #3333;
    font-size: 12px;
    z-index: 10;
    transform: translateX(-50%);
  }
}

.inner {
  width: 800px;
}

.pc {
  width: 95%;
}

.admin {
  background-color: #f1f2f4;
}