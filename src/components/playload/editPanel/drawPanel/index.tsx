import {InsertRowAboveOutlined, InsertRowRightOutlined, MoonOutlined} from '@astro/ui/icon'
import {FloatButton} from '@astro/ui/ui'
import {useCallback, useEffect, useMemo, useRef, useState} from 'react'
import {Loading, Tips, Warn} from 'src/components/common/status'
import pageStore from 'src/components/playload/editPanel/pageStore'
import config from 'src/config'
import {mapEmpToLocal} from 'src/utils/dev'
import drawStore from './drawStore'
import styles from './index.module.scss'
const {isDev, localDevEmpConfig} = config

/**
 * DrawPanel 组件
 *
 * 负责渲染页面预览区域，包含以下功能：
 * 1. 通过 iframe 加载预览页面
 * 2. 向 iframe 发送页面数据
 * 3. 根据不同状态显示不同的提示组件
 * 4. 处理 iframe 加载和错误事件
 */
const DrawPanel = () => {
  // iframe DOM 引用
  const iframeRef = useRef<HTMLIFrameElement>(null)
  // 页面状态和绘制状态
  const pageState = pageStore.state
  const drawState = drawStore.state
  // iframe 的 key 值，用于强制重新加载
  const [iframeKey, setIframeKey] = useState(0)
  // iframe 是否准备好显示，用于控制淡入效果
  const [iframeReady, setIframeReady] = useState(false)

  /**
   * 缓存页面数据
   *
   * 将页面数据和项目信息组合成消息格式
   * 开发环境下处理 emp 映射
   */
  const pageData = useMemo(() => {
    let page = pageState.clone({componentKey: pageState.componentKey, ...pageState.page})
    if (isDev && localDevEmpConfig?.empMap) {
      page = mapEmpToLocal(page, localDevEmpConfig.empMap)
    }
    return {
      type: 'pageData',
      page,
      project: pageState.clone(pageState.projectInfo),
    }
  }, [pageState.componentKey, pageState.page, pageState.projectInfo])
  /**
   * 获取页面参数 - 优化为纯函数
   *
   * 从项目配置中提取当前页面的 URL 参数
   * 将参数格式化为 URL 查询字符串格式
   */
  const getPageParams = useMemo(() => {
    const urlParams = pageState?.projectInfo?.projectConfig?.urlParams ?? {}
    const projectUrlParams = pageState?.projectInfo?.projectConfig?.projectUrlParams ?? {}
    let pageParamsInfo: any = urlParams?.[pageState?.pageKey] ?? {}
    pageParamsInfo = {...projectUrlParams, ...pageParamsInfo}

    const envInfo = pageState.templateControl
    if (envInfo?.hasEnv && envInfo?.curEnvKey) {
      const info = {[envInfo?.curEnvKey]: {select: [envInfo.curEnvValue]}}
      pageParamsInfo = {...info, ...pageParamsInfo}
    }
    return Object.keys(pageParamsInfo).reduce(
      (params, key) => params + `&${key}=${pageParamsInfo?.[key]?.select?.join(',')}`,
      '',
    )
  }, [
    pageState?.pageKey,
    pageState?.projectInfo?.projectConfig?.urlParams,
    pageState.projectInfo?.projectConfig?.projectUrlParams,
    pageState.templateControl,
  ])

  /**
   * URL 变化时重载 iframe
   *
   * 1. 重置 iframe 准备状态
   * 2. 设置加载状态
   * 3. 延迟更新 iframeKey 触发重新加载
   */
  useEffect(() => {
    if (!drawState.url) return

    setIframeReady(false)
    drawState.setLoading(true)

    const timer = setTimeout(() => setIframeKey(prev => prev + 1), 300)
    return () => clearTimeout(timer)
  }, [drawState.url])

  /**
   * 更新预览配置
   *
   * 当关键参数变化时，更新预览配置：
   * - platform: 预览平台（PC/移动端）
   * - pageKey: 页面标识
   * - pageParams: 页面参数
   * - renderType: 渲染模式
   */
  useEffect(() => {
    if (!(pageState.platform && pageState.pageKey && pageState.projectInfo.renderType)) return

    drawState.setup({
      platform: pageState.platform,
      pageKey: pageState.pageKey,
      pageParams: getPageParams,
      renderType: pageState.projectInfo.renderType,
    })
  }, [pageState.platform, pageState.pageKey, pageState.projectInfo.renderType, getPageParams])

  /**
   * iframe 加载完成处理
   *
   * 1. 延迟关闭 loading 状态，确保 iframe 内容已完全渲染
   * 2. 使用 requestAnimationFrame 在下一帧发送数据
   * 3. 延迟设置 iframe 准备好状态，实现平滑过渡
   */
  const handleIframeLoad = useCallback(() => {
    const _window = iframeRef.current?.contentWindow
    if (!_window) return

    setTimeout(() => {
      drawState.setLoading(false)

      requestAnimationFrame(() => {
        _window.postMessage(pageData, '*')
        setTimeout(() => setIframeReady(true), 50)
      })
    }, 100)
  }, [pageData])

  /**
   * 向 iframe 发送数据
   *
   * 使用 requestAnimationFrame 确保在下一帧发送数据
   * 避免在渲染过程中阻塞主线程
   */
  const sendDataToIframe = useCallback(() => {
    const _window = iframeRef.current?.contentWindow
    if (!_window) return
    requestAnimationFrame(() => _window.postMessage(pageData, '*'))
  }, [pageData])

  /**
   * 监听 pageData 变化，同步数据到 iframe
   *
   * 只在 iframe 已准备好且不在加载状态时更新
   * 使用防抖避免频繁发送消息
   */
  useEffect(() => {
    if (!iframeReady || drawState.loading) return

    window.addEventListener(
      'message',
      async (e: any) => {
        // console.log('handleMessage收到来自子窗口的消息：', e.data)
        if (e.data?.type === 'ViewModeComp') {
          const sort = e.data?.sort
          if (sort > -1) {
            pageState.setComponent(sort)
            await pageStore.getComponentForm()
            await pageStore.getComponentSlotForm(pageStore.component)
          }
        }
      },
      false,
    )
    const timer = setTimeout(sendDataToIframe, 50)
    return () => {
      clearTimeout(timer)
      window.removeEventListener('message', () => {})
    }
  }, [pageData, drawState.loading, iframeReady, sendDataToIframe])

  /**
   * 根据状态返回对应的提示组件
   *
   * 1. 加载中显示 Loading
   * 2. 未选择页面显示提示
   * 3. 页面无组件显示警告
   */
  const renderContent = useMemo(() => {
    if (drawState.loading) return <Loading />

    const isProjectLoaded = !pageState.isGetProjectPagesActionLoading

    if (!pageState.pageKey && isProjectLoaded) return <Tips title={'请选择左侧页面进行相应操作!'} />

    if (
      pageState.pageKey &&
      isProjectLoaded &&
      (!pageState.page?.components || !pageState.page?.components.length) &&
      typeof pageState.page?.url === 'undefined'
    ) {
      return <Warn title={'请点击左上角(+)添加配置!'} />
    }

    return null
  }, [drawState.loading, pageState.pageKey, pageState.page, pageState.isGetProjectPagesActionLoading])

  /**
   * iframe 加载错误处理
   *
   * 1. 重置加载状态
   * 2. 重置 iframe 准备状态
   */
  const handleIframeError = useCallback(() => {
    drawState.setLoading(false)
    setIframeReady(false)
  }, [])

  /**
   * 是否显示 iframe
   *
   * 只有在没有提示内容且有 URL 时才显示 iframe
   * 使用 Boolean 确保返回布尔值
   */
  const showIframe = Boolean(!renderContent && drawState.url)

  /**
   * 动态样式
   *
   * 根据 iframe 显示状态和准备状态计算样式
   * 使用 opacity 和 transition 实现平滑过渡效果
   */
  const iframeStyle = useMemo(
    () => ({
      display: showIframe ? 'block' : 'none',
      opacity: showIframe && iframeReady ? 1 : 0,
      transition: 'opacity 0.5s ease-in-out',
    }),
    [showIframe, iframeReady],
  )

  /**
   * 容器类名
   *
   * 根据平台类型和项目标签计算容器类名
   * 支持 PC/移动端切换和管理员模式
   */
  const containerClassName = useMemo(
    () =>
      `${styles.container} ${styles[pageState.platform]} ${pageState.projectInfo.tags?.includes(4) ? styles.admin : ''} ${pageState.page?.isDark ? styles.dark : ''}`,
    [pageState.platform, pageState.projectInfo.tags, pageState.page?.isDark],
  )

  return (
    <div className={containerClassName}>
      {/* 根据状态显示提示内容 */}
      {renderContent && <div className={styles.contentWrapper}>{renderContent}</div>}

      {/* {pageState.page?.isDark && <div className={styles.darkTips}>已开启深色模式，仅在平台预览生效</div>} */}
      {pageState.page?.isDark && (
        <FloatButton
          tooltip="已开启深色模式，仅在平台预览生效"
          shape="circle"
          style={{insetInlineEnd: 400}}
          icon={<MoonOutlined />}
        />
      )}

      {/* iframe 预览区域 */}
      <iframe
        key={iframeKey}
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        ref={iframeRef}
        title="preview"
        src={drawState.url}
        width="100%"
        height="100%"
        style={iframeStyle}
      />
    </div>
  )
}

export default DrawPanel
