import config from 'src/config'
import {BaseStore} from 'src/store'
import {appendSearchParamToUrl} from 'src/utils'
export class DrawStore extends BaseStore {
  loading = false
  setLoading(b: boolean) {
    this.loading = b
  }
  pageKey = ''
  platform = ''
  pageParams = ''
  url = ''
  setup({platform, pageKey, pageParams, renderType}: any) {
    if (renderType) config.renderType = renderType
    const url = `${config.render}?device=${platform}&page=${pageKey}${pageParams}`
    this.url = getIframeUrl(url)
    if (this.pageKey === pageKey && this.platform === platform) return
    this.pageKey = pageKey
    this.platform = platform
    this.loading = true
  }
}
export default new DrawStore()

const getIframeUrl = (url: string) => (appendSearchParamToUrl(url, location.search) || null) as any
