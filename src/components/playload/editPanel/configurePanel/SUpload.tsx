import {type ProFormColumnsType, ProFormUploadButton} from '@astro/ui/pro'
import UploadRequest, {type IUploadData} from 'src/utils/uploadRequest'
import styles from './index.module.scss'
type sUploadSetupType = {
  d: ProFormColumnsType
  k: string
  pk?: string
  v: any
  dataConfig: any
  onChange: any
}
export const sUploadSetup = ({d, k, v, pk, dataConfig, onChange}: sUploadSetupType) => {
  d.transform = (v, namePath, allValues) => {
    let cb = {[k]: ''}
    if (typeof v === 'string') {
      cb = {[k]: v}
    }
    return cb
  }
  d.renderFormItem = (schema, config: any, form) => {
    return (
      <SUpload
        {...{
          configId: config.id,
          pk,
          k,
          v,
          dataConfig,
          onChange,
        }}
      />
    )
  }
}
type SUploadType = {
  pk?: string //parent key
  k: string // current key
  v: any // form item config
  dataConfig: any // form dataConfig
  onChange: any // return dataConfig change with k or pk
  configId?: string // get pk index with config id
}
export const SUpload = ({configId, pk, k, v, dataConfig, onChange}: SUploadType) => {
  let fileList: any = []
  let gIndex = undefined
  if (configId && pk) {
    gIndex = configId.replace(`${pk}_`, '').replace(`_${k}`, '')
    gIndex = Number(gIndex) || 0
  }
  const url =
    pk && gIndex !== undefined && dataConfig[pk] && dataConfig[pk][gIndex] ? dataConfig[pk][gIndex][k] : dataConfig[k]
  fileList = url
    ? [
        {
          uid: k,
          name: k,
          url,
        },
      ]
    : []
  //
  const onRemove = (f: any) => {
    // console.log('BetaSchemaForm remove', config.id, gIndex, pk)
    if (!pk || gIndex === undefined) {
      onChange({[k]: ''})
      return true
    }
    dataConfig[pk][gIndex][k] = ''

    // pageState.saveFormDataConfig({[pk]: dataConfig[pk]})
    onChange({[pk]: dataConfig[pk]})
    return false
  }
  //
  const uploadRequest = async ({onProgress, onSuccess, onError, file}: any) => {
    try {
      const res: IUploadData = await UploadRequest(file, {}, {onProgress})
      console.log(
        'BetaSchemaForm data',
        res,
        'onChange',
        onChange,
        pk,
        k,
        gIndex,
        dataConfig,
        !pk || gIndex === undefined,
      )
      if (!pk || gIndex === undefined) {
        // pageState.saveFormDataConfig({[k]: res.data.url})
        onChange({[k]: res.data.url})
      } else {
        console.log('BetaSchemaForm upload uploadRequest', dataConfig, gIndex)
        dataConfig[pk] = dataConfig[pk] ? dataConfig[pk] : []
        dataConfig[pk][gIndex] = dataConfig[pk][gIndex] ? dataConfig[pk][gIndex] : {}
        dataConfig[pk][gIndex][k] = res.data.url

        // pageState.saveFormDataConfig({[pk]: dataConfig[pk]})
        onChange({[pk]: dataConfig[pk]})
      }
      onSuccess(res.data.url, file)
    } catch (e) {
      onError(e)
    }
    return true
  }
  //
  return (
    <ProFormUploadButton
      title={v.label}
      fileList={fileList}
      fieldProps={{
        rootClassName: styles.fUpload,
        customRequest: uploadRequest,
        onRemove,
        maxCount: 1,
        // 更新上传样式
        listType: 'picture-card',
      }}
    />
  )
}
