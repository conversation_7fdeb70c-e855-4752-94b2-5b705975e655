import {BetaSchemaForm, type ProFormColumnsType} from '@astro/ui/pro'
import {Button, message} from '@astro/ui/ui'
import {useCallback, useEffect, useRef, useState} from 'react'
import pageStore from 'src/components/playload/editPanel/pageStore'
import {setColumn} from './SForm'
export type SchemaFormType = {
  columns: ProFormColumnsType[]
  disabled: boolean
  formInfo?: any
  onChange: any
  dataConfig: any
}
export const SchemaForm = ({columns, disabled, onChange, dataConfig}: SchemaFormType) => {
  const formRef = useRef<any>()
  useEffect(() => {
    formRef?.current?.setFieldsValue(dataConfig)
  }, [dataConfig])

  return (
    <BetaSchemaForm
      formRef={formRef}
      // initialValues={dataConfig}
      onValuesChange={(f: any, d: any) => {
        const cb = formRef.current?.getFieldsFormatValue?.()
        onChange(cb)
      }}
      // layout={'horizontal'}
      grid={true}
      disabled={disabled}
      // rowProps={{
      //   gutter: [16, 0],
      // }}
      autoFocusFirstInput={false}
      submitter={false}
      columns={columns}
      variant="filled"
      // size="small"
    />
  )
}

/**
 * 服务器表单
 * @param param0
 * @returns
 */
export const SchemaServerForm = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [isLoading, setIsLoading] = useState(false)
  const formRef = useRef<any>()
  const dataConfigRef = useRef<any>({})
  const formInfoRef = useRef<any>({})
  const [formColumns, setFormColumns] = useState<ProFormColumnsType[]>([])

  const getFormDataByServer = useCallback(async () => {
    setIsLoading(true)
    const res = await pageStore.getServerFormData()
    const dataOrigin = res?.data?.props

    if (dataOrigin) {
      Object.keys(dataOrigin).forEach(key => {
        dataConfigRef.current[key] = dataOrigin[key].value
      })
      formInfoRef.current = res?.data
      const columns: ProFormColumnsType[] = setColumn(formInfoRef.current)
      setFormColumns(columns)
      formRef?.current?.setFieldsValue(dataConfigRef.current)
      setIsLoading(false)
    } else {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    getFormDataByServer()
  }, [])

  return (
    <>
      {contextHolder}
      <BetaSchemaForm
        formRef={formRef}
        // initialValues={pageState.clone(pageState.formDataConfig)}
        onValuesChange={(f: any, d: any) => {
          // const cb = formRef.current?.getFieldsFormatValue?.()
          // onChange(cb)
        }}
        // layout={'horizontal'}
        grid={true}
        disabled={pageStore.submitProjectConfig}
        // rowProps={{
        //   gutter: [16, 0],
        // }}
        autoFocusFirstInput={false}
        submitter={{
          render: ({form, onSubmit}) => {
            return [
              <Button
                type="primary"
                key="submit"
                onClick={async (values: any) => {
                  ;(form as any)?.submit?.()
                }}
              >
                提交
              </Button>,
              <Button key="refresh" onClick={getFormDataByServer} loading={isLoading}>
                重置
              </Button>,
            ]
          },
        }}
        onFinish={async (values: Record<string, any>) => {
          const submitInfo = getServerFormFormatDataByFormData(formInfoRef.current, values)
          const res = await pageStore.setServerFormData(submitInfo)
          // console.log('onFinish submitInfo:', submitInfo, 'values:', values, 'res:', res)
          if (res?.result === 200) {
            messageApi.success('保存成功')
          } else {
            messageApi.error(res?.reason || '保存失败')
          }
        }}
        columns={formColumns}
        variant="filled"
      />
    </>
  )
}

/**
 * 生产服务所需的数据格式，从form vaule时。
 * @param originServerData
 * @param values
 * @returns
 */
const getServerFormFormatDataByFormData = (originServerData: Record<string, any>, values: Record<string, any>) => {
  const submitInfo = originServerData
  Object.keys(submitInfo.props).forEach(k => {
    if (Array.isArray(values?.[k])) {
      submitInfo.props[k].value = []
      values[k].forEach((dataItem, dataIndex) => {
        submitInfo.props[k].value.push(dataItem)
      })
    } else {
      submitInfo.props[k].value = values?.[k]
    }
  })
  return submitInfo
}
