import {NoSetting} from 'src/components/common/playload'
import {Loading} from 'src/components/common/status'
import pageStore from 'src/components/playload/editPanel/pageStore'
import {ComponentForm, PageForm} from './SForm'

const ConfigurePanel = () => {
  const pageState = pageStore.state
  if (pageState.isGetForm) {
    return <Loading />
  } else if (pageState.sformType === 'component' && pageState.componentKey > -1) {
    const form = pageState.clone(pageState.componentForm)
    return <ComponentForm form={form} />
  } else if (pageState.sformType === 'page' && pageState.pageForm.length > 0) {
    const form = pageState.clone(pageState.pageForm)
    return <PageForm form={form} />
  } else {
    return <NoSetting />
  }
}
export default ConfigurePanel
