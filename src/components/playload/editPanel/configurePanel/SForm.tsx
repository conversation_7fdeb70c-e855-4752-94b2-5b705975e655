import {CaretRightOutlined, ConsoleSqlOutlined, InfoCircleOutlined, SkinOutlined, UserOutlined} from '@astro/ui/icon'
import type {ProFormColumnsType} from '@astro/ui/pro'
import {Button, Collapse, type CollapseProps, Flex, Tooltip, message} from '@astro/ui/ui'
import {memo, useEffect, useState} from 'react'
import AdminTableEditor from 'src/components/common/AdminTableEditor'
import AstroAdmin from 'src/components/common/AstroAdmin'
import CodeEditor from 'src/components/common/CodeEditor'
import RichEditor from 'src/components/common/Editor'
import F2CConfigIdContent from 'src/components/common/F2CConfigIdContent'
import FrontEndAi from 'src/components/common/FrontEndAi'
import SformUpload from 'src/components/common/Upload'
import userStore from 'src/components/common/user/userStore'
import pageStore from 'src/components/playload/editPanel/pageStore'
import {isValidUrl, wordToHex} from 'src/utils'
import {SchemaForm, SchemaServerForm} from './SchemaForm'
import styles from './index.module.scss'
import {FormItemType} from './type'
export const ComponentForm = ({form}: any) => {
  const [showAllConfig, setShowAllConfig] = useState(false)
  const userState = userStore.state

  return (
    <Flex vertical>
      <SFConfig source={form} showAllConfig={showAllConfig} />

      {/* 给产品提供展示全部配置的功能 */}
      {userState.user.role === 'P' && (
        <Button
          style={{width: '100px', margin: '20px auto 0'}}
          size={'small'}
          onClick={() => {
            if (!showAllConfig) {
              setShowAllConfig(true)
            } else {
              message.info('已展示全部配置')
            }
          }}
        >
          展示全部配置
        </Button>
      )}
    </Flex>
  )
}
export const PageForm = ({form}: any) => {
  return <SFConfig source={form} />
}

export const SFConfig = ({source, showAllConfig}: any) => {
  const items: CollapseProps['items'] = []
  const userState = userStore.state
  const pageState = pageStore.state
  const dataConfig = pageStore.clone(pageState.formDataConfig || {})
  const onChange = (d: any) => {
    pageState.saveFormDataConfig(d)
  }
  const CollapseOnChange = (key: string | string[]) => {
    // console.log(key)
  }
  source.forEach((d: any, k: number) => {
    const item = {
      key: `SF-${k}`,
      label: d.name,
      children:
        d.type === FormItemType.Server ? (
          <SchemaServerForm key={`SchemaFormItem-${k}`} />
        ) : (
          <SchemaForm
            key={`SchemaFormItem-${k}`}
            columns={setColumn(d)}
            onChange={onChange}
            disabled={pageState.submitProjectConfig}
            dataConfig={dataConfig}
          />
        ),
      style: {
        background: '#fff',
        //  borderRadius: '8px'
      },
      //helpDocument是帮助文档的地址，如果layout没有这个就不会显示
      extra: <CollapseExtra k={k} type={d.type} helpDocument={d.helpDocument ? d.helpDocument : undefined} />,
    }
    // === 权限配置
    if (showAllConfig) {
      // 设置了查看全部配置
      items.push(item)
    } else if (userState.user.role === 'P' && (d.type === FormItemType.UI || d.type === FormItemType.DevServer)) {
      // 产品 运营没权限查看 FormItemType.UI 或 FormItemType.DevServer
    } else {
      items.push(item)
    }
    // ===
  })
  return (
    <div className={styles.container}>
      {
        <Collapse
          expandIconPosition={'end'}
          items={items}
          bordered={false}
          expandIcon={({isActive}) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
          defaultActiveKey={`SF-0`}
          onChange={CollapseOnChange}
        />
      }
    </div>
  )
}
export const CollapseExtra = memo(({k, type, helpDocument}: any) => {
  const [config, setConfig] = useState<any>({title: '', type, icon: undefined})

  useEffect(() => {
    switch (type) {
      case FormItemType.UI:
        setConfig({title: 'F2C配置', type, icon: SkinOutlined})
        break
      case FormItemType.Server:
        setConfig({title: '服务端配置', type, icon: ConsoleSqlOutlined})
        break
      case FormItemType.Biz:
        setConfig({title: '业务配置', type, icon: UserOutlined})
        break
      case FormItemType.DevServer:
        setConfig({title: '研发服务端配置', type, icon: ConsoleSqlOutlined})
        break
      case FormItemType.LayoutSlotConfig:
        setConfig({title: '动态插槽配置', type, icon: SkinOutlined})
        break
    }
  }, [type])
  return (
    <>
      {config?.icon ? (
        <>
          {helpDocument && isValidUrl(helpDocument) ? (
            <Tooltip key={`SFHelpDocument-${k}`} title={'帮助文档'}>
              <InfoCircleOutlined
                onClick={() => {
                  window.open(helpDocument as string, '_blank')
                }}
                style={{marginRight: '8px'}}
              />
            </Tooltip>
          ) : (
            <></>
          )}

          <Tooltip key={`SFToolTip-${k}`} title={config.title}>
            <config.icon
              key={`SFClick-${k}`}
              onClick={(e: any) => {
                e.stopPropagation()
              }}
            />
          </Tooltip>
        </>
      ) : (
        <></>
      )}
    </>
  )
})

export const setColumn = (sourceItem: any): ProFormColumnsType[] => {
  const c: ProFormColumnsType[] = []
  const {props} = sourceItem
  if (props) {
    for (const [k, v] of Object.entries(props) as any) {
      c.push(getFormItemConfig({v, k}))
    }
  }
  return c
}

export type GetFormItemConfigOpt = {
  v: any
  k: string
}

export function getFormItemConfig({v, k}: GetFormItemConfigOpt): ProFormColumnsType {
  const d: ProFormColumnsType = {
    dataIndex: k,
    title: v.label,
  }
  switch (v.type) {
    case 'AstroTypes.Input':
      d.valueType = 'text'
      break
    case 'AstroTypes.InputNumber':
      d.valueType = 'digit'
      break
    case 'AstroTypes.Switch':
      d.valueType = 'switch'
      break
    case 'AstroTypes.InputArea':
      d.valueType = 'textarea'
      break
    case 'AstroTypes.Dropdown':
      d.valueType = 'select'
      d.fieldProps = {
        options: v.options,
      }
      break
    case 'AstroTypes.TimePicker':
      d.valueType = 'dateTime'
      break
    case 'AstroTypes.Upload':
      {
        d.title = ''
        d.transform = (v, namePath, allValues) => {
          let cb = {[k]: ''}
          if (typeof v === 'string') {
            cb = {[k]: v}
          }
          return cb
        }
        d.renderFormItem = (schema, config, form: any) => {
          return <SformUpload {...config} label={v.label} />
        }
      }
      break
    case 'AstroTypes.ColorPicker':
      d.valueType = 'color'
      d.fieldProps = {
        size: 'default',
        rootClassName: styles.fColor,
      }
      d.convertValue = color => {
        const curColor = wordToHex(color) || color
        return curColor
      }
      d.transform = color => {
        return {
          [k]: color.toHexString ? color.toHexString() : color,
        }
      }
      break
    case 'AstroTypes.Checkbox':
      d.valueType = 'checkbox'
      d.fieldProps = {
        options: v.options,
      }
      break
    case 'AstroTypes.Group': {
      d.valueType = 'formList'
      d.columns = []
      const gp: any = {
        valueType: 'group',
        columns: [],
      }
      for (const [ko, vo] of Object.entries(v.props) as any) {
        const cc: any = getFormItemConfig({v: vo, k: ko})
        gp.columns.push(cc)
      }
      d.columns = [gp]
      break
    }
    case 'AstroTypes.RadioGroup': {
      d.valueType = 'select'
      const valueEnum: any = {}
      for (const [ko, vo] of Object.entries(v.options) as any) {
        valueEnum[vo.value] = {text: vo.label}
      }
      d.valueEnum = valueEnum
      break
    }
    case 'AstroTypes.MultipleRadioGroup': {
      d.valueType = 'select'
      const valueEnum: any = {}
      for (const [ko, vo] of Object.entries(v.options) as any) {
        valueEnum[vo.value] = {text: vo.label}
      }
      d.valueEnum = valueEnum
      d.fieldProps = {mode: 'multiple'}
      break
    }
    case 'AstroTypes.HtmlF2CId':
      {
        // d.title = ''
        d.valueType = 'code'
        d.renderFormItem = (schema, config, form: any) => {
          return <F2CConfigIdContent {...config} />
        }
      }
      break
    case 'AstroTypes.RichEditor':
      {
        // d.title = ''
        d.renderFormItem = (schema, config, form: any) => {
          return <RichEditor {...config} />
        }
      }
      break
    case 'AstroTypes.Json': {
      // d.title = ''
      d.renderFormItem = (schema, config, form: any) => {
        return <CodeEditor {...config} />
      }
      break
    }
    case 'AstroTypes.Code': {
      // d.title = ''
      d.renderFormItem = (schema, config, form: any) => {
        return <CodeEditor {...config} />
      }
      break
    }
    case 'AstroTypes.Scss': {
      d.renderFormItem = (schema, config, form: any) => {
        return <CodeEditor {...config} codeType={['scss']} />
      }
      break
    }
    case 'AstroTypes.AdminTable':
      d.renderFormItem = (schema, config, form: any) => {
        return <AdminTableEditor {...config} />
      }
      break
    case 'AstroTypes.AstroTable':
      d.renderFormItem = (schema, config, form: any) => {
        return <AstroAdmin {...config} tableMode={'Table'} />
      }
      break
    case 'AstroTypes.AstroForm':
      d.renderFormItem = (schema, config, form: any) => {
        return <AstroAdmin {...config} tableMode={'Form'} />
      }
      break
    case 'AstroTypes.AstroChart':
      d.renderFormItem = (schema, config, form: any) => {
        return <AstroAdmin {...config} tableMode={'Chart'} />
      }
      break
    case 'AstroTypes.FrontEndAi':
      {
        // d.title = ''
        d.valueType = 'code'
        d.renderFormItem = (schema, config, form: any) => {
          return <FrontEndAi {...config} />
        }
      }
      break
  }
  return d
}
