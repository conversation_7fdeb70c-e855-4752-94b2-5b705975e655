import {CaretRightOutlined} from '@astro/ui/icon'
import {ExportOutlined, SettingOutlined} from '@astro/ui/icon'
import {Collapse, Tooltip} from '@astro/ui/ui'
import {message} from '@astro/ui/ui'
import VersionSelect from 'src/components/common/VersionSelect'
import {appendLinkParam, copy} from 'src/utils'
import type pageStore from '../pageStore'

const CollapseExtra = () => {
  return (
    <Tooltip key={`SFToolTip-Com-Version`} title={'组件版本配置'}>
      <SettingOutlined />
    </Tooltip>
  )
}

const ExportComponent = (props: {title: string}) => {
  const {title} = props
  return (
    <Tooltip key={`SFToolTip-Com-Version`} title={title}>
      <ExportOutlined />
    </Tooltip>
  )
}

const VersionPanel = ({
  currentSelectedComponent,
  showSubmitButton,
  onVersionSelect,
}: {
  currentSelectedComponent: typeof pageStore.currentSelectedComponent
  showSubmitButton: boolean
  onVersionSelect?: (version: string) => void
}) => {
  const items = [
    currentSelectedComponent
      ? {
          key: 'component_version_select',
          label: '版本管理',
          children: (
            <VersionSelect
              currentSelectedComponent={currentSelectedComponent}
              showSubmitButton={showSubmitButton}
              onVersionSelect={onVersionSelect}
            />
          ),
          extra: (
            <>
              <span
                style={{marginRight: '10px'}}
                onClick={() => {
                  copy(JSON.stringify(currentSelectedComponent))
                  message.success('已复制到剪贴版')
                }}
              >
                <ExportComponent title={currentSelectedComponent?.pageKey ? `导出模板配置` : `导出组件`} />
              </span>
              <CollapseExtra />
            </>
          ),
        }
      : null,
  ]
  return (
    <>
      {currentSelectedComponent?.url ? (
        <Collapse
          style={{textAlign: 'left', background: '#FFF', borderBottom: '1px solid #d9d9d9', borderRadius: '0px'}}
          expandIconPosition={'end'}
          items={items}
          bordered={false}
          expandIcon={({isActive}) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
          defaultActiveKey={`component_version_0`}
        />
      ) : (
        <></>
      )}
    </>
  )
}

export default VersionPanel
