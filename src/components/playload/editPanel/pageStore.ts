import { message } from '@astro/ui/ui'
import { isEqual } from '@astro/utils/toolkit'

import update from 'immutability-helper'
import pageCentral from 'src/components/playload/UserSocket/pageCentral'
import config from 'src/config'
import type { EnvValue, IUrlParams, ProjectType } from 'src/config/project'
import {
  getDataConfig,
  getProjectConfig,
  getVersionList,
  lockVersion,
  publish,
  saveDataConfig,
  saveProjectConfig,
} from 'src/services/api'
import { BaseStore } from 'src/store'
import { deepAssign } from 'src/utils'
import { pagesBus } from 'src/utils/eventBus'
import { subscribe } from 'valtio'
import { subscribeKey } from 'valtio/utils'
import type { ProjectVersionInfo } from '../VersionModal/type'
import { type Component, FormItemType, type Page, type Pages, type PlatformType, type SformType } from './types'
const { isDev, localDevEmpConfig } = config
// 配置数据，包含当前项目配置的所有信息
export class PageStore extends BaseStore {
  projectId = 0 // 当前编辑的项目
  pages: Pages = {} // 当前项目的所有页面信息 - 编辑页使用
  get page() {
    return this.pageKey ? this.pages[this.pageKey] : {}
  }
  get component() {
    if (this.pageKey && this.componentKey > -1 && this.pages[this.pageKey]) {
      const page: Page = this.pages[this.pageKey]
      if (page.components && page.components.length > 0) {
        return page.components[this.componentKey]
      }
    }
    return {}
  }
  pageKey = ''
  componentKey = -1
  projectInfo: Partial<ProjectType> = {}
  platform = localStorage.getItem('platform') || 'h5' // 当前预览的是哪端 h5, inner, web
  constructor() {
    super()
    this.formCache &&
      subscribe(this.formCache, () => {
        // console.log(`##this.formCache`, this.formCache)
        localStorage.setItem('formCache', JSON.stringify(this.formCache))
      })
    subscribeKey(this, 'platform', () => {
      // console.log(this.platform)
      localStorage.setItem('platform', this.platform)
    })
  }
  //
  sformType?: SformType
  setPlatform(p: PlatformType) {
    this.platform = p
  }
  setSformType(d: SformType) {
    this.sformType = d
  }
  //
  setPage(k: string) {
    this.sformType = 'page'
    if (k !== this.pageKey) {
      // this.componentKey = -1
      this.resetComponent()
    }
    // 数据同步过来的时候 不做页面切换
    if (pageCentral.sync === false) this.pageKey = k
    // this.page = this.pages[k]
  }
  addPage(k: string, v: Page) {
    this.pages[k] = v
    this.setPage(k)
    //
    pageCentral.addPage(k, v)
  }
  /**
   * 编辑页面
   * @param k 页面key
   * @param v 页面内容
   * @param oldKey 旧的页面key
   * @param extend 是否继承原有页面内容
   */
  async editPage(k: string, v: Page, oldKey?: string, extend = false) {
    let page: Page
    // 如果不继承,直接覆盖页面内容
    if (!extend) {
      // this.pages[k] = v
      page = v
    } else {
      // 如果继承,合并原有内容和新内容
      // this.pages[k] = oldKey ? {...this.pages[oldKey], ...v} : {...this.pages[k], ...v}
      page = oldKey ? { ...this.pages[oldKey], ...v } : { ...this.pages[k], ...v }
      // console.log('DrawerComponent editPage', this.clone(this.pages[k]))
    }
    await this.SyncPageRemoteForm(page) //  遇到新的版本改变需要先增加缓存 formCache 再赋值响应 page
    this.pages[k] = page
    // 如果存在旧key且与新key不同,删除旧页面
    if (oldKey && oldKey !== k) {
      delete this.pages[oldKey]
    }
    // 设置当前页面
    this.setPage(k)
    // 更新组件分区key
    this.updateComponentsSlotKey()
    // 同步到其他用户
    pageCentral.editPage(k, v, oldKey, extend)
  }
  getPageIndex(pages = this.pages) {
    let key
    let sort = Number.POSITIVE_INFINITY
    Object.entries(pages).forEach(([k, v]) => {
      if (v.sort && v.sort < sort) {
        sort = v.sort
        key = k
      }
    })
    return key
  }
  deletePage(k: string) {
    if (this.pageKey === k) {
      this.resetPage()
      this.resetComponent()
    }
    delete this.pages[k]
    const index = this.getPageIndex()
    if (index) this.setPage(index)
    pageCentral.deletePage(k)
  }
  syncPages(p: Pages) {
    // this.pages = deepAssign(this.pages, this.clone(p))
    // this.pages = {...this.pages, ...this.clone(p)}
    //
    if (this.pageKey && !p[this.pageKey]) {
      this.setDefaultPageIndex(p)
    }
    // 接口保存数据与socket暂存数据不一致，高亮保存按钮
    if (!isEqual(this.pages, p)) {
      this._markChangedPages(p)
    }
    this.pages = this.clone(p)
    // console.log('DrawerComponent syncPages', this.clone(this.pages[this.pageKey]))
  }
  forceSync(roomPages: { pages: Pages; createTime: number }) {
    this.syncPages(roomPages.pages)
    //
    pageCentral.forceSync(roomPages)
  }
  setDefaultPageIndex(pages = this.pages) {
    const index = this.getPageIndex(pages)
    if (index) {
      this.setPage(index)
      // this.SyncPageRemoteForm()
    }
  }
  addComponent(component: Component, pk = this.pageKey) {
    if (!this.pages[pk].components) {
      this.pages[pk].components = []
    }
    const index = this.pages[pk].components.length
    component.sort = index
    this.pages[pk].components.push(component)
    // 如何非当前用户操作,则不更新组件分区key
    if (!pageCentral.sync) this.setComponent(index)
    //
    pageCentral.addComponent(component, pk)
  }
  resetComponent() {
    this.componentKey = -1
  }
  resetPage() {
    this.pageKey = ''
  }
  reset() {
    this.resetPage()
    this.resetComponent()
    // this.formOp = {}
    this.removeFormOp()
  }
  updateComponentItem(k: keyof Component, v: any, pk = this.pageKey, ck = this.componentKey) {
    if (pk && ck > -1 && this.pages[pk]) {
      const page: Page = this.pages[pk]
      if (page.components && page.components.length > 0) {
        const component = page.components[ck]
        component[k] = v
        //
        pageCentral.updateComponentItem(k as any, v, pk, ck)
      }
    }
  }
  updateComponents(components: Component[], pk = this.pageKey) {
    this.pages[pk].components = components
    //
    pageCentral.updateComponents(this.clone(this.pages[pk].components), pk)
  }
  updateComponent(component: Component, pk = this.pageKey, ck = this.componentKey) {
    const components = this.pages[pk].components as Component[]
    components[ck] = component
    pageCentral.updateComponent(this.clone(components[ck]), pk, ck)
  }
  setComponent(k: number) {
    this.componentKey = k
    this.sformType = 'component'
  }
  removeComponent(k: number, pk = this.pageKey) {
    this.pages[pk].components?.splice(k, 1)
    if (this.componentKey === k) {
      this.resetComponent()
    }
    //
    pageCentral.removeComponent(k, pk)
  }
  /**
   * sortComponent 组件排序
   * @param dragIndex
   * @param hoverIndex
   */
  sortComponent(dragIndex: number, hoverIndex: number, pk = this.pageKey) {
    this.componentKey = -1
    if (!this.pages[pk].components) this.pages[pk].components = []
    this.pages[pk].components = update(this.pages[pk].components, {
      $splice: [
        [dragIndex, 1],
        [hoverIndex, 0, this.pages[pk].components[dragIndex]],
      ],
    })
    this.pages[pk].components.map((v, k) => {
      v.sort = k
    })
    //
    pageCentral.updateComponents(this.clone(this.pages[pk].components), pk)
  }
  /**
   *
   * @param projectId 项目id
   * @param trigger 推送到其他用户
   */
  isGetProjectPagesActionLoading = false
  async getProjectPagesAction(projectId: string | number, redirect = true, trigger = false) {
    this.isGetProjectPagesActionLoading = true
    if (redirect) {
      this.reset()
    }
    await this.queryProjectDetail({ projectId })
    this.isGetProjectPagesActionLoading = false
    if (redirect) {
      const index = this.getPageIndex()
      if (index) {
        this.setPage(index)
        this.SyncPageRemoteForm()
      }
    }
    if (trigger) {
      this.removeFormOp()
      const roomPages = {
        pages: this.pages,
        createTime: this.projectInfo.createTime,
      }
      pageCentral.forceSync(this.clone(roomPages))
    }
  }
  async queryProjectDetail({ projectId }: any) {
    this.projectId = projectId
    const { data: res } = await getProjectConfig({
      projectId: this.projectId,
    })
    if (res && res?.result == 200) {
      this.pages = res?.data?.config?.pages
      this.projectInfo = res?.data?.project
      this.replaceEnvInfo(this.projectInfo?.projectConfig?.envInfo)
      this.checkCurTemplate()
      if (!this.projectInfo.renderType) this.projectInfo.renderType = config.renderType
      // console.log('this.projectInfo', this.clone(this.projectInfo), config.renderType)
    }
    // console.log(`('@@@@@@---queryProjectDetail:`, JSON.stringify(this.pages['222222']))

    return this.pages
  }
  updateProjectInfo(d: any) {
    this.projectInfo = deepAssign(this.projectInfo, d, true)
    this.checkCurTemplate()
    pageCentral.updateProjectInfo(d)
  }
  public submitProjectConfig = false
  async saveProjectConfig() {
    this.submitProjectConfig = true
    try {
      const data = await saveProjectConfig({
        projectId: this.projectId,
        config: { pages: this.clone(this.pages) },
      })
      this.submitProjectConfig = false
      this.removeFormOp()
      //
      pagesBus.publish('history', { act: 'saveProjectConfig', op: [this.projectId] })
      //
      return data
    } catch (e) {
      this.submitProjectConfig = false
      return {
        err: e,
      }
    }
  }
  async publish(env: string, publishRemark?: string) {
    try {
      const data = await publish({
        projectId: this.projectId,
        env,
        publishRemark,
      })
      // console.log(`publish:`, data)
      pagesBus.publish('history', { act: 'publish', op: [this.projectId, env, publishRemark] })
      return data
    } catch (e) {
      return {
        err: e,
      }
    }
  }
  isGetForm = false
  formCache: any = JSON.parse(localStorage.getItem('formCache') || `{}`)
  get componentForm() {
    const { url, path }: any = this.component || {}
    if (url && path) return this.formCache[`${url}-${path}`] ? this.formCache[`${url}-${path}`] : []
    return []
  }
  get pageForm() {
    const { url, path }: any = this.page || {}
    if (url && path) return this.formCache[`${url}-${path}`] ? this.formCache[`${url}-${path}`] : []
    return []
  }
  // 判断form 中是否有 dataConfig 并返回 对应字段
  // async updateServerFormConfig(form: any, url: string, path: string) {
  //   const serverFormConfig = form.find((item: any) => item.type === FormItemType.Server)
  //   if (serverFormConfig) {
  //     const serverConfig: any = {}
  //     const formServerConfig = await this.getServerFormData()
  //     if (formServerConfig?.data?.props) {
  //       serverFormConfig.props = formServerConfig.data.props
  //     }
  //     if (formServerConfig?.data?.props) {
  //       Object.entries(formServerConfig.data.props).forEach(([key, d]: any) => {
  //         serverConfig[key] = d.value
  //       })
  //     }
  //     this.component.dataConfig = Object.assign(this.component.dataConfig || {}, serverConfig)
  //     this.formCache[`${url}-${path}`] = form
  //   }
  // }
  /**
   * 获取组件表单信息 并设置到 dataConfig
   */
  async getComponentForm(component?: Component, resetDataConfig = false) {
    component = component && component.url ? component : this.component
    const { url, path }: any = component || {}
    if (!url) return
    let form = this.formCache[`${url}-${path}`] || []
    if (form.length === 0) {
      this.isGetForm = true
      let f_url = url.replace('/emp.js', '/astro/') + path.replace('./', '') + '.json'
      if (isDev && localDevEmpConfig?.empMap?.[f_url]) {
        f_url = localDevEmpConfig.empMap[f_url]
      }
      const d = await fetch(f_url)
        .then(d => d.json())
        .catch(e => {
          this.formCache[`${url}-${path}`] = []
        })
      form = d || []
      this.formCache[`${url}-${path}`] = form
    }
    if (!component.dataConfig && form.length > 0) {
      let dataConfig: any = {}
      const bizConfig: any = {}
      const serverConfig: any = {}
      for (const d of this.componentForm) {
        if (d.type === FormItemType.Biz) {
          Object.entries(d.props).forEach(([key, d]: any) => {
            bizConfig[key] = d.value
          })
        } else {
          Object.entries(d?.props || {}).forEach(([key, d]: any) => {
            dataConfig[key] = d.value
          })
        }
      }
      dataConfig = Object.assign(Object.assign(dataConfig, bizConfig), serverConfig)
      component.dataConfig = dataConfig
    } else if (component.dataConfig) {
      const temp_dataConfig = component.dataConfig
      // console.log(`[version] temp_dataConfig #1`, temp_dataConfig)
      form.forEach((item: any) => {
        Object.entries(item?.props ?? {}).forEach(([key, d]: any) => {
          // if (!temp_dataConfig[key]) {
          //   temp_dataConfig[key] = d.value
          // }
          if (resetDataConfig || temp_dataConfig[key] === undefined) {
            temp_dataConfig[key] = d.value
          }
        })
      })
      component.dataConfig = temp_dataConfig
      // console.log(`[version] temp_dataConfig #2`, temp_dataConfig)
    }
    // 更新服务器表单
    // this.updateServerFormConfig(form, url, path)
    this.isGetForm = false
  }
  ////////////////////////////////////////
  /**
   * 获取组件表单信息 并设置到 dataConfig
   */
  async getPageForm(page?: Page) {
    page = page && page.url ? page : this.page
    const { url, path }: any = page
    if (!url) return
    let form = this.formCache[`${url}-${path}`] || []
    if (form.length === 0 && path) {
      this.isGetForm = true
      let f_url = url.replace('/emp.js', '/astro/') + (path ?? '').replace('./', '') + '.json'
      if (isDev && localDevEmpConfig?.empMap?.[f_url]) {
        f_url = localDevEmpConfig.empMap[f_url]
      }
      const d = await fetch(f_url)
        .then(d => d.json())
        .catch(e => {
          this.formCache[`${url}-${path}`] = []
        })
      this.isGetForm = false
      form = d || []
      this.formCache[`${url}-${path}`] = form
    }
    if (!page.dataConfig && form.length > 0) {
      let dataConfig: any = {}
      const bizConfig: any = {}
      const pageForm = this.formCache[`${url}-${path}`] ? this.formCache[`${url}-${path}`] : []
      pageForm.map((d: any) => {
        if (d.type === FormItemType.Biz) {
          Object.entries(d.props).forEach(([key, d]: any) => {
            bizConfig[key] = d.value
          })
        } else {
          Object.entries(d.props).forEach(([key, d]: any) => {
            dataConfig[key] = d.value
          })
        }
      })
      dataConfig = Object.assign(dataConfig, bizConfig)
      page.dataConfig = dataConfig
    } else if (page.dataConfig) {
      const temp_dataConfig = page.dataConfig
      form.forEach((item: any) => {
        Object.entries(item?.props ?? {}).forEach(([key, d]: any) => {
          // if (!temp_dataConfig[key]) {
          //   temp_dataConfig[key] = d.value
          // }
          if (temp_dataConfig[key] === undefined) {
            temp_dataConfig[key] = d.value
          }
        })
      })
      page.dataConfig = temp_dataConfig
    }
  }
  /**
   * 获取组件表单信息
   */
  async getForm(com: any) {
    const { url, path }: any = com || {}
    if (!url) return
    let form = this.formCache[`${url}-${path}`] || []

    if (form.length === 0) {
      this.isGetForm = true
      let f_url = url.replace('/emp.js', '/astro/') + path.replace('./', '') + '.json'
      if (isDev && localDevEmpConfig?.empMap?.[f_url]) {
        f_url = localDevEmpConfig.empMap[f_url]
      }
      const d = await fetch(f_url)
        .then(d => d.json())
        .catch(e => {
          this.formCache[`${url}-${path}`] = []
        })
      this.isGetForm = false
      form = d || []
      this.formCache[`${url}-${path}`] = form

    }
    // console.log(`###6[version] form`, this.formCache[`${url}-${path}`])
    const formConfig = this.formCache[`${url}-${path}`]
    const formObj: any = {}
    if (formConfig) {
      formConfig.map((d: any) => {
        if (d) {
          const collapseName = d?.name || ''
          Object.entries(d.props || {}).forEach(([key, d]: any) => {
            let value: any
            if (d.type === 'AstroTypes.Group') {
              value = {}
              const groupProps = d.props
              Object.entries(groupProps).forEach(([key, item]: any, id: number) => {
                // const itemV: any = {}
                // Object.entries(item ?? {}).forEach(([itemKey, itemD]: any) => {
                //   itemV[itemKey] = {type: itemD?.type || 'underfined', value: itemD, name: itemD?.label || 'underfined', collapseName}
                // })
                // value[id] = itemV
                value[key] = { type: item?.type || 'underfined', value: item, name: item?.label || 'underfined', collapseName }
              })
            } else {
              value = d.value
            }
            formObj[key] = { type: d.type, value, name: d.label, collapseName }
          })
        }
      })
    }
    // console.log('###formObj', formObj)
    return formObj
  }
  get formDataConfig() {
    // console.log('saveFormDataConfig this.sformType', this.sformType)
    if (this.sformType === 'component') {
      return this.component?.dataConfig
    } else if (this.sformType === 'page') {
      return this.page.dataConfig
    }
    return {}
  }
  get currentSelectedComponent() {
    const pageInfo = this.page
    if (pageInfo.components && this.componentKey >= 0) {
      return this.clone(pageInfo.components[this.componentKey])
    } else if (this.pages?.[this.pageKey]) {
      return this.clone(this.pages[this.pageKey])
    } else {
      return null
    }
  }
  async saveFormDataConfig(d: any, pk = this.pageKey, ck = this.componentKey, sformType = this.sformType) {
    const page: Page = this.pages[pk]
    if (sformType === 'component' && page && page.components) {
      const component = page.components[ck]
      component.dataConfig = component.dataConfig ? component.dataConfig : {}
      for (const [k, v] of Object.entries(d) as any) {
        component.dataConfig[k] = v
      }
      // console.log('saveFormData component', d, component.dataConfig, pk, ck)
    } else if (sformType === 'page') {
      page.dataConfig = page.dataConfig ? page.dataConfig : {}
      for (const [k, v] of Object.entries(d) as any) {
        page.dataConfig[k] = v
      }
      // console.log('saveFormData page', d, page.dataConfig, pk, ck)
    }
    //
    pageCentral.saveFormDataConfig(d, pk, ck, sformType as any)
    // await this.saveProjectConfig()
  }
  async saveFormItem(k: string, v: any) {
    if (this.sformType === 'component' && this.component.dataConfig) {
      this.component.dataConfig[k] = v
    } else if (this.sformType === 'page' && this.page.dataConfig) {
      this.page.dataConfig[k] = v
    }
  }
  // 保存ext
  async saveFormItemExt(k: 'configIdF2C' | 'configIdVersionF2C' | 'modeF2C', v: any) {
    let ext = {}
    if (this.sformType === 'component') {
      if (!this.component.ext) {
        this.component.ext = {}
      }
      this.component.ext[k] = v
      ext = this.component.ext
    } else if (this.sformType === 'page' && this.page.ext) {
      this.page.ext[k] = v
      ext = this.page.ext
    }
    pageCentral.updateComponentItem('ext' as any, ext, this.pageKey, this.componentKey)
  }
  // 操作记录
  formOp: { [k: string]: number[] } = {}
  checkFormOp(pageKey: string, componentKey?: number) {
    if (typeof componentKey === 'undefined') {
      return !!this.formOp[pageKey]
    }
    return (this.formOp[pageKey] || []).includes(componentKey)
  }
  setFormOp(pageKey: string, componentKey?: number) {
    if (!this.formOp[pageKey]) {
      this.formOp[pageKey] = []
    }
    if (typeof componentKey !== 'undefined' && !this.formOp[pageKey].includes(componentKey)) {
      this.formOp[pageKey].push(componentKey)
    }
  }
  removeFormOp() {
    this.formOp = {}
  }
  get isFormOp() {
    return Object.keys(this.formOp || {}).length > 0
  }
  //====================================================================
  // 分区相关
  get slots() {
    const { url, path }: any = this.page || {}
    if (url && path) return this.formCache[`${url}-${path}-slot`] ? this.formCache[`${url}-${path}-slot`] : {}
    return {}
  }
  get slotOptions() {
    const { url, path }: any = this.page || {}
    const slotOptions: any[] = [{ value: '', label: '无关联' }]
    if (this.page?.dataConfig?.layoutSlotConfig && this.page?.dataConfig?.layoutSlotConfig?.length > 0) {
      this.page.dataConfig.layoutSlotConfig.forEach((item: Record<string, string>) => {
        // console.log(`slotOptions add`, item)
        if (item?.slotName) {
          slotOptions.push({
            value: item.slotName,
            label: `${item.slotDescription} (${item.slotName})`,
          })
        }
      })
    }
    if (url) {
      const form = this.formCache[`${url}-${path}-slot`] || {}
      Object.keys(form).map((k: any) => {
        slotOptions.push({
          value: k,
          label: `${form[k].label} (${k})`,
        })
      })
    }
    // console.log(`slotOptions`, slotOptions)
    return slotOptions
  }

  // 更新组件列表的slotKey
  updateComponentsSlotKey() {
    const slotsArr = this.slotOptions.map(item => item.value)

    // 更新模板后，不存在之前分区slotKey的置空
    this.page?.components?.map(item => {
      if (!slotsArr.includes(item.slotKey)) {
        item.slotKey = ''
      }
    })
  }

  async getSlotForm(page?: Page) {
    // console.log('getSlotForm', page)
    page = page && page.url ? page : this.page
    const { url, path }: any = page
    if (!url) return
    let form = this.formCache[`${url}-${path}-slot`] || {}
    if (Object.keys(form).length === 0 && path) {
      let f_url = url.replace('/emp.js', '/astro/') + (path ?? '').replace('./', '') + '.slot.json'
      if (isDev && localDevEmpConfig?.empMap?.[f_url]) {
        f_url = localDevEmpConfig.empMap[f_url]
      }
      const d = await fetch(f_url)
        .then(d => d.json())
        .catch(e => {
          this.formCache[`${url}-${path}-slot`] = {}
        })
      form = d || {}
      this.formCache[`${url}-${path}-slot`] = form
    }
  }
  async SyncPageRemoteForm(page: Page = {}) {
    await Promise.all([this.getPageForm(page), this.getSlotForm(page)])
  }
  // ============================================================================

  // serverform 相关 ===============================================================
  async getServerFormData() {
    try {
      const res: any = await getDataConfig({
        projectId: this.projectId,
        page: this.pageKey,
        materialId: Number(this.component.id || 0),
        uniqueNum: this.component.uniqueNum || '',
        url: this.component?.url || '',
        path: this.component?.path || '',
      })
      if (res?.data?.result === 200) {
        return res.data
      } else {
        return null
      }
    } catch (e) {
      console.warn(`get ServerFormData err`, e)
      return null
    }
  }

  async setServerFormData(config: Record<string, any>) {
    try {
      const res: any = await saveDataConfig({
        projectId: this.projectId,
        page: this.pageKey,
        materialId: Number(this.component.id || 0),
        uniqueNum: this.component.uniqueNum || '',
        config,
        url: this.component?.url ?? '',
        path: this.component?.path ?? '',
      })
      if (res?.data?.result === 200) {
        message.success('提交成功')
        return res.data
      } else {
        message.error(res?.data?.reason || '提交失败')
        return null
      }
    } catch (e) {
      console.warn(`set ServerFormData err`, e)
      return null
    }
  }

  // 项目版本相关 ============================================================================
  versionList: ProjectVersionInfo[] = [] // 版本列表

  curLockVersion = 0 // 当前版本，0为默认最新版本，其余数字为锁定版本

  setCurLockVersion(curLockVersion: number) {
    this.curLockVersion = curLockVersion
  }

  // 获取项目版本列表
  async queryVersionList() {
    try {
      const { data: res } = await getVersionList({
        projectId: this.projectId,
      })
      if (res && res.result === 200) {
        this.versionList = res.data
        this.setCurLockVersion(0)
        res.data.map((item: ProjectVersionInfo) => {
          if (item.lock === true) {
            this.setCurLockVersion(item.versionId)
          }
        })
      }
    } catch (e) {
      return {
        err: e,
      }
    }
  }

  // 更新项目对外锁定版本
  async lockProjectVersion(versionId: number) {
    try {
      const data = await lockVersion({
        projectId: this.projectId,
        versionId,
      })
      this.setCurLockVersion(versionId)
      return data
    } catch (e) {
      return {
        err: e,
      }
    }
  }

  // 更新页面参数
  updateUrlParams(paramsData: IUrlParams) {
    this.projectInfo.projectConfig ??= {}
    this.projectInfo.projectConfig.urlParams ??= {}
    this.projectInfo.projectConfig.urlParams[this.pageKey] = paramsData
    return this.projectInfo
  }

  // 更新页面环境参数
  replaceEnvInfo(paramsData: { envKey?: string; envValueList?: EnvValue[] } | undefined) {
    this.projectInfo.projectConfig ??= {}
    this.projectInfo.projectConfig.envInfo ??= {}
    if (paramsData) {
      this.projectInfo.projectConfig.envInfo = paramsData
    } else {
      this.projectInfo.projectConfig.envInfo = {
        envKey: 'ServerTest',
        envValueList: [
          { value: '0', desc: '正式' },
          { value: '1', desc: '测试' },
        ],
      }
    }
    return this.projectInfo
  }

  get componentSlotOptions() {
    const { url, path }: any = this.component || {}
    const slotOptions: any[] = []
    if (url) {
      const form = this.formCache[`${url}-${path}-slot`] || {}
      Object.keys(form).map((k: any) => {
        slotOptions.push({
          value: k,
          label: `${form[k].label} (${k})`,
        })
      })
    }
    return slotOptions
  }

  async getComponentSlotForm(component?: Component) {
    component = component && component.url ? component : this.component
    const { url, path }: any = component ?? {}
    if (!url || !component?.hasSlot) return
    let form = this.formCache[`${url}-${path}-slot`] || {}
    if (Object.keys(form).length === 0 && path) {
      let f_url = url.replace('/emp.js', '/astro/') + (path ?? '').replace('./', '') + '.slot.json'
      if (isDev && localDevEmpConfig?.empMap?.[f_url]) {
        f_url = localDevEmpConfig.empMap[f_url]
      }
      const d = await fetch(f_url)
        .then(d => d.json())
        .catch(e => {
          this.formCache[`${url}-${path}-slot`] = {}
        })
      form = d || {}
      this.formCache[`${url}-${path}-slot`] = form
    }
  }

  //后台环境相关=================================================
  templateControl = {
    hasEnv: true, // 是否开启环境参数，默认开启
    curEnvKey: 'ServerTest', // 当前环境key，默认值为ServerTest
    curEnvValue: '0', // 当前环境，默认0为正式环境
  }
  //判断当前环境
  checkCurTemplate() {
    // store.projectInfo?.tags => type ProjectTagsType = 1 | 2 | 3 | 4 | 5 // 1-活动 2-玩法 3-功能 4-后台 5-协议
    // if (this.projectInfo?.tags?.includes(4)) {) {
    this.templateControl.hasEnv =
      this.projectInfo?.projectConfig && 'envOpen' in this.projectInfo.projectConfig
        ? this.projectInfo.projectConfig.envOpen
        : true

    if (this.projectInfo?.projectConfig?.envInfo?.envKey) {
      this.templateControl.curEnvKey = this.projectInfo?.projectConfig?.envInfo?.envKey
    } else {
      this.templateControl.curEnvKey = 'ServerTest'
    }
  }

  setTemplateEnv(env: string) {
    this.templateControl.curEnvValue = env
  }

  /**
   * 递归移除组件及其子组件中的tagList字段
   */
  private removeTagListRecursively(component: Component): Component {
    const { tagList, ...componentWithoutTagList } = component

    // 如果有子组件，递归处理
    if (componentWithoutTagList.subComponents && componentWithoutTagList.subComponents.length > 0) {
      componentWithoutTagList.subComponents = componentWithoutTagList.subComponents.map(subComponent =>
        this.removeTagListRecursively(subComponent),
      )
    }

    return componentWithoutTagList
  }

  /**
   * 标记有变化的页面(新增、修改或删除)
   * @param p 新的页面数据
   */
  private _markChangedPages(p: Pages) {
    // 找出有差异的页面
    Object.keys(p).forEach(pageKey => {
      // 克隆当前页面和新页面数据
      const pageInfo = this.clone(this.pages[pageKey])
      const tempPageData = this.clone(p[pageKey])

      // 处理两个对象以便比较
      this._preparePageForComparison(pageInfo, null)
      this._preparePageForComparison(tempPageData, pageInfo?.components)

      // 比较处理后的对象
      if (!isEqual(pageInfo, tempPageData)) {
        console.log('####差异对比：', pageInfo, tempPageData)
        this.setFormOp(pageKey)
      }
    })

    // 处理被删除的页面
    Object.keys(this.pages).forEach(pageKey => {
      if (!p[pageKey]) {
        this.setFormOp(pageKey)
      }
    })
  }

  /**
   * 准备页面数据用于比较
   * @param page 页面数据
   * @param sourceComponents 源组件列表(用于dataConfig合并)
   */
  private _preparePageForComparison(page: any, sourceComponents?: Component[] | null) {
    if (!page?.components) return

    page.components = page.components.map((component: Component) => {
      // 移除tagList
      const componentWithoutTagList = this.removeTagListRecursively(component)

      // 如果提供了源组件，进行dataConfig合并
      if (sourceComponents) {
        const sourceComponent = sourceComponents.find(item => item.uniqueNum === component.uniqueNum)

        if (sourceComponent?.dataConfig && componentWithoutTagList.dataConfig) {
          const mergedDataConfig = {
            ...sourceComponent.dataConfig,
            ...componentWithoutTagList.dataConfig,
          }
          // 将空对象转为null
          componentWithoutTagList.dataConfig = Object.keys(mergedDataConfig).length > 0 ? mergedDataConfig : null
        } else if (
          !componentWithoutTagList.dataConfig ||
          Object.keys(componentWithoutTagList.dataConfig).length === 0
        ) {
          // 确保dataConfig存在，为null而不是{}
          componentWithoutTagList.dataConfig = null
        }

        // 处理子组件
        if (componentWithoutTagList.subComponents?.length) {
          const subSourceComponents = sourceComponent?.subComponents || []
          const subPage = { components: componentWithoutTagList.subComponents }
          this._preparePageForComparison(subPage, subSourceComponents)
          componentWithoutTagList.subComponents = subPage.components
        }
      } else {
        // 确保dataConfig存在，为null而不是{}
        if (!componentWithoutTagList.dataConfig || Object.keys(componentWithoutTagList.dataConfig).length === 0) {
          componentWithoutTagList.dataConfig = null
        }

        // 处理子组件
        if (componentWithoutTagList.subComponents?.length) {
          const subPage = { components: componentWithoutTagList.subComponents }
          this._preparePageForComparison(subPage, null)
          componentWithoutTagList.subComponents = subPage.components
        }
      }

      return componentWithoutTagList
    })
  }
}
const pageStore = new PageStore()
export default pageStore
