import {Splitter} from '@astro/ui/ui'
import {useEffect, useState} from 'react'
import {useParams} from 'react-router-dom'
import pageStore from 'src/components/playload/editPanel/pageStore'
import config from 'src/config'
import SocketEntry from '../UserSocket/SocketEntry'
import ConfigurePanel from './configurePanel'
import DrawPanel from './drawPanel'
import F2CPanel from './f2cPanel'
import f2cStore from './f2cStore'
import css from './index.module.scss'
import './index.scss'
// import SocketEntry from '../UserSocket/YScoket'
import FontEndAgent from '../FontEndAgent'
import panelStore from './panelStore'
import ToolPanel from './toolPanel'
import VersionPanel from './versionPanel'
const EditPanel = () => {
  const Panel = Splitter.Panel
  const pageState = pageStore.state
  const panelState = panelStore.state
  const f2cState = f2cStore.state
  const {id: projectId} = useParams()
  // 打开socket 默认关闭 完善好再打开
  const [openSocket, setOpenSocket] = useState(config.isOpenSocket)
  const [isFinishLoadPages, setIsFinishLoadPages] = useState(false)
  useEffect(() => {
    if (!projectId) return
      ; (async () => {
        setIsFinishLoadPages(false)
        await pageState.getProjectPagesAction(projectId)
        // pagesBus.publish('fnishGetProjectPagesAction')
        setIsFinishLoadPages(true)
      })()
  }, [projectId])

  useEffect(() => {
    //f2c按页面维度全局换皮
    f2cStore.updateComponentListValue()
  }, [f2cState.f2cGroupInfo])

  useEffect(() => {
    console.log('pageState projectInfo', pageState.projectInfo)
  }, [pageState])

  return (
    <>
      {openSocket && isFinishLoadPages && <SocketEntry projectId={projectId as string} />}
      <Splitter className={css.container}>
        <Panel
          key={'leftPanel'}
          min={panelState.side.min}
          max={panelState.side.max}
          defaultSize={panelState.side.left}
          className={`${css.leftColumn} leftPannel`}
        >
          {/* 左侧工具面板 */}
          <ToolPanel />
        </Panel>

        <Panel
          key={'middlePanel'}
          collapsible={{start: true}}
          className={`${css.middleColumn} ${pageState.projectInfo.tags?.includes(4) && css.admin}`}
        >
          {/* 中间画布面板 */}
          <DrawPanel />
        </Panel>

        <Panel
          key={'rightPanel'}
          min={panelState.side.min}
          max={panelState.side.max}
          defaultSize={panelState.side.right}
          className={`${css.rightColumn} rightPannel`}
        >
          {/* 右侧编辑面板 */}
          <VersionPanel currentSelectedComponent={pageState.currentSelectedComponent} showSubmitButton={true} />
          {/* 组件F2C面板 */}
          <F2CPanel />
          <ConfigurePanel />
        </Panel>
      </Splitter>
      <FontEndAgent key={'FontEndAgent'} />
    </>
  )
}
export default EditPanel
