import {message} from '@astro/ui/ui'
import pageState from 'src/components/playload/editPanel/pageStore'
import {configVersionList, listGroupLatestVersion} from 'src/services/common'
import {searchMComponent} from 'src/services/material'
import {BaseStore} from 'src/store'
import {getF2CGroupItemId, getF2cMap, getF2cMapForConfig1, getF2cMapForConfig2, processDataWithReplacements} from 'src/utils/f2c'
import type {Component, Page, SformType} from './types'
/**
 * F2C换肤说明
 * ……背景：
 * 1、页面：可以满足全局一个类型组件一个样式，所以能用全局唯一key，即使少部分情况需要用2个相同组件，也可以用configId绑定来满足全局key唯一
 * 2、pc实时通信页面：经常出现一个组件多套样式情况，按设计习惯还可能出现嵌套情况，则无法用全局唯一key来做唯一标识，需要父级key来辅助匹配（但建议定义key还是得足够唯一）
 *
 * ……单个组件方案：
 * 1、输入configId，拆解所有对象的key，平铺匹配所有组件key做替换
 * 【暂不开放】2、输入configId，拆解所有对象的key，除了当前组件的数据，其它对象的key加个前缀“父级名_”,这个方案需要写组件时候，嵌套了其它组件时候，也按这个规则平铺props的key
 * 用户选择后记住当前组件的覆盖方案。(由于再不同的上传场景下，configId并不一定唯一，但不管什么configId，组件的替换方案是肯定的)
 *
 * ……全局页面UI替换方案:(还原组件方案不涉及)
 * 输入groupId->
 * 判断是否每个组件有绑定configId->
 *  1、有，则按单个组件方案替换
 *  2、无，按groupItemId规则（包含组件名‘[组件名]’）给组件绑定configId ->
 *    a、能绑定，则每个组件，按单个组件方案替换（默认还是唯一key，不符合用户第一次需要选择对应方案）
 *    b、不能绑定，则按全局唯一key替换
 */

//f2c换肤，当前使用到的字段configId，groupItemId，value
export type F2cGroupType = {
  bucketId: number
  bucketName: string
  businessId: number
  components: Array<any>
  configId: number // 组件绑定的id，先通过groupItemId搜索组件，在绑定id，为了后续多个同名组件下可以唯一匹配
  createTime: string
  createUid: number
  createUsername: string
  groupId: string
  groupItemId: string // 我们用来搜索组件的字段， f2c为文件目录
  id: number
  tags: Array<string>
  value: string // f2c内的组件数据，我们使用allComCfg来还原{allComCfg:{组件名字:{...}}},(一个组件可能内部包了n个组件)
  version: number
}
export type DiffComType = {
  uniqueNum: string //组件唯一id
  ck: number  // 组件的componentKey
  sformType: SformType | undefined //组件类型 
  name: string //组件名称
  normalValue?: string, // 组件from表单的原始数据
  newValue: Map<string, any>, // F2C组件的未使用的数据
  editingValue: string,// 组件当前编辑的数据
  dataConfig: any // 组件的待更新的配置数据
  hitKey?: string, // 命中的key
  replacedMap: Map<string, any>, // F2C组件已找到的属性
  modifyValue?: string, // 组件最终的编辑数据
  form?: any // 组件的本地form表单配置
}
let comNum = 0
export class F2cStore extends BaseStore {
  hasCom = false // 当前页面是否有组件
  updataComF2CInfo = {} // 更新的当前key的组件
  f2cGroupInfo: F2cGroupType[] = []
  showDiffModal = false //是否显示属性diff弹窗
  diffComMap = new Map<string, DiffComType>()
  resetF2CConfigId = false

  setUpdataComF2CInfo(info: any) {
    this.updataComF2CInfo = JSON.parse(JSON.stringify(info))
  }
  setHasCom(b: boolean) {
    this.hasCom = b
  }

  openResetF2CConfigId() {
    this.resetF2CConfigId = true
  }

  setDiffComList(com: DiffComType) {
    const map = new Map([...this.diffComMap])  // 正确拷贝Map的方式
    map.set(com.uniqueNum, com)
    this.diffComMap.clear()
    this.diffComMap = map
  }
  setShowDiffModal(b: boolean) {
    !b && this.diffComMap.clear() // 关闭弹窗清空数据
    this.showDiffModal = b
  }
  async syncf2cInfo(groupId: string) {
    const promise2 = await this.getListGroupLatestVersion(groupId)
    const keyword = getF2CGroupItemId(this.f2cGroupInfo) || ''
    // console.log('######---keyword', this.f2cGroupInfo, keyword)
    const promise1 = await searchMComponent({keyword}, 300)//await getMComponent('')
    Promise.all([promise1, promise2]).then(async (values: any) => {
      // console.log('######---promise1', promise1, this.f2cGroupInfo)
      const list = values?.[0]?.list || []
      await this.f2c2components(list)
      this.updateComponentListValue()
    })
  }

  async syncAiInfo(promise2: string) {
    const promise1 = await searchMComponent({})
    // const promise2: any =  JSON.stringify({
    //           '挂件逻辑处理[astro_ui_widget_components_PluginBase]': {
    //             rootFontSize: '{"h5":"calc(100vw / 2.23)","pc":"60px","inner":"60px"}',
    //           },
    //           '信息提示面板[astro_ui_widget_components_Warn]': {slotKey: 'comList_nav'},
    //           '倒计时面板[astro_ui_widget_components_Waiting]': {slotKey: 'waiting_nav'},
    //           '缩小面板[astro_ui_widget_components_MiniWidget]': {slotKey: 'mini_nav'},
    //           '小横幅[astro_ui_widget_components_WidgetMarquee]': {slotKey: 'banner_nav'},
    //           '蒙层[astro_ui_widget_components_Mask]': {slotKey: 'mask_nav'},
    //         }),

    Promise.all([promise1, promise2]).then(async (values: any) => {
      await this.dealComponents(values)
    })
  }
  //获取f2c组件关键key
  getComKey(com: any) {
    const reg = /\[(.*?)\]/
    const f2cKey = com?.groupItemId?.match(reg)?.[1] ?? '' // 提取中括号内的组件标识，如“勋章[astro_ui_barrage_rule]”里的astro_ui_barrage_rule
    if (!f2cKey) {
      try {
        const info = JSON.parse(com?.value)
        if (info?.rootKey) {
          return info?.rootKey.match(reg)?.[1] ?? ''
        }
      } catch (e: any) {

      }
    }
    return f2cKey
  }

  /**
   * 新增模糊匹配和精准匹配区别
   * 模糊匹配：1,精准匹配：2 
   * 由于F2C可能是重复复制的情况，则可能存在多个组件相同key的情况
   * 所以需要引入模糊和精准区别
   * 精准匹配：组件名完全一致，如“勋章[astro_ui_barrage_rule]”  或者组件存在configId配置
   * 模糊匹配：则只是参数key相同。
    **/
  getComType(uniqueNum: string) {
    try {
      const page = pageState.page
      const components = page.components
      const componentList: any[] = components ? [...components] : []
      componentList.push(page)
      for (const component of componentList) {
        const {scope = '', path = ''} = component
        const componentKey = `${scope.match(/^(.*?)_\d+_\d+_\d+$/)?.[1] || scope}_${path.match(/\/(.*)$/)?.[1]}`
        const comUniqueNum = component.uniqueNum || component?.pageKey || ''
        if (uniqueNum == comUniqueNum) {
          for (const [index, item] of this.f2cGroupInfo.entries()) {
            const f2cKey = this.getComKey(item)
            if (!f2cKey) {
              return 1 // 图层命名不规范
            }

            if (f2cKey == componentKey || component.ext && component.ext?.configIdF2C == item?.configId) {
              return 2
            }
          }
        }
      }
    } catch (e: any) {
      console.error('【F2C】组件的getComType匹配异常', e.message)
      return {
        err: e,
      }
    }
    return 1
  }
  //groupId更新组件
  async changeF2cInfo(groupId: string) {
    if (this.hasCom) {
      this.openResetF2CConfigId()
      this.getListGroupLatestVersion(groupId)
    } else {
      await this.syncf2cInfo(groupId)
    }
  }
  updateComponentListValue() {
    this.updateComponentsConfigId()
    this.updateComponentsToKey()
  }
  //重置组件内的configId,按新的groupId重新绑定
  updateComponentsConfigId() {
    if (this.resetF2CConfigId) {
      this.resetF2CConfigId = false
      try {
        const page = pageState.page
        const components = page.components
        const componentList: any[] = components ? [...components] : []
        componentList.push(page)
        for (const [index, item] of this.f2cGroupInfo.entries()) {
          const f2cKey = this.getComKey(item)
          if (!f2cKey) continue // 图层命名不规范

          for (const component of componentList) {
            const {scope = '', path = ''} = component
            const componentKey = `${scope.match(/^(.*?)_\d+_\d+_\d+$/)?.[1] || scope}_${path.match(/\/(.*)$/)?.[1]}`

            // 匹配到组件库组件
            if (f2cKey === componentKey) {
              !component.ext && (component.ext = {})
              item?.configId && (component.ext.configIdF2C = item?.configId)
              item?.version && (component.ext.configIdVersionF2C = item?.version)
            }
          }
        }
      } catch (e: any) {
        console.error('【F2C】组件的F2C configId匹配异常', e.message)
        return {
          err: e,
        }
      }
    }
  }
  //按全局唯一key更新
  updateComponentsToKey() {
    try {
      const globalF2CKeyMap = getF2cMap(this.f2cGroupInfo)
      const page = pageState.page
      console.log('###---globalF2CKeyMap', globalF2CKeyMap, this.f2cGroupInfo)
      if (globalF2CKeyMap) {
        //layout层
        if (page && page?.dataConfig) {
          // pageState.page.dataConfig = deepAssignMap(this.clone(pageState.page.dataConfig), globalF2CKeyMap)
          let pageMap
          if (
            page?.ext &&
            'configIdF2C' in page.ext &&
            page.ext.configIdF2C &&
            globalF2CKeyMap.get(page.ext.configIdF2C.toString())
          ) {
            pageMap = globalF2CKeyMap.get(page.ext.configIdF2C.toString())
          } else {
            pageMap = globalF2CKeyMap
          }
          // console.log('###---pageMap', page)
          // const dataConfig = deepAssignMap(this.clone(page.dataConfig), pageMap)
          // pageState.saveFormDataConfig(dataConfig, pageState.pageKey, undefined, 'page')
          this.pushDiffList(page, pageMap, 'page')

        }
        //componentsList
        const components = page.components
        components?.map((v, k) => {
          // if (v.dataConfig) v.dataConfig = deepAssignMap(this.clone(v.dataConfig), globalF2CKeyMap)
          let comMap
          if (
            v?.ext &&
            'configIdF2C' in v.ext &&
            v.ext.configIdF2C &&
            globalF2CKeyMap.get(v.ext.configIdF2C.toString())
          ) {
            comMap = globalF2CKeyMap.get(v.ext.configIdF2C.toString())
          } else {
            comMap = globalF2CKeyMap
          }
          // console.log('###---v', v)
          // const dataConfig = deepAssignMap(this.clone(v.dataConfig), comMap)
          // pageState.saveFormDataConfig(dataConfig, pageState.pageKey, k, 'component')
          this.pushDiffList(v, comMap, 'component', k)
        })
      } else {
        message.error('【F2C】数据解析异常~~')
      }
    } catch (e: any) {
      console.error('【F2C】数据解析异常~~', e.message)
      return {
        err: e,
      }
    }
  }
  //按组件configId更新(所有)
  updateComponentsToConfigId() {
    // Todo=====
  }
  replaceMapNameForForm(map: Map<string, any>, form: any) {
    map.forEach((value, key) => {
      if (key in form) {
        const item = value
        item.name = form[key]?.name
      }
    })
  }

  //按组件configId更新(单个)
  async updateComponentToConfigId(component: Component, data: F2cGroupType, modeF2C = 1) {
    try {
      const singleF2CKeyMap = modeF2C == 1 ? getF2cMapForConfig1(data) : getF2cMapForConfig2(data)
      if (singleF2CKeyMap) {
        this.pushDiffList(component, singleF2CKeyMap)
      }
    } catch (e: any) {
      console.error('【F2C】数据解析异常~~', e.message)
      return {
        err: e,
      }
    }
  }
  async pushDiffList(component: Component | Page, map: Map<string, any>, sformType: SformType | undefined = undefined, ck: number | undefined = undefined) {
    try {
      // console.log('###component.dataConfig', modeF2C, JSON.stringify(component.dataConfig), singleF2CKeyMap)
      // component.dataConfig = deepAssignMap(this.clone(component.dataConfig), singleF2CKeyMap)
      // const dataConfig = deepAssignMap(this.clone(component.dataConfig), singleF2CKeyMap) //深度替换
      const {target: dataConfig, replacedMap, unreplacedMap} = processDataWithReplacements(this.clone(component.dataConfig), map)//深度替换并返回未使用数据
      // console.log('###component.dataConfig after', component.name, 'target', JSON.stringify(dataConfig), replacedMap, unreplacedMap)

      // const testMap = new Map(Object.entries({
      //   "waitingTextMgT": "247px",
      //   "astro_static_txt_color": "#e5f2ff",
      //   "WaitingTextMsgEmpty": "-10px",
      //   "astro_bg_h": "330px",
      //   "countdownText": '1111111111111',
      //   "astro_dynamics_txt_color": "#ffe99a",
      //   "astro_bg": "url(https://zhuiya.bs2cdn.yy.com/f2c/10000/16448/8055/0aa0bf74e4094ee580ed8a1263e175b6.png)",
      //   "astro_bg_w": "222px"
      // }))
      // const {target, replacedMap, unreplacedMap} = processDataWithReplacements(this.clone(component.dataConfig), testMap)//深度替换并返回未使用数据
      // console.log('###component.dataConfig after', testMap, 'target', JSON.stringify(target), replacedMap, unreplacedMap)
      // if (replacedMap?.size <= 0) {
      //   message.warning(`组件“${component?.name}” 未匹配到相关数据`)
      //   return
      // }
      const form = await pageState.getForm(component)
      /*let hitValue = null
      let hitKey = ''
      // console.log('###component.dataConfig after----', form)
      Object.entries(form ?? {}).forEach(([key, item]: any) => {
        //过滤属性是否有'AstroTypes.Code' |  'AstroTypes.Scss'类型
        if (item.type == 'AstroTypes.Code' || item.type == 'AstroTypes.Scss') {
          hitKey = key
          hitValue = item.type == 'AstroTypes.Code' ? JSON.stringify(item?.value) : item?.value
          // console.log('###component.dataConfig Code----hitKey', item, key, hitValue)
        }
      })
      if ((hitKey && unreplacedMap?.size > 0) || replacedMap?.size > 0) {
        this.replaceMapNameForForm(replacedMap, form)
        const obj: DiffComType = {
          uniqueNum: component.uniqueNum || '',
          name: component.name || '',
          normalValue: hitValue || '',
          newValue: unreplacedMap,
          editingValue: dataConfig?.[hitKey],
          replacedMap: replacedMap,
          dataConfig,
          hitKey,
        }*/
      if ((unreplacedMap?.size > 0) || replacedMap?.size > 0) {
        this.replaceMapNameForForm(replacedMap, form)
        //layout 组件没有uniqueNum，此处用pageKey代替
        const obj: DiffComType = {
          uniqueNum: component.uniqueNum || component?.pageKey || '',
          name: component.name || '',
          editingValue: '',//由于配置可能存在再其它group内，此处不匹配
          sformType: sformType || pageState.sformType,
          form,
          ck: ck != undefined ? ck : pageState.componentKey,
          newValue: unreplacedMap,
          replacedMap: replacedMap,
          dataConfig,
        }
        // console.log('###component.dataConfig obj----', obj)
        this.setDiffComList(obj)
        this.setShowDiffModal(true)
        return
      }
      message.warning(`组件“${component?.name}” 未匹配到相关数据`)

      // pageState.saveFormDataConfig(dataConfig)
      // console.log('###component.dataConfig after', JSON.stringify(component.dataConfig))
    } catch (e: any) {
      console.error('【F2C】数据解析异常~~', e.message)
      return {
        err: e,
      }
    }
  }
  async changeComDataCfgV(uniqueNum: string, pathArr: Array<any>, newV: any) {
    // console.log('###changeComDataCfgV', uniqueNum, pathArr, newV)
    const page = pageState.page
    let sformType: SformType = 'component'
    let ck: number = pageState.componentKey
    let fineCom: Component | Page | undefined = undefined
    if (page && page?.dataConfig) {
      if (page?.pageKey == uniqueNum) {
        fineCom = page
        sformType = 'page'
      }
    }
    if (!fineCom) {
      const components = page.components
      components?.map((v, k) => {
        if (v?.uniqueNum == uniqueNum) {
          fineCom = v
          ck = k
        }
      })
    }
    if (fineCom) {
      // console.log('###fineCom', fineCom)
      const dataConfig = this.clone(fineCom.dataConfig)
      let curData = dataConfig
      let flg = false
      for (const [index, item] of pathArr.entries()) {
        const key = item.itemKey
        if (index == pathArr.length - 1) {
          curData[key] = newV
          flg = true
        } else {
          curData = curData[key]
        }
      }
      flg && pageState.saveFormDataConfig(dataConfig, pageState.pageKey, ck, sformType)
      console.log('###curData', dataConfig, curData)
    }
    // 遍历diffComMap
    // this.diffComMap.forEach((v, k) => {
    //   const dataConfig = this.clone(v.dataConfig)
    //   let flg = true
    //   if (v.hitKey && v.modifyValue) {
    //     dataConfig[v.hitKey] = v.modifyValue// 替换成修改后的配置
    //   }

    //   if (selArr && !selArr?.includes(k)) {
    //     flg = false
    //   }
    //   flg && pageState.saveFormDataConfig(dataConfig, pageState.pageKey, v.ck, v.sformType)
    // })
    message.success('页面替换成功')
  }

  async saveComF2CInfo(selArr: string[] | undefined = undefined) {
    // 遍历diffComMap
    let flg = false
    let hasModifyValue = false
    await this.diffComMap.forEach(async (v, k) => {
      const dataConfig = this.clone(v.dataConfig)

      if (selArr && selArr?.includes(k)) {
        if (v.hitKey && v.modifyValue) {
          dataConfig[v.hitKey] = v.modifyValue// 替换成修改后的配置
          hasModifyValue = true
        }
        await pageState.saveFormDataConfig(dataConfig, pageState.pageKey, v.ck, v.sformType)
        flg = true
      }
    })
    if (flg) {
      message.success(`换肤成功${!hasModifyValue ? '（配置文件（json数据/css文件）没有更新）' : ''}`)
    } else {
      message.error('换肤异常，未匹配到对应组件')
    }
  }

  getComponent() {
    let curCompent: any = null
    const pageInfo = pageState.page
    if (pageState.componentKey > -1) {
      const key = pageState.componentKey >= 0 ? pageState.componentKey : 0
      const componentInfo = pageInfo.components?.[key]
      curCompent = componentInfo
      // console.log('获取ext componentInfo', componentInfo, key, componentInfo?.ext)
    } else {
      // console.log('获取ext page', pageInfo, pageInfo.ext)
      curCompent = pageInfo
    }
    return curCompent
  }

  // 根据groupId查询f2c样式数据
  async getListGroupLatestVersion(d: string) {
    try {
      const {data: res} = await listGroupLatestVersion(d)

      if (res && res?.code == 0) {
        this.f2cGroupInfo = res?.data
      } else {
        message.open({type: 'error', content: `F2C数据获取异常，请稍后重试~ code:${res?.code}`, duration: 6})
      }
    } catch (e) {
      return {
        err: e,
      }
    }
  }

  // 根据configId查询f2c样式数据
  async getConfigVersionList(configId: any) {
    try {
      const {data: res} = await configVersionList(configId)

      if (res && res?.code == 0) {
        return res?.data
      } else {
        message.open({type: 'error', content: `F2C数据获取异常，请稍后重试~ code:${res?.code}`, duration: 6})
      }
    } catch (e) {
      return {
        err: e,
      }
    }
    return null
  }

  // async f2c2components(componentList: Component[]) {
  //   for (const [index, item] of this.f2cGroupInfo.entries()) {
  //     console.log('###item.value', item)
  //     const f2cKey = item?.groupItemId?.match(/\[(.*?)\]/)?.[1] ?? '' // 提取中括号内的组件标识，如“勋章[astro_ui_barrage_rule]”里的astro_ui_barrage_rule
  //     if (!f2cKey) continue // 图层命名不规范

  //     for (const cpt of componentList) {
  //       const component = this.clone(cpt)
  //       const {scope = '', path = ''} = component
  //       const componentKey = `${scope.match(/^(.*?)_\d+_\d+_\d+$/)?.[1] || scope}_${path.match(/\/(.*)$/)?.[1]}`

  //       // 匹配到组件库组件
  //       if (f2cKey === componentKey) {
  //         console.log('###f2cKey === componentKey', f2cKey, componentKey)
  //         component.uniqueNum = `${pageState.pageKey}_${component.id}_${new Date().getTime()}_${item.configId}` // 前端根据引入组件时间生成唯一值序列号
  //         component.slotKey = ''
  //         pageState.addComponent(component)
  //         item?.configId && pageState.saveFormItemExt('configIdF2C', item?.configId)
  //         item?.version && pageState.saveFormItemExt('configIdVersionF2C', item?.version)
  //         await pageState.getComponentForm()
  //         // 设置小红点
  //         // pageState.setFormOp(pageState.pageKey, pageState.componentKey)
  //       }
  //     }
  //   }
  // }
  async f2c2components(componentList: Component[]) {
    const newComponents: any[] = []
    for (const [index, item] of this.f2cGroupInfo.entries()) {
      // console.log('###item.value', item)
      const f2cKey = this.getComKey(item)
      if (!f2cKey) continue // 图层命名不规范

      for (const cpt of componentList) {
        const component = this.clone(cpt)
        const {scope = '', path = ''} = component
        const componentKey = `${scope.match(/^(.*?)_\d+_\d+_\d+$/)?.[1] || scope}_${path.match(/\/(.*)$/)?.[1]}`

        // 匹配到组件库组件
        if (f2cKey === componentKey) {
          this.findComList(newComponents, component, item)
          // component.uniqueNum = `${pageState.pageKey}_${component.id}_${new Date().getTime()}_${item.configId}` // 前端根据引入组件时间生成唯一值序列号
          // component.slotKey = ''
          // pageState.addComponent(component)
          // item?.configId && pageState.saveFormItemExt('configIdF2C', item?.configId)
          // item?.version && pageState.saveFormItemExt('configIdVersionF2C', item?.version)
          // await pageState.getComponentForm()

          // 设置小红点
          // pageState.setFormOp(pageState.pageKey, pageState.componentKey)
        }
      }
    }
    if (newComponents?.length > 0) {
      await this.addComonentList(newComponents)
    }
  }
  async findComList(newComponents: any, component: any, item: any) {
    try {
      if (component.componentType == 1) {
        //组件
        component.uniqueNum = `${pageState.pageKey}_${component.id}_${comNum++}_${new Date().getTime()}` // 前端根据引入组件时间生成唯一值序列号
        component.slotKey = item?.slotKey || ''
        component.dataConfig = item?.dataConfig || {}
        console.log('###astro  fine', component, new Date().getTime())
      } else if (component.componentType == 2) {
        //layout
        console.log('###astro  fine layout', component, new Date().getTime())
        if ('rootFontSize' in item) {
          if (!component?.ext) {
            component.ext = {}
          }
          component.ext.rootFontSize = item.rootFontSize
        }
        if ('dataConfig' in item) {
          component.dataConfig = item.dataConfig
        }
      }
      !component.ext && (component.ext = {})
      item?.configId && (component.ext.configIdF2C = item?.configId)
      item?.version && (component.ext.configIdVersionF2C = item?.version)

      // console.log('###astro  findComList', component, item)
      newComponents && newComponents.push(component)
    } catch (err: any) {
      console.error('astro 服务器数据异常，请稍后重试', err.message)
    }
  }
  //根据ai数据重组
  async dealComponents(dataArr: any) {
    try {
      console.log('astro dealComponents', dataArr)
      const result: string = dataArr[1]
      if (result) {
        if (result == '{}') {
          message.warning('未找到相关的组件配置，请重新输入')
          return
        }
        const comLis = Object.entries(JSON.parse(result))
        const searchComponents = dataArr?.[0]?.list
        let num = 1
        let hasFind = false
        let newComponents: any[] = []

        comLis.map(async ([key, value]) => {
          console.log('astro ###item.value', value, key)
          const curKey = key?.match(/\[(.*?)\]/)?.[1] ?? key // 提取中括号内的组件标识，如“勋章[astro_ui_barrage_rule]”里的astro_ui_barrage_rule
          if (!curKey) return // 图层命名不规范

          for (const cpt of searchComponents) {
            const component = this.clone(cpt)
            const {scope = '', path = ''} = component
            const componentKey = `${scope.match(/^(.*?)_\d+_\d+_\d+$/)?.[1] || scope}_${path.match(/\/(.*)$/)?.[1]}`
            // console.log('astro key', curKey, componentKey)
            // 匹配到组件库组件
            if (curKey === componentKey) {
              if (!hasFind) {
                hasFind = true
              }
              if (component.componentType == 1) {
                //组件
                component.uniqueNum = `${pageState.pageKey}_${component.id}_${num++}_${new Date().getTime()}` // 前端根据引入组件时间生成唯一值序列号
                component.slotKey = value?.slotKey || ''
                component.dataConfig = value?.dataConfig || {}
                console.log('astro ai fine', curKey, component, new Date().getTime())
                // await pageState.addComponent(component)
                // await pageState.getComponentForm()
              } else if (component.componentType == 2) {
                //layout
                console.log('astro ai fine layout', curKey, component, new Date().getTime())
                if ('rootFontSize' in value) {
                  if (!component?.ext) {
                    component.ext = {}
                  }
                  component.ext.rootFontSize = value.rootFontSize
                }
                if ('dataConfig' in value) {
                  component.dataConfig = value.dataConfig
                }
                //   await pageState.SyncPageRemoteForm(component)
                //   await pageState.editPage(pageState.pageKey, component, pageState.pageKey, true)
              }
              newComponents.push(component)
              break
            }
          }
        })
        if (hasFind) newComponents = [...newComponents.map((item, id) => id)]
        return newComponents
      } else {
        message.warning('未找到相关的组件配置，请添加一些相关的信息')
      }
    } catch (err: any) {
      console.error('astro 服务器数据异常，请稍后重试', err.message)
      message.error('服务器数据异常，请稍后重试' + err.message)
    }
  }

  async addComonentList(list: any[]) {
    try {
      while (list.length > 0) {
        const item = list.shift()
        await this.addComonent(item)
      }
    } catch (err: any) {
      console.error('astro 添加组件异常[addComonentList]，请稍后重试', err.message)
      message.error('添加组件[addComonentList]数据异常，请稍后重试' + err.message)
    }
  }
  async addComonent(component: any) {
    try {
      if (component.componentType == 1) {
        //组件
        await pageState.addComponent(component)
        await pageState.getComponentForm()
      } else if (component.componentType == 2) {
        //layout
        if ("name" in component) {
          //避免重置页面命名
          delete component.name
        }
        await pageState.SyncPageRemoteForm(component)
        await pageState.editPage(pageState.pageKey, component, pageState.pageKey, true)
      }
    } catch (err: any) {
      console.error('astro 添加组件异常，请稍后重试', err.message)
      message.error('添加组件数据异常，请稍后重试' + err.message)
    }
  }
  async getAllForm() {
    const formList: any = []
    const componentList: any = [pageState.page, ...(pageState.page?.components || [])]
    for (const cpt of componentList) {
      const form = await pageState.getForm(cpt)
      form && formList.push({key: cpt?.uniqueNum || cpt?.pageKey || '', com: cpt, form})
    }
    return formList
  }
}
export default new F2cStore()
