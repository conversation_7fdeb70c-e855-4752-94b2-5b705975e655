export interface Component {
  uniqueNum?: string // 组件唯一序列号，前端生成
  id?: string // 组件详情页id，编辑页面需要，找详情
  img?: string // 组件图片
  name?: string // 组件名称
  alias?: string // 组件别名（当前项目&&当前页面内使用）
  url?: string // 组件地址
  path?: string // 组件路径
  scope?: string // 组件包名
  slotKey?: string | null // 所属分区，无分区默认为null
  sort?: number
  dataConfig?: {[key: string]: any} | null
  componentType: number // 组件类型，1普通组件，2layout组件
  hasSlot?: boolean // 当前组件是否有子组件插槽
  subComponents?: Component[] // 新增子组件列表
  selected?: boolean // 前端自定义，标识当前组件是否被选中
  // 拓展字段，存放页面的其他配置
  ext?: {
    configIdF2C?: string // F2C 组件的configId
    configIdVersionF2C?: string // F2C 组件的configId的版本
    modeF2C?: number // F2C 组件的替换方式
  }
  project?: string //包名，用于批量新增/修改的判断
  tagList?: {
    tagId: number
    tagName: string
    typeId: number
  }[]
  hasSlot?: boolean // 是否有插槽
}

export interface Pages {
  [key: string | number]: Page
}
export interface Page {
  groupId?: string // F2C的groupId
  name?: string // 页面名字，编辑页面需要
  pageKey?: string //页面路由
  online?: boolean // 页面是否上线
  url?: string // layout组件地址，layout容器有需要选择的情况
  path?: string // layout的path
  scope?: string // layout的scope
  dataConfig?: {[key: string]: any} // 表单配置数据
  sort?: number // 前端自定义，排序用
  isDark?: boolean // 前端自定义，是否是深色模式
  slots?: {
    [key: string]: object
  }
  // 拓展字段，存放页面的其他配置
  ext?: {
    rootFontSize?: '' // 三端根节点字号
    configIdF2C?: string // F2C 组件的configId
    configIdVersionF2C?: string // F2C 组件的configId的版本
    modeF2C?: number // F2C 组件的替换方式
  }
  components?: Component[]
}
export type SformType = 'page' | 'component'

export type PlatformType = 'h5' | 'inner' | 'pc'

export enum FormItemType {
  Biz = 'FormItemType.Biz',
  Ui = 'FormItemType.Ui',
  Server = 'FormItemType.Server',
}
