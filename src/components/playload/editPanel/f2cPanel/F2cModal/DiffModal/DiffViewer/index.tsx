
import {SwapOutlined} from '@astro/ui/icon'
import {<PERSON>, Collapse, ColorPicker, Flex, List, Result, Space, Spin, type CollapseProps} from '@astro/ui/ui'
import Editor from '@monaco-editor/react'
import {useEffect, useRef, useState} from 'react'
import type {DiffComType} from 'src/components/playload/editPanel/f2cStore'
import {removeUrlStr} from 'src/utils/f2c'
import css from './index.module.scss'

function isColor(color: string): boolean {
  const colorRegex = /^(#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{8}|rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)|rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*([01]|0\.\d+)\)|hsl\((\d{1,3}),\s*(\d{1,3})%,\s*(\d{1,3})%\)|hsla\((\d{1,3}),\s*(\d{1,3})%,\s*(\d{1,3})%,\s*([01]|0\.\d+)\))$/
  return colorRegex.test(color)
}

function isImage(path: string): boolean {
  return /(https?:\/\/[^\s"'<>]+\.(?:jpg|jpeg|png|gif|svg|mp4|svga)(?:\?[^\s"'<>]*)?)/gi.test(path)
  // return /\.(png|jpg|jpeg|gif|webp|svg|mp4|svga)$/i.test(path)
}
const stringifyCSSVariables = (variables: Record<string, string>, originalContent: string, replacedObj: any, type = 2): string => {
  //type 1: 按当前配置替换 2: 未匹配的内容看注解方式注入
  let result = originalContent
  let braceCount = 0 // 用于计数 { 和 } 的数量
  let braceStartIndex = -1 // { 的起始索引
  const originalValues: Record<string, string> = {}

  for (let i = 0; i < result.length; i++) {
    if (result[i] === '{') {
      braceCount++
      if (braceCount === 1) {
        braceStartIndex = i
        break
      }
    } else if (result[i] === '}') {
      braceCount--
    }
  }
  let hasBrace = false
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`${key}:\\s*([^;]+);?`)
    if (regex.test(result)) {
      const match = result.match(regex)
      if (match) {
        originalValues[key] = match[1].trim()
      }
      result = result.replace(regex, `${key}: ${value};`)
      replacedObj[key] = {original: originalValues[key], target: value}
    } else if (type == 2) {
      if (braceStartIndex !== -1) {
        const braceEndIndex = result.indexOf('}', braceStartIndex)
        if (braceEndIndex !== -1) {
          result = result.slice(0, braceEndIndex) + `${hasBrace ? '\n' : ''}// ${key}: ${value};`//+ result.slice(braceEndIndex)
          hasBrace = true
          continue
        }
      }
      result += `\n// ${key}: ${value};`
    }
  }
  if (hasBrace) {
    result += '\n}'
  }

  return result
}
const parseCSSVariables = (cssContent: string) => {
  const variables: any = {}
  const lines = cssContent.split('\n')
  for (const line of lines) {
    if (line.trim().startsWith('//') || line.trim() === '') continue

    const varMatch = line.match(/([^:]+):\s*([^;]+);?/)
    if (varMatch) {
      const key = varMatch[1].trim()
      variables[key] = varMatch[2].trim()
    }
  }
  return variables
}
interface IDiffViewer {
  type: 1 | 2, // 1: 按当前配置替换 2: 补齐所有配置
  item: DiffComType
}
const DiffViewer = (props: IDiffViewer) => {
  // const {type = 1, normalValue, newValue, editingValue} = props
  const {type = 1, item} = props
  //normalValue 暂时未用
  const {normalValue, newValue, editingValue, replacedMap} = item
  const [state, setState] = useState(1)//1: loading 2: 预览 404: 错误
  const [oldText, setOldText] = useState<any>({})
  const [newText, setNewText] = useState<any>({})
  const [allListItems, setAllListItems] = useState<any>([])
  const [listItems, setListItems] = useState<any>([])
  const [isCSS, setIsCSS] = useState(false)

  const editorRefL = useRef<any>(null)
  const editorRefR = useRef<any>(null)

  const getResultDiff = () => {
    try {
      setState(1)
      let editNewObj: any = Object.fromEntries(newValue)
      const oldText = JSON.stringify(editNewObj)
      let resultObj: any = {}
      let isCSS = false
      let replacedObj: any = {}
      const editingStr = typeof editingValue == 'string' ? editingValue?.replace(/\r\n/g, '\n').trim() : editingValue
      if (editingStr) {
        try {
          resultObj = typeof editingStr == 'string' ? JSON.parse(editingStr) : editingStr
        } catch (parseError) {
          // console.warn('editingValue is not a valid JSON string:', parseError)
          // if (editingStr.startsWith(':root') || editingStr.includes('--')) {
          // resultObj = parseCSSVariables(editingStr)
          // isCSS = true
          // }
          isCSS = true
        }
        //暂时不做，实际场景不需要
        // if (type == 2) {
        //   let normalObj = {}
        //   try {
        //     normalObj = JSON.parse(normalValue)
        //     for (const [key, value] of Object.entries(normalObj)) {
        //       if (!(key in resultObj)) {
        //         resultObj[key] = value
        //       }
        //     }
        //   } catch (parseError) {
        //     if (normalValue.startsWith(':root') || normalValue.includes('--')) {
        //       normalObj = parseCSSVariables(normalValue)
        //       console.log('@@@@@@resultStr normalObj', normalObj, normalValue)
        //       // editingStr= stringifyCSSVariables(editNewObj, editingStr)
        //     }
        //   }
        // }
        // console.log('@@@@@@resultStr editNewObj', editNewObj)
        if (!isCSS) {
          //json数据，只对匹配的key做替换，不然会破坏json数据
          if (resultObj && typeof resultObj === 'object' && !Array.isArray(resultObj)) {
            for (const [key, value] of Object.entries(editNewObj)) {
              //F2C数据只做替换操作，不新增不存在的键值对
              if (key in resultObj) {
                replacedObj[key] = {original: resultObj[key], target: value}
                resultObj[key] = value
              }
              // const resultStr = JSON.stringify(resultObj, null, 2)
              // console.log('@@@@@@resultStr', editingValue, resultObj)
            }
          }
        }
        const newText = isCSS
          ? stringifyCSSVariables(editNewObj, editingStr, replacedObj)
          : typeof resultObj === 'string'
            ? resultObj
            : JSON.stringify(resultObj, null, 2)

        setState(2)
        setIsCSS(isCSS)
        return {
          replacedObj,
          oldText,
          newText//: typeof resultObj === 'string' ? resultObj : JSON.stringify(resultObj),
        }
      }
      setState(2)
    } catch (error) {
      console.error('Error in getResultDiff:', error)
      setState(404)
    }
    return {oldText: '{}', newText: typeof editingValue === 'string' ? editingValue : JSON.stringify(editingValue), replacedObj: {}}
  }
  const getListItems = (obj: any) => {
    const list: any[] = []
    const allList: any[] = []

    replacedMap.forEach((value, key) => {
      list.push({key, original: value?.original, target: value?.target, name: value?.name})
      allList.push({key, original: value?.original, target: value?.target, name: value?.name})
    })
    obj && Object.entries(obj).forEach(([key, value]) => {
      allList.push({
        key,
        original: removeUrlStr(value?.original),
        target: removeUrlStr(value?.target),
        name: `${key}（${isCSS ? "样式文件" : "文本文件"}）`
      })
    })
    return {list, allList}
  }
  const getPreviewBox = (item: any) => {
    if (isImage(item?.original)) {
      return (
        <div style={{display: 'flex', gap: '16px', margin: '8px 0'}}>
          <div>
            <img
              src={item?.original}
              alt="原始图片"
              style={{maxWidth: '200px', maxHeight: '200px'}}
            />
          </div>
          <SwapOutlined />
          <div>
            <img
              src={item?.target}
              alt="新图片"
              style={{maxWidth: '200px', maxHeight: '200px'}}
            />
          </div>
        </div>
      )
    } else if (isColor(item?.original)) {
      return (
        <Space>
          <ColorPicker disabled defaultValue={item?.original} showText />
          <SwapOutlined />
          <ColorPicker disabled defaultValue={item?.target} showText />
        </Space>
      )
    }
    return (
      <>
        <span>原数据：{item?.original}</span>
        <SwapOutlined />
        <span style={{color: '#1677ff'}}>新数据：{item?.target}</span>
      </>
    )
  }

  useEffect(() => {
    const {oldText, newText, replacedObj} = getResultDiff()
    const {list, allList} = getListItems(replacedObj)
    setOldText(oldText)
    setNewText(newText)
    setListItems(list)
    setAllListItems(allList)
    item.modifyValue = newText
  }, [type, normalValue, newValue, editingValue])

  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: '替换内容预览',
      children: <>{allListItems?.length > 0 ? <List
        bordered
        size="small"
        dataSource={allListItems}
        renderItem={(item: any) => (
          <List.Item>
            <List.Item.Meta
              title={item?.name || item?.key}
              description={getPreviewBox(item)}
            />
          </List.Item>
        )}
      /> : <Result
        status="warning"
        title="无可视化的内容替换"
        subTitle="替换内容可能都是注释内容，可点击”详细配置信息“查看"
      />
      },</>
    },
    {
      key: '2',
      label: '详细配置信息',
      children: <Flex vertical gap="small">
        {listItems?.length > 0 ? <List
          bordered
          size="small"
          dataSource={listItems}
          renderItem={(item: any) => (
            <List.Item>
              <List.Item.Meta
                title={item?.name || item?.key}
                description={<>
                  <p style={{color: '#000000'}}>key：{item?.key}</p>
                  <p>原数据：{item?.original}</p>
                  <p style={{color: '#1677ff'}}>新数据：{item?.target}</p>
                </>}
              />
            </List.Item>
          )}
        /> : <></>}
        {newText ?
          <Flex gap="small">
            <Card title="新增内容" size="small" style={{width: "50%", overflow: "auto"}}>
              <Editor
                // options={{readOnly: true}}
                language={'json'}
                value={oldText}
                onMount={(editor: any) => {
                  editorRefL.current = editor
                  setTimeout(async () => await editorRefL.current?.getAction('editor.action.formatDocument').run(), 3000)
                }}
                width="100%"
                height="500px"
              />
              <div className={css.editorMask} />
              {/* <ReactDiffViewer
      oldValue={''}
      newValue={editNewText}
      splitView={false}
      showDiffOnly={true}
      hideLineNumbers={false}
      styles={{
        diffAdded: {backgroundColor: '#e6ffed'},
        diffRemoved: {backgroundColor: '#ffebe9'},
        diffContainer: {
          // minHeight: '500px',
          width: '100%',
          overflow: 'auto',
          fontSize: '14px',
        },
        line: {
          padding: '2px',
        },
      }}
    /> */}
            </Card>
            <Card title="修改后内容" size="small" style={{width: "50%", overflow: "auto"}}>
              <Editor
                language={isCSS ? 'scss' : 'json'}
                // options={{readOnly: true}}
                value={newText}
                onMount={(editor: any) => {
                  editorRefR.current = editor
                  setTimeout(async () => await editorRefR.current?.getAction('editor.action.formatDocument').run(), 3000)
                }}
                width="100%"
                height="500px"
              />
              <div className={css.editorMask} />
              {/* <ReactDiffViewer
      oldValue={''}
      newValue={newText}
      splitView={true}
      showDiffOnly={true}
      hideLineNumbers={false}
      styles={{
        diffAdded: {backgroundColor: '#e6ffed'},
        diffRemoved: {backgroundColor: '#ffebe9'},
        diffContainer: {
          // minHeight: '500px',
          width: '100%',
          overflow: 'auto',
          fontSize: '14px',
        },
        line: {
          padding: '2px',
        },
      }}
    /> */}
            </Card>

          </Flex>
          : <></>}
      </Flex>,
    },
  ]
  return (
    <>
      {state == 2 ? (
        <Collapse defaultActiveKey={['1']} ghost items={items} />
      ) : (state == 1 ? <Spin tip="Loading" size="large">
        <div className={css.loading} />
      </Spin> : <Result
        status="error"
        title="数据异常"
        subTitle="未知错误,请稍后重试"
      />)}
    </>
  )
}

export default DiffViewer
