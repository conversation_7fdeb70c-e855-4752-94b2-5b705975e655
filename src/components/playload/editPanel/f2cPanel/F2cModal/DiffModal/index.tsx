import {QuestionCircleOutlined} from '@astro/ui/icon'
import {Button, Divider, Flex, List, message, Modal, Popover, Radio, Select, Space, Tabs, Tag, type SelectProps} from '@astro/ui/ui'
import {useEffect, useMemo, useState} from 'react'
import {getDeepValue, mergeMaps, processDataWithReplacements, setDeepValue} from 'src/utils/f2c'
import f2cStore, {DiffComType} from '../../../f2cStore'
import pageStore from '../../../pageStore'
import DiffViewer from './DiffViewer'
import css from './index.module.scss'
type TagRender = SelectProps['tagRender']

const optionsRadio: Array<any> = [
  {label: '按当前配置替换', value: 1, desc: '按当前属性1比1替换'},
  {label: '补齐所有配置', value: 2, desc: '补齐组件缺省属性，并替换'},
]
const optionsRadioComType: Array<any> = [
  {label: '精准匹配', value: 2, desc: '严格按设计稿的组件或者configID匹配'},
  {label: '模糊匹配', value: 1, desc: '只对配置的内容匹配，有内容更改的都匹配'},
]
/**
 * ToDo:组件映射选择本地记忆，后续不用全部重选
 * 
 */
const selMap = new Map<string, any>()

const DiffModal = () => {
  const f2cState = f2cStore.state
  const [listItems, setListItems] = useState<any>([])
  const [btnType, setBtnType] = useState(1) // 1: 只有替换按钮 2: 下一步，没有替换按钮 3：上一步，替换按钮
  // const [tabs, setTabs] = useState(Array<any>())
  const [dataType, setDataType] = useState(optionsRadio[0].value)
  const [comType, setComType] = useState(optionsRadioComType[0].value)
  const [valueSelect, setValueSelect] = useState<any>([])

  const handleOk = async () => {
    console.log('handleOk', valueSelect)
    if (valueSelect.length > 0) {
      await f2cState.saveComF2CInfo(valueSelect)
      handleCancel()
    } else {
      message.warning('当前没有可换肤组件，请选择~~')
    }
  }
  const handleCancel = () => {
    setBtnType(1)
    setListItems([])
    f2cState.setShowDiffModal(false)
  }
  const getTabs = useMemo(
    () => {
      const tabs: any = []
      f2cState.diffComMap.forEach((item: DiffComType) => {
        // normalValue={item.normalValue} newValue={item.newValue} editingValue={item.editingValue}
        const type = f2cState.getComType(item.uniqueNum)
        if (comType != 2 || type == comType) {
          tabs.push({
            key: item.uniqueNum,
            label: item.name,
            forceRender: true,
            children: <DiffViewer type={dataType} item={item} />,
          })
        }
      })
      return tabs
    },
    [f2cState.diffComMap, dataType, comType],
  )
  const getSelOp = useMemo(
    () => {
      const op: any = []
      const sel: any = []
      f2cState.diffComMap.forEach((item: DiffComType) => {
        const type = f2cState.getComType(item.uniqueNum)
        // normalValue={item.normalValue} newValue={item.newValue} editingValue={item.editingValue}
        if (comType != 2 || type == comType) {
          op.push({
            value: item.uniqueNum,
            label: item.name,
          })
          sel.push(item.uniqueNum)
        }
      })
      setValueSelect(sel)
      return op
    },
    [f2cState.diffComMap, dataType, comType],
  )
  const nextHandler = () => {
    dealDiffComMap()
    setBtnType(3)
  }
  const getFooter = () => {
    let btnList = [
      // 实际使用少，暂不提供
      // <div className={css.selectWrap} key="DiffModal">
      //   <Radio.Group onChange={e => setDataType(e.target.value)} defaultValue={dataType}>
      //     {optionsRadio.map((item: any) => (
      //       <Radio.Button key={item.value} value={item.value} title={item.desc}>
      //         {item.label}
      //       </Radio.Button>
      //     ))}
      //   </Radio.Group>
      // </div>,
      <Button key="cancel" type="default" onClick={handleCancel}>
        取消
      </Button>
    ]
    if (btnType == 2) {
      btnList.unshift(
        <div className={css.selectcomTypeWrap} key="DiffModal">
          <Radio.Group onChange={e => setComType(e.target.value)} defaultValue={comType}>
            {optionsRadioComType.map((item: any) => (
              <Radio.Button key={item.value} value={item.value} title={item.desc}>
                {item.label}
              </Radio.Button>
            ))}
          </Radio.Group>
        </div>
      )
      btnList.push(
        <Button key="next" type="primary" onClick={nextHandler}>
          下一步
        </Button>,
      )
    } else {
      if (btnType == 3)
        btnList.push(
          <Button key="prev" type="primary" onClick={() => {setBtnType(2)}}>
            上一步
          </Button>,
        )
      btnList.push(
        <Button key="submit" type="primary" onClick={handleOk}>
          确认替换
        </Button>,
      )
    }
    return btnList
  }
  const getListItem = (item: any) => {
    const options: any = []
    const dataSource = item?.dataSource
    const uniqueNum = item?.uniqueNum
    const localKey = `${pageStore.projectId}%astro%${pageStore.pageKey}%astro%${uniqueNum}`
    dataSource?.forEach((item: any) => {
      const content = item?.editingValue ? JSON.stringify(item?.editingValue) : '无内容'

      options.push({
        value: `${uniqueNum}%astro%${item.key}`, label: <Space>
          {item.name}
          <Popover key={`tab_${item.uniqueNum}`} content={content} trigger="hover">
            <QuestionCircleOutlined style={{cursor: 'pointer'}} />
          </Popover>
        </Space>
      })
    })
    const localValue = localStorage.getItem(localKey)
    const defaultValue = localValue || selMap.get(uniqueNum) || options[0]?.value
    if (defaultValue != localValue) {
      localStorage.setItem(localKey, defaultValue)
    }
    if (selMap.get(uniqueNum) != defaultValue) {
      selMap.set(uniqueNum, defaultValue)
    }
    const setSelectedItems = (selectedItems: any) => {
      const value = typeof selectedItems == 'string' ? selectedItems : selectedItems?.target?.value || 0
      selMap.set(uniqueNum, value)
      localStorage.setItem(localKey, value)
    }
    return (
      <Flex>
        <span style={{display: 'flex', minWidth: 90}}>映射的属性：</span>
        {options?.length < 6 ? (
          <Flex>
            <Radio.Group
              key={uniqueNum} options={options} defaultValue={defaultValue} onChange={setSelectedItems} />
          </Flex>
        ) : (
          <Select
            key={uniqueNum}
            minWidth={150}
            defaultValue={defaultValue}
            onChange={setSelectedItems}
            style={{width: '100%'}}
            options={options}
          />
        )}
      </Flex>
    )
  }
  const filterDiffComMap = () => {
    //判断diffCom是否有AstroTypes.Group类型或者多个AstroTypes.Code|AstroTypes.Scss,有则需要对当前配置关联到对应的配置
    const map = f2cState.diffComMap
    const listItems: any[] = []
    let needSel = false //是否需要选择映射的属性，默认不需要，只有多个组件的情况下才需要选择
    map.forEach((item: DiffComType) => {
      const arr: any[] = []
      const form = item.form
      const uniqueNum = item.uniqueNum
      let selKey = ''
      Object.entries(form ?? {}).forEach(([key, formItem]: any) => {
        let itemKey = ''
        let value = ''
        if (formItem.type == 'AstroTypes.Code' || formItem.type == 'AstroTypes.Scss') {
          value = getDeepValue(item.dataConfig, key, '.')
          arr.push({key: key, name: formItem.name, editingValue: value})
          itemKey = `${uniqueNum}%astro%${key}`
          // console.log('@@@@@@component.dataConfig11 Code----hitKey', item, key, value)
        } else if (formItem.type == 'AstroTypes.Group') {
          value = getDeepValue(item.dataConfig, key, '.')
          //规范一个group下只有1个AstroTypes.Code | AstroTypes.Scss，否则嵌太深，用户操作很懵逼
          // console.log('@@@@@@component.dataConfig Code----hitKey', item, key, value)
          for (let i = 0; i < value?.length; i++) {
            arr.push({key: `${key}%astro%${i}`, name: `[${formItem.collapseName}] ${formItem.name}(第${i + 1}个)`, editingValue: value[i]})
          }
          // arr.push({key: key, name: `[${formItem.collapseName}] ${formItem.name}`, len: value?.length})
          itemKey = `${uniqueNum}%astro%${key}%astro%0`
        }
        if (!selKey) {
          selKey = itemKey
          selMap.set(uniqueNum, selKey)
        }
      })
      if (arr?.length > 1) {
        const type = f2cState.getComType(uniqueNum)
        const listItem: any = {title: item?.name, uniqueNum, type, dataSource: arr}
        listItems.push(listItem)
        needSel = true
      }
    })
    if (needSel) {
      // console.log('###listItems', listItems)
      setListItems(listItems)
      setBtnType(2)
    } else {
      dealDiffComMap()
    }
  }
  const dealDiffComMap = () => {
    const map = f2cState.diffComMap
    map.forEach((item: DiffComType) => {
      const form = item.form
      const unreplacedMap = item.newValue
      const replacedMap = item.replacedMap
      const dataConfig = item.dataConfig
      const uniqueNum = item.uniqueNum
      const targetSel = selMap.get(uniqueNum)
      const targetArr = targetSel?.split('%astro%') || []
      const targetKey = targetArr?.length > 1 ? targetArr[1] : ''
      let hitValue = ''
      let hitKey = ''
      if (targetKey && form?.[targetKey]) {
        const targetItem = form?.[targetKey]
        if (targetItem.type == 'AstroTypes.Group') {
          //group的默认值，再平台新建组件后，就找不到默认配置了，如果找不到按照第一个配置处理
          const id = targetArr?.length > 2 ? targetArr[2] : 0
          const groupItem = targetItem?.value?.[id] || targetItem?.value?.[0]
          //过滤属性是否有'AstroTypes.Code' |  'AstroTypes.Scss'类型
          Object.entries(groupItem ?? {}).forEach(([k, v]: any) => {
            if (!hitValue && (v.type == 'AstroTypes.Code' || v.type == 'AstroTypes.Scss')) {
              hitKey = targetSel?.replace(`${uniqueNum}%astro%`, '') + `%astro%${k}`
              hitValue = v.type == 'AstroTypes.Code' ? JSON.stringify(v?.value) : v?.value
              // console.log('@@@@@@component.dataConfig Code----hitKey', hitKey, v, k, hitValue)
            }
          })
        } else {
          hitKey = targetKey
          hitValue = targetItem.type == 'AstroTypes.Code' ? JSON.stringify(targetItem?.value) : targetItem?.value
        }
      }
      // 二次替换
      const itemCfgKey = targetSel?.replace(`${uniqueNum}%astro%`, '')
      const itemCfg = getDeepValue(dataConfig, itemCfgKey, '%astro%')
      const {target: tarCfg, replacedMap: rep, unreplacedMap: unrep} = processDataWithReplacements(itemCfg, unreplacedMap)//深度替换并返回未使用数据
      setDeepValue(dataConfig, itemCfgKey, tarCfg, '%astro%')
      mergeMaps(replacedMap, rep) // 合并map

      // console.log('@@@@@@component.dataConfig after----', form)
      /*Object.entries(form ?? {}).forEach(([key, item]: any) => {
        console.log('@@@@@@tagetItem----', item)
        //过滤属性是否有'AstroTypes.Code' |  'AstroTypes.Scss'类型
        if (!hitValue && (item.type == 'AstroTypes.Code' || item.type == 'AstroTypes.Scss')) {
          hitKey = key
          hitValue = item.type == 'AstroTypes.Code' ? JSON.stringify(item?.value) : item?.value
          // console.log('@@@@@@component.dataConfig Code----hitKey', item, key, hitValue)
        }
      })*/
      if ((hitKey && unreplacedMap?.size > 0) || replacedMap?.size > 0) {
        const edId = hitKey.replace(`${uniqueNum}%astro%`, '')
        f2cStore.replaceMapNameForForm(replacedMap, form)
        //重置组件的原始数据，便于后续对比
        item.normalValue = hitValue || ''
        item.newValue = unrep//unreplacedMap
        item.editingValue = getDeepValue(dataConfig, edId, '%astro%')//dataConfig?.[hitKey]
        item.replacedMap = replacedMap
        item.hitKey = hitKey
      }
    })
  }

  const tagRender: TagRender = (props: any) => {
    const {label, closable, onClose} = props
    const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
      event.preventDefault()
      event.stopPropagation()
    }
    return (
      <Tag
        color={'cyan'}
        onMouseDown={onPreventMouseDown}
        closable={closable}
        onClose={onClose}
        style={{marginInlineEnd: 4}}
      >
        {label}
      </Tag>
    )
  }
  useEffect(() => {
    filterDiffComMap()
  }, [f2cState.diffComMap])

  return (
    <Modal
      footer={getFooter()}
      title={btnType != 2 ? 'F2C属性替换' : '选择组件的替换的属性配置'}
      width={{xs: '90%', sm: '90%', md: '80%', lg: '80%', xl: '70%', xxl: '70%'} as any}
      open={f2cState.showDiffModal}
      onCancel={handleCancel}
      destroyOnHidden={true}
    >
      {btnType != 2 ?
        <Flex vertical>
          <Tabs
            type="card"
            items={getTabs}
          />
          <Divider orientation="left" style={{borderColor: '#1677ff', fontWeight: 'bold', margin: 0}}>选择替换的图层</Divider>
          <Flex justify={'flex-end'}>
            <Select
              mode="multiple"
              value={valueSelect}
              style={{width: '100%'}}
              onChange={setValueSelect}
              placeholder="选择需要上传的组件"
              options={getSelOp}
              tagRender={tagRender}
            />
          </Flex>
        </Flex>
        : <List
          bordered
          size="small"
          dataSource={listItems}
          renderItem={(item: any) => {
            return (comType != 2 || item.type == comType) ? <List.Item>
              <List.Item.Meta
                title={item?.title}
                description={getListItem(item)}
              />
            </List.Item> : <></>
          }}
        />}
    </Modal>
  )
}

export default DiffModal
