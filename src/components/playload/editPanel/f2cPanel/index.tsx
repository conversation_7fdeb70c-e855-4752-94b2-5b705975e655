import {CaretRightOutlined} from '@astro/ui/icon'
import {Collapse, Tooltip} from '@astro/ui/ui'
import F2CConfigIdSelect from 'src/components/common/F2CConfigIdSelect'
import pageStore from '../pageStore'
import F2cModal from './F2cModal'
import styles from './index.module.scss'

const CollapseExtra = () => {
  return (
    <Tooltip key={`SFToolTip-F2C-Version`} title={'F2C configId配置'}>
      <div className={styles.f2cIcon} />
    </Tooltip>
  )
}

const items = [
  {
    key: 'F2C_version_select',
    label: 'F2C组件换肤',
    children: <F2CConfigIdSelect />,
    extra: <CollapseExtra />,
    forceRender: true//需要折叠时候也能促发组件F2C功能（前端君）
  },
]

const F2CPanel = () => {
  const pageState = pageStore.state
  return (
    <>
      {pageState.page?.path || pageState.componentKey >= 0 ? (
        <Collapse
          style={{
            textAlign: 'left',
            background: '#FFF',
            borderBottom: '1px solid #d9d9d9',
            borderRadius: '0px',
          }}
          expandIconPosition={'end'}
          items={items}
          bordered={false}
          expandIcon={({isActive}) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
        />
      ) : (
        <></>
      )}
      <F2cModal />
    </>
  )
}

export default F2CPanel
