import {Avatar, Button, Flex, Tag, Tooltip} from '@astro/ui/ui'
import dayjs from 'dayjs'
import React, {useState, useEffect} from 'react'
import {versionLockLogs} from 'src/services/astro'
import pageStore from '../editPanel/pageStore'
import type {Entity} from './type'

const LockVersionListModal = () => {
  const pageState = pageStore.state
  const [logs, setLogs] = useState<Entity[]>([])

  useEffect(() => {
    ;(async () => {
      const res = await versionLockLogs({projectId: pageState.projectId})
      if (res && res.data.result === 200) {
        setLogs(res.data.data)
      }
    })()
  }, [])

  const renderLogsContent = () => {
    if (!logs.length) {
      return <div style={{color: '#666'}}>无锁定记录</div>
    }
    return (
      <Flex vertical style={{padding: '8px', maxHeight: '300px', overflowY: 'auto', color: '#666'}}>
        {logs.map((log, index) => (
          <Flex
            key={index}
            vertical
            gap={4}
            style={{
              padding: '18px 8px',
              borderBottom: index < logs.length - 1 ? '1px solid #f0f0f0' : 'none',
            }}
          >
            <Flex gap={4}>
              <Tag color="blue">版本ID: {log.versionId}</Tag>
              <span> {dayjs(log.createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
            </Flex>

            <Flex gap={4} align="flex-start">
              <span>操作用户：</span>
              <Avatar size="small" src={log.operatorAvatar} />
              <span>{log.operatorPassport}</span>
            </Flex>
          </Flex>
        ))}
      </Flex>
    )
  }

  return (
    <Tooltip
      title={renderLogsContent()}
      placement="right"
      color="white"
      overlayStyle={{
        maxWidth: '400px',
      }}
    >
      <Button>锁定记录</Button>
    </Tooltip>
  )
}

export default LockVersionListModal
