import {Button, Flex, Form, Input, Modal, Radio, Select, Table, message} from '@astro/ui/ui'
import dayjs from 'dayjs'
import {memo, useEffect, useRef, useState} from 'react'
import type React from 'react'
import userStore from 'src/components/common/user/userStore'
import pageStore from 'src/components/playload/editPanel/pageStore'
import config from 'src/config'
import {getApplyViewAuth, getPermissions, submitPermissions} from 'src/services/adminWeb'
import type {AuthTreeNodeVo} from './type'

const durationList: {value: number; label: string}[] = [
  {
    label: '1天',
    value: 1,
  },
  {
    label: '1周',
    value: 7,
  },
  {
    label: '1个月',
    value: 30,
  },
  {
    label: '3个月',
    value: 90,
  },
  {
    label: '半年',
    value: 180,
  },
  {
    label: '1年',
    value: 365,
  },
]

export const ApplyPermissionModal = (props: any) => {
  const {isModalOpen, setIsModalOpen} = props
  const [applyViewAuth, setApplyViewAuth] = useState(false)
  const [permissionsList, setPermissionsList] = useState<AuthTreeNodeVo[]>([])
  const [applyLoading, setApplyLoading] = useState(false)
  const [viewAuthLoading, setViewAuthLoading] = useState(false)

  const [form]: any = Form.useForm()
  const formRef = useRef<any>()
  const userState = userStore.state

  const queryApplyViewAuth = async () => {
    setViewAuthLoading(true)
    const {data: res} = await getApplyViewAuth()
    setViewAuthLoading(false)
    if (res && res.status === 0) {
      setApplyViewAuth(true)
      queryPermissionsList()
    } else {
      message.error(res?.msg || '请用工号申请权限')
    }
  }

  const queryPermissionsList = async () => {
    const uid = userState?.user?.uid

    if (!uid) {
      return
    }
    const {data: res} = await getPermissions(uid)
    if (res && res.result === 0) {
      setPermissionsList(res?.data)
      setApplyViewAuth(true)
    } else if (res.result === 400) {
      setApplyViewAuth(false)
    }
  }

  const getNodeType = (id: number) => {
    for (const permission of permissionsList) {
      const info: AuthTreeNodeVo = permission?.children?.[0] ?? {}
      if (info.id === id) {
        return info.nodeType
      }
    }
  }

  useEffect(() => {
    queryPermissionsList()
  }, [userState?.user?.uid])

  const handleCancel = () => {
    setIsModalOpen(false)
  }
  const onFinish = async () => {
    formRef.current
      .validateFields()
      .then(async (values: any) => {
        const params = {
          duration: values?.duration,
          reason: values?.reason,
          pitems: [
            {
              id: values?.id,
              type: getNodeType(values?.id),
            },
          ],
        }
        setApplyLoading(true)
        const {data: res} = await submitPermissions(params)
        setApplyLoading(false)
        if (res && res.result === 0) {
          message.success('申请成功')
          setIsModalOpen(false)
        } else {
          message.error(res?.msg ?? '设置失败')
        }
      })
      .catch((error: any) => {
        console.log('###error', error)
      })
  }

  const RenderForm = memo(() => {
    return (
      <Form
        labelCol={{span: 4}}
        wrapperCol={{span: 14}}
        layout="horizontal"
        style={{maxWidth: 600}}
        form={form}
        ref={formRef}
      >
        <Form.Item name="id" label="选择角色" rules={[{required: true}]}>
          <Radio.Group>
            {permissionsList.map((item: AuthTreeNodeVo) => {
              const id = item?.children?.[0]?.id
              return (
                <Radio value={id} key={id}>
                  {item.name}
                </Radio>
              )
            })}
          </Radio.Group>
        </Form.Item>

        <Form.Item name="reason" label="申请原因" rules={[{required: true}]}>
          <Input />
        </Form.Item>

        <Form.Item name="duration" label="选择有效期" rules={[{required: true}]}>
          <Select options={durationList} />
        </Form.Item>
      </Form>
    )
  })

  return (
    <>
      <Modal
        title="申请权限"
        width={600}
        open={isModalOpen}
        onCancel={handleCancel}
        destroyOnClose={true}
        footer={
          applyViewAuth ? (
            <>
              <Button key="cancel" onClick={handleCancel}>
                取消
              </Button>
              <Button key="ok" type="primary" onClick={onFinish} loading={applyLoading}>
                提交申请
              </Button>
            </>
          ) : null
        }
      >
        {!applyViewAuth ? (
          <Flex justify={'center'} align={'center'}>
            <p>当前无权限查看，是否申请？</p>
            <Button onClick={queryApplyViewAuth} type="primary" loading={viewAuthLoading}>
              申请查看
            </Button>
          </Flex>
        ) : (
          <>
            <p>
              {config.isTest
                ? '测试环境申请权限，点击角色并提交，1分钟内自动通过，申请失败请如流联系李伟宙。'
                : '正式环境申请将提交给上级审批，审批结果将会下发如流通知，申请失败请如流联系李伟宙。'}
            </p>
            <RenderForm />
          </>
        )}
      </Modal>
    </>
  )
}
