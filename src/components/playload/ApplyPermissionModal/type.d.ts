export type AuthTreeNodeVo = {
  nodeId?: string // 节点id（唯一）
  nodeType?: NodeType // 节点类型 1-菜单，2-权限
  id?: number // 菜单/权限id
  name?: string // 菜单/权限名
  accessKey?: string // 接入key
  url?: string // url(path)，"http(s)://"或者“/”开头
  icon?: string // 图标
  children?: AuthTreeNodeVo[] // 子节点列表
  orderNo?: number // 排序
}

// 示例
// AuthTreeNodeVo = {
//   nodeId: "m11325",
//   nodeType: 1,
//   id: 11325,
//   name: "角色",
//   accessKey: "astro",
//   children: [
//       {
//           nodeId: "p1334",
//           nodeType: 2,
//           id: 1334,
//           name: "【权限】技术",
//           accessKey: "astro"
//       },
//       {
//           nodeId: "p1335",
//           nodeType: 2,
//           id: 1335,
//           name: "【权限】运营",
//           accessKey: "astro"
//       }
//   ]
// };
