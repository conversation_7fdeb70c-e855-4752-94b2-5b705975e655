import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
import {Avatar, Popover, Space, Tag} from '@astro/ui/ui'
import dayjs from 'dayjs'
import userStore from 'src/components/common/user/userStore'
import './index.scss'
import type {RoleType} from 'src/config/types'
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')
export const RealTimeEditor = () => {
  const userState = userStore.state
  //   console.log('userState.editorUser', userStore.clone(userState.editorUser), userState.editorUser.uid)
  const {name, color} = getRoleName(userState.editorUser.role)
  return userState.editorUser.uid > 0 ? (
    <Popover
      rootClassName="editor-user-popover"
      placement="bottom"
      title={''}
      content={() => {
        return (
          <Space>
            <Avatar size="large" src={userState.editorUser.avatar} />
            <div>
              <Space>
                <span>{userState.editorUser.nickname || userState.editorUser.userName}</span>
                <Tag className="editor-user-tag" bordered={false} color={color}>
                  {name}
                </Tag>
              </Space>
              <br />
              最后更新: {dayjs(userState.editorUser.createTime).fromNow()}
            </div>
          </Space>
        )
      }}
    >
      <Avatar className="editor-user-avatar" src={userState.editorUser.avatar} />
    </Popover>
  ) : null
}
// //角色 T 技术人员 C普通人员 P 产品人员 A管理员
const getRoleName = (o: RoleType) => {
  switch (o) {
    case 'A':
      return {name: 'Admin', color: 'purple'}
    case 'T':
      return {name: 'Technical', color: 'geekblue'}
    case 'C':
      return {name: 'Client', color: 'blue'}
    case 'P':
      return {name: 'Product', color: 'cyan'}
    default:
      return {name: 'Unknown', color: 'green'}
  }
}
