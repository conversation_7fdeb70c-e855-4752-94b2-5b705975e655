import {Button, Flex, Modal, Table, type TableColumnsType, type TableProps, message} from '@astro/ui/ui'
import dayjs from 'dayjs'
import {memo, useEffect, useRef, useState} from 'react'
import type React from 'react'
import pageStore from 'src/components/playload/editPanel/pageStore'
import config from 'src/config'
import LockVersionListModal from '../LockVersionListModal'
import styles from './index.module.scss'
import type {ProjectVersionInfo} from './type'

export const RenderVersinListTable = (props: any) => {
  const pageState = pageStore.state
  const versionList: ProjectVersionInfo[] = props.versionList
  const setSelectedRow = props.setSelectedRow
  const tableRef = useRef()

  const columns: TableColumnsType<ProjectVersionInfo> = [
    {
      title: '版本ID',
      dataIndex: 'versionId',
      width: 80,
      align: 'center',
    },
    {
      title: '发布时间',
      dataIndex: 'publishTime',
      align: 'center',
      width: 120,
      render: (publishTime: Date) => dayjs(publishTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    {title: '发布者', dataIndex: 'publishPassport', width: 200, align: 'center'},
    {
      title: '备注',
      dataIndex: 'publishRemark',
      align: 'center',
      render: (mark: string) => mark ?? '无',
    },
    {
      title: '预览',
      dataIndex: 'publishRemark',
      align: 'center',
      render: (_: any, record: any) => {
        console.log('###record', record)
        return (
          <a target="_blank" href={`${record?.previewUrl}`} rel="noreferrer">
            查看
          </a>
        )
      },
    },
  ]

  const getRowClassName = (record: ProjectVersionInfo) => {
    return record.versionId === pageState.curLockVersion ? styles['current-version-row'] : ''
  }

  return (
    <Table<ProjectVersionInfo>
      className={styles.table}
      ref={tableRef}
      rowSelection={{
        type: 'radio',
        onChange: (selectedRowKeys: React.Key[], selectedRows: ProjectVersionInfo[]) => {
          console.log(`selectedRowKeys: ${selectedRowKeys}`)
          setSelectedRow?.(selectedRowKeys)
        },
        getCheckboxProps: (record: ProjectVersionInfo) => ({
          disabled: record.versionId === pageState.curLockVersion, // Column configuration not to be checked
          name: String(record.versionId),
        }),
      }}
      columns={columns}
      dataSource={versionList}
      rowKey="versionId"
      pagination={false}
      rowClassName={getRowClassName}
      scroll={{y: 290}} // table最大高度
    />
  )
}

export const VersionModal = (props: any) => {
  const {isModalOpen, setIsModalOpen} = props
  const selectedRowKeysRef = useRef<any>()
  const pageState = pageStore.state

  const setSelectedRow = (row: any) => {
    // 设置选择的版本行
    selectedRowKeysRef.current = row
  }

  const handleOk = async (versionId?: number) => {
    const {data: res} = await pageState.lockProjectVersion(versionId ?? selectedRowKeysRef.current?.[0] ?? 0)
    if (res && res.result === 200) {
      message.success('设置成功')
      setIsModalOpen(false, true)
    } else {
      message.error(res?.reason ?? '设置失败')
    }
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }

  return (
    <>
      <Modal
        title="设置默认访问版本"
        width={650}
        open={isModalOpen}
        onOk={() => {
          handleOk()
        }}
        onCancel={handleCancel}
        destroyOnClose={true}
        footer={
          pageState.curLockVersion > 0
            ? [
                <LockVersionListModal key="LockVersionListModal" />,
                <Button
                  key="useNew"
                  type="primary"
                  title="设置默认访问的项目版本自动指向最新发布的版本"
                  onClick={() => {
                    handleOk(0)
                  }}
                >
                  默认使用最新版本
                </Button>,
                <Button
                  key="ok"
                  type="primary"
                  onClick={() => {
                    handleOk()
                  }}
                >
                  确定
                </Button>,
              ]
            : [
                <LockVersionListModal key="LockVersionListModal" />,
                <Button
                  key="ok"
                  type="primary"
                  onClick={() => {
                    handleOk()
                  }}
                >
                  确定
                </Button>,
              ]
        }
      >
        当前对外访问版本：
        <span className={styles.tips}>
          {Number(pageState.curLockVersion) > 0
            ? `锁定版本（${pageState.curLockVersion}）`
            : `默认最新版本（${pageState.versionList?.[0]?.versionId || '无发布版本'}）`}
        </span>
        <RenderVersinListTable versionList={pageState.versionList} setSelectedRow={setSelectedRow} />
      </Modal>
    </>
  )
}
