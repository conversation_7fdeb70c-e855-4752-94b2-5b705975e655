import {ControlOutlined} from '@astro/ui/icon'
import {Button, Select, Space, Tooltip} from '@astro/ui/ui'
import {useEffect, useState} from 'react'
import type {IEnvInfo} from 'src/config/project'
import EnvInfoForm from '../EnvInfoForm'
import pageStore from '../editPanel/pageStore'
import css from './layout.module.scss'

const STORAGE_KEY = 'astroEnv'
const optionsDefault: any = [
  {label: '正式', value: '0'},
  {label: '测试', value: '1'},
]
export const EnvControl = () => {
  const pageState = pageStore.state
  const envInfo = pageStore.clone(pageState.projectInfo?.projectConfig?.envInfo) as IEnvInfo
  const hasEnv = pageState.templateControl.hasEnv
  const [, forceUpdate] = useState({})
  const store = pageStore.state
  const [env, setEnv] = useState<string>('')
  function changeState(value: string) {
    window.localStorage.setItem(STORAGE_KEY, value)
    store.setTemplateEnv(value)
    setEnv(value)
  }
  const onChange = (value: string) => {
    changeState(value)
  }
  const getOptions = () => {
    if (envInfo && envInfo?.envValueList) {
      const _op = []
      for (const item of envInfo.envValueList) {
        _op.push({label: item.desc, value: item.value})
      }
      return _op
    }
    return optionsDefault
  }
  const options = getOptions()
  useEffect(() => {
    const locationState = window.localStorage.getItem(STORAGE_KEY) || ''
    let state = locationState
    let flg = false
    for (const item of options) {
      if (item.value === locationState) {
        flg = true
        break
      }
    }
    if (!flg) {
      state = options?.length > 0 ? options[0].value : ''
    }
    changeState(state)
    //store.pageKey 有时序问题
  }, [store.pageKey, options])

  useEffect(() => {
    forceUpdate({})
  }, [pageState.projectInfo?.projectConfig?.urlParams])

  return hasEnv ? (
    <Space align="baseline">
      <Space.Compact style={{width: '100%', height: '32px'}}>
        <EnvInfoForm
          key="setting_env"
          info={envInfo}
          refresh={false}
          trigger={
            <Tooltip title={'环境切换，"点击"可以自定义环境参数'}>
              {/* <Button type="link" className="templateName">
                接口环境
              </Button> */}
              <Button size="middle" className={css.templateName}>
                <ControlOutlined />
                {/* <DatabaseOutlined />
              <SwitcherOutlined /> */}
              </Button>
            </Tooltip>
          }
        />
        {/* {options?.length < 3 ? (
        <Segmented options={options} value={env} onChange={onChange} />
      ) : (
        <Select style={{width: '80px', flexShrink: 0}} value={env} options={options} onChange={onChange} />
      )} */}
        <Select
          className={css.selectEnv}
          popupClassName={css.selectPopup}
          value={env}
          options={options}
          onChange={onChange}
        />
      </Space.Compact>
    </Space>
  ) : (
    <></>
  )
}
