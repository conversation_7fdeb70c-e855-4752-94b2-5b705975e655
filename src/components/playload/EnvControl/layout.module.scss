.templateName {
  display: flex;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  font-size: 14px;
  width: 30px;
  color: rgba(0, 0, 0, 0.88)
}

.selectEnv {
  width: 50px; //65px;
  flex-shrink: 0;

  :global {

    .ant-select-selector {
      padding: 0 5px !important;

    }

    .ant-select-selection-item,
    .ant-select-selection-placeholder {
      font-size: 10px;
    }
  }
}

.selectPopup {
  font-size: 10px;
  padding: 2px;

  [class^="ant-select"],
  [class*=" ant-select"] {
    font-size: 10px;
    line-height: 18px;
  }
}