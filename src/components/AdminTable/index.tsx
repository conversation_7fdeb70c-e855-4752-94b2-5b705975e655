import TableManage from '@astro/adminwebmaster/tableManage'
import {useEffect, useState} from 'react'
import {useNavigate} from 'react-router-dom'
import {lxWhitelist} from 'src/utils/user'
import {AstroPageContainer} from '../common'
import userStore from '../common/user/userStore'
const AdminTable = () => {
  const navigate = useNavigate()
  const userState = userStore.state
  const [visible, setVisible] = useState(false)
  useEffect(() => {
    if (!userState.isLogin || (!userStore.userLoading && !lxWhitelist.includes(userState.user.uid))) {
      navigate('/', {replace: true})
    } else {
      setVisible(true)
    }
  }, [userState.isLogin, userStore.userLoading, userState.user])

  if (visible) {
    return (
      <AstroPageContainer>
        <TableManage pageFrom={'Astro'} />
      </AstroPageContainer>
    )
  }
  return null
}

export default AdminTable
