import {Http} from '@astro/utils'

const getApiHost = (isProd: boolean) => {
  return isProd ? 'https://lx.yy.com' : 'https://lx-test.yy.com'
}

export const fetch = new Http({
  // base: apiHost,
  credentials: 'include',
})

fetch.fetchRequest = config => {
  config.headers['X-Fts-Referer'] = window.location.href
  return config
}

export interface ApiResponse<T> {
  code: number
  msg: string
  serverTime: number
  traceId: string
  data: T
}

export interface TableInfo {
  tableId: string
  tableKey: string
  tableName: string
  tableType: string
  dsKey: string
  createUid: number
  createTime: string
  updateUid: number
  updateTime: string
}

export type TableMode = 'Table' | 'Form' | 'Chart'

/**
 * 获取 table 配置列表
 * @param params
 * @param isProd // 是否为生产环境
 */
export function getTableList(
  params: {
    pageNum: number
    pageSize: number
    serverKey: string
    search?: string
    tableMode?: TableMode
  } = {
    pageSize: 10,
    pageNum: 1,
    serverKey: '',
    search: '',
    tableMode: 'Table',
  },
  isProd = false,
): Promise<
  ApiResponse<{
    list: TableInfo[]
    total: number
  }>
> {
  return fetch.get(getApiHost(isProd) + `/api/admin/table/search`, params)
}

export interface ServerInfo {
  serverKey: string
  serverName: string
  env: string
}

/**
 * 获取数据配置信息
 * @param isProd // 是否为生产环境
 */
export function getServerList(isProd = false): Promise<ApiResponse<ServerInfo[]>> {
  return fetch.get(getApiHost(isProd) + '/api/admin/listServerInfo?all=true')
}

/**
 * 查询权限
 */
export function checkServerAuth(
  params: {serverKey: string} = {serverKey: ''},
  isProd = false,
): Promise<ApiResponse<{tableAuth: boolean}>> {
  return fetch.get(getApiHost(isProd) + '/api/admin/checkServerAuth', params)
}

export function ApplyServerAuth(serverKey: string, isProd = false) {
  return isProd ? `https://lx.yy.com/${serverKey}` : `https://lx-test.yy.com/${serverKey}`
}
