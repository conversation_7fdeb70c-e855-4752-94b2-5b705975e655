import {proxy, useSnapshot} from 'valtio'
import {deepClone, devtools} from 'valtio/utils'
function bind<T extends object>(instance: T): T {
  const obj = instance as any
  const names = Object.getOwnPropertyNames(Object.getPrototypeOf(obj))

  for (const name of names) {
    const method = obj[name]
    if (name === 'constructor' || typeof method !== 'function') continue
    obj[name] = (...args: unknown[]) => method.apply(instance, args)
  }

  return instance
}
// 优化后的用法
export class BaseStore {
  constructor() {
    // console.log('this ValtioProxy', this, this.constructor.name)

    // const state = bind(proxy(this))
    const state = bind(proxy(deepClone(this)))
    devtools(state, {name: this.constructor.name, enabled: true})
    // biome-ignore lint/correctness/noConstructorReturn: <explanation>
    return state
  }
  get state() {
    return useSnapshot(this) as typeof this
  }
  clone(v: any) {
    return deepClone(v)
  }
}
