import {message} from '@astro/ui/ui'
import {BosClient} from '@baiducloud/sdk'
import dayjs from 'dayjs'
import {getUploadToken} from 'src/services/common'

export interface UploadFile {
  name?: string // 文件名
  size: number // 文件大小（字节）
  type: string // 文件类型（MIME 类型）
  lastModified: number // 文件最后修改时间（时间戳）
  file: File // 原生 File 对象
}
export interface IUploadData {
  code: number
  data: {
    url: string
    progress: number | string
  }
  msg?: string
}

export interface IUploadParams {
  size?: number
  type?: string[]
}

const getBosconfig = async (key: string) => {
  const res: any = await getUploadToken(key)
  console.log('getBosconfig', res)
  if (res.code === 0) {
    const newBosconfig = {
      endpoint: 'https://' + res.data.endPoint,
      credentials: {
        ak: res.data.ak, //您的AccessKey
        sk: res.data.sk, //您的SecretAccessKey
      },
      sessionToken: res.data.token,
    }
    console.log('newBosconfig', newBosconfig)
    return {
      bosConfig: newBosconfig,
      config: res.data,
    }
  } else {
    return null
  }
}

const defaultCbRes = {
  code: -1,
  data: {
    url: '',
    progress: 0,
  },
  msg: '',
}

// 不同code含义 0：上传成功，1：上传中  -1： 上传失败 -2：大小超出限制 -3:文件类型错误
export const UploadRequest = async (file: UploadFile, params?: IUploadParams, ev?: any): Promise<IUploadData> => {
  //通过sts方式访问bos: https://cloud.baidu.com/doc/BOS/s/Hjwvyri9y#%E9%80%9A%E8%BF%87sts%E6%96%B9%E5%BC%8F%E8%AE%BF%E9%97%AEbos
  // console.log('file', file)
  if (params?.size && file.size > params.size) {
    //文件限制
    return {...defaultCbRes, code: -2, msg: '大小超出限制'}
  }
  if (params?.type && !params?.type.includes(file.type)) {
    //类型不一致
    return {...defaultCbRes, code: -2, msg: '大小超出限制'}
  }

  let fileType = file.type.split('/')[1] || file.name?.split('.').pop()
  const key = dayjs().format('YYYYMMDDH') + Math.random().toString().split('.')[1] + '.' + fileType // 保存到bos时的key，您可更改，默认以文件名作为key
  const config = await getBosconfig(key)
  if (!config) {
    return {...defaultCbRes, code: -3, msg: '获取bos配置异常, 请稍后重试'}
  }
  const client: any = new BosClient(config.bosConfig)
  console.log('config 11', config.config)
  //前期准备
  // const fileName = file.name.split('.')[0]
  if (fileType === 'svg+xml') {
    //svg的文件上传的时候，fileType为svg+xml，需要单独特殊处理下
    fileType = `svg`
  }
  // const dir = '/static/astro/'
  // const keyPrefix = fileName + '_'

  const url = `https://${config.config.cdnDomain}/${config.config.fileName.replace('static/', '')}`
  console.log('config url', url)
  let contentType = file.type
  if (fileType === 'woff2' || fileType === 'otf') {
    //woff2的字体文件上传的时候，file.type为空，需要单独特殊处理下
    contentType = `font/${fileType}`
  }
  return new Promise((resolve, reject) => {
    client
      .putObjectFromBlob(config.config.bucket, config.config.fileName, file, {'Content-Type': contentType})
      .then(function (res: any) {
        console.log('上传完成', url)
        resolve({
          code: 0,
          data: {
            url: url,
            progress: 100,
          },
        })
      })
      .catch(async function (err: any) {
        // 上传失败，添加您的代码
        if (err.status_code === 400) {
          return await UploadRequest(file, params)
        } else {
          console.log('err', err)
          resolve({...defaultCbRes, code: -1, msg: err || '上传失败'})
        }
      })
    client.on('progress', function (evt: any) {
      const percent = (evt.loaded / evt.total) * 100
      // console.log('BetaSchemaForm progress', evt, percent)
      ev?.onProgress({percent})
      // 监听上传进度
      if (evt.lengthComputable) {
        // 添加您的代码
        const percentage = (evt.loaded / evt.total) * 100
      }
    })
  })
}

// 不同code含义 0：上传成功，1：上传中  -1： 上传失败 -2：大小超出限制 -3:文件类型错误
export const UploadBlob = async (file: Blob, mimeType: string, ev?: any): Promise<IUploadData> => {
  //通过sts方式访问bos: https://cloud.baidu.com/doc/BOS/s/Hjwvyri9y#%E9%80%9A%E8%BF%87sts%E6%96%B9%E5%BC%8F%E8%AE%BF%E9%97%AEbos
  // console.log('file', file)
  let fileType = mimeType.split('/')[1]
  const key = dayjs().format('YYYYMMDDH') + Math.random().toString().split('.')[1] + '.' + fileType // 保存到bos时的key，您可更改，默认以文件名作为key
  const config = await getBosconfig(key)
  if (!config) {
    return {...defaultCbRes, code: -3, msg: '获取bos配置异常, 请稍后重试'}
  }
  const client: any = new BosClient(config.bosConfig)
  console.log('config 11', config.config)
  //前期准备
  // const fileName = file.name.split('.')[0]
  if (fileType === 'svg+xml') {
    //svg的文件上传的时候，fileType为svg+xml，需要单独特殊处理下
    fileType = `svg`
  }
  // const dir = '/static/astro/'
  // const keyPrefix = fileName + '_'

  const url = `https://${config.config.cdnDomain}/${config.config.fileName.replace('static/', '')}`
  console.log('config url', url)
  let contentType = file.type
  if (fileType === 'woff2' || fileType === 'otf') {
    //woff2的字体文件上传的时候，file.type为空，需要单独特殊处理下
    contentType = `font/${fileType}`
  }
  return new Promise((resolve, reject) => {
    client
      .putObjectFromBlob(config.config.bucket, config.config.fileName, file, {'Content-Type': contentType})
      .then(function (res: any) {
        console.log('上传完成', url)
        resolve({
          code: 0,
          data: {
            url: url,
            progress: 100,
          },
        })
      })
      .catch(async function (err: any) {
        // 上传失败，添加您的代码
        if (err.status_code === 400) {
          return await UploadBlob(file, fileType)
        } else {
          console.log('err', err)
          resolve({...defaultCbRes, code: -1, msg: err || '上传失败'})
        }
      })
  })
}

export default UploadRequest
