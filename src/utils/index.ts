import colorName from 'color-name'
// import debounce from 'lodash.debounce'
import type {Component} from 'src/components/playload/editPanel/types'
import {getF2CConfig} from 'src/services/common'

export const getParam = function (key: string) {
  const r = new RegExp('(\\?|#|&)' + key + '=([^&#]*)(&|#|$)')
  const m = location.href.match(r)
  return decodeURI(!m ? '' : m[2])
}

// 兼容物料库组件录入的数据结构转换，后面预计换新接口
export const transformComponent = (component: any): Component => {
  const extJson = JSON.parse(component?.extJson ?? '{}')

  const obj: Component = {
    name: component.materialName,
    componentType: component.componentType,
    id: component.materialId,
    img: component.materialUrl,
    url: extJson?.materialUrl,
    path: extJson?.path,
    scope: extJson?.scope,
    slotKey: null,
    dataConfig: null,
    project: component.project,
    tagList: component.tagList,
    hasSlot: component.hasSlot,
  }

  return obj
}

/**
 * 复制到剪切板
 * @param value
 */
export const copy = async (value: string) => {
  await navigator.clipboard.writeText(value)
}

export function deepAssign<T>(target: any, source: any, isFullOverwrite = false): T {
  if (!source) return target

  for (const k in source) {
    const vs = source[k]
    const vt = target[k]

    if (Object(vs) == vs && Object(vt) === vt && !isFullOverwrite) {
      // 只在非完全覆盖模式下进行深度合并
      target[k] = deepAssign(vt, vs, isFullOverwrite)
      continue
    }

    // 完全覆盖模式下直接赋值，或非完全覆盖模式下值不为 undefined 时赋值
    target[k] = vs
  }
  return target
}

export const deepMergeWithArrayMerge = (obj1: any, obj2: any) => {
  const result: any = {}
  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)

  // 合并两个对象的键
  const allKeys = new Set([...keys1, ...keys2])

  allKeys.forEach(key => {
    const value1 = obj1[key]
    const value2 = obj2[key]

    if (Array.isArray(value1) && Array.isArray(value2)) {
      // 如果两个属性都是数组，合并相同下标的元素
      result[key] = value1.map((item, index) => ({
        ...item,
        ...value2[index],
      }))
    } else if (typeof value1 === 'object' && typeof value2 === 'object') {
      // 如果两个属性都是对象，递归合并
      result[key] = deepMergeWithArrayMerge(value1, value2)
    } else {
      // 其他情况，直接覆盖或赋值
      result[key] = value2 !== undefined ? value2 : value1
    }
  })

  return result
}

// 根据configId获取F2C配置信息中的html文件内容
export async function getF2CHTMLContent(configId: number) {
  let returnValue = String(configId)
  const f2cRes = await getF2CConfig(configId)
  if (f2cRes.data && f2cRes.data.code === 0 && f2cRes.data.data !== undefined && f2cRes.data.data) {
    const [target] = f2cRes.data.data
    const {value} = target.lastVersion
    const format = JSON.parse(value)
    const htmlUrl = format?.html?.['/index.html']
    if (htmlUrl) {
      const fileResponse = await fetch(htmlUrl)
      if (fileResponse.ok) {
        returnValue = await fileResponse.text()
      }
    }
  }
  return returnValue
}

export const JSONParse = (v: string) => {
  let rs = {}
  try {
    rs = JSON.parse(v ?? {})
  } catch (e) {}
  return rs
}

export const pkgNameParse = (str: string) => {
  // const str = "https://unpkg.yy.com/@astro-ui/barrage@0.72.0/dist/emp.js";
  const regex = /@([^/]+)\/([^@]+)/
  const match = str.match(regex)
  if (match && match[0]) {
    // console.log(match)
    return match[0]
  } else {
    return ''
  }
}

export const getPkgVersionByUrl = (str: string) => {
  const ml = str.match(/@(\d+\.\d+\.\d+(?:-[^/]+)?)/)
  if (ml?.[1]) {
    return ml?.[1]
  } else {
    return '0.0.0'
  }
}
export const wordToHex = (word: any) => {
  const rgbArr = colorName?.[word]
  return rgbArr ? `rgb(${rgbArr.join(',')})` : undefined
}

// 添加search参数到url链接末
export const appendSearchParamToUrl = (url = '', search = ''): string => {
  if (!url || !search) {
    return url
  }

  const urlObj = new URL(url)
  const searchParams = new URLSearchParams(urlObj.search)

  const searchWithoutQuestionMark = search.startsWith('?') ? search.substring(1) : search

  searchWithoutQuestionMark.split('&').forEach(param => {
    const [key, value] = param.split('=')
    searchParams.set(key, decodeURIComponent(value))
  })

  urlObj.search = searchParams.toString()
  return urlObj.toString()
}

// 给链接添加参数，识别?和&
export const appendLinkParam = (link: string, key: string, value: string): string => {
  const url = new URL(link, window.location.origin)
  url.searchParams.set(key, value)
  return decodeURIComponent(url.toString())
}

export const keepChineseEnglishNumbers = (str: string) => {
  // 只保留中文字符、英文字符和数字
  return str.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '')
}

export const isValidUrl = (url: string): boolean => {
  const pattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,6}(\:[0-9]+)?(\/[^\s]*)?$/
  return pattern.test(url)
} //去测试帮助文档的地址是否为合法地址（https协议）
