import type {Pages} from 'src/components/playload/editPanel/types'
export const mapEmpToLocal = (oldPage: Pages, mapInfo: Record<string, any>): Pages => {
  const layOutInfo = mapInfo?.[String(oldPage?.url)]
  if (layOutInfo) {
    oldPage.url = layOutInfo?.url ?? ''
    oldPage.scope = layOutInfo?.scope ?? ''
  }
  if (oldPage?.components && Array.isArray(oldPage.components)) {
    oldPage.components.forEach(item => {
      const itemInfo = mapInfo?.[String(item?.url)]
      if (itemInfo) {
        item.url = itemInfo?.url ?? ''
        item.scope = itemInfo?.scope ?? ''
      }
    })
  }
  return oldPage
}
