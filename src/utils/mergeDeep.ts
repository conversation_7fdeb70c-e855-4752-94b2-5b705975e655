function isObject(obj: any) {
  return obj !== null && typeof obj === 'object'
}
const isArray = Array.isArray

function isPlainObject(obj: any) {
  return (
    isObject(obj) &&
    (obj.constructor === Object || // obj = {}
      obj.constructor === undefined) // obj = Object.create(null)
  )
}

export function mergeDeep(target: any, ...sources: any) {
  console.log('mergeDeep', target, sources[0])
  if (!sources.length) return target
  const source = sources.shift()

  if (isPlainObject(source) || isArray(source)) {
    for (const key in source) {
      if (isPlainObject(source[key]) || isArray(source[key])) {
        if (isPlainObject(source[key]) && !isPlainObject(target[key])) {
          target[key] = {}
        } else if (isArray(source[key]) && !isArray(target[key])) {
          target[key] = []
        }
        mergeDeep(target[key], source[key])
      } else if (source[key] !== undefined && source[key] !== '') {
        target[key] = source[key]
      }
    }
  }
  return mergeDeep(target, ...sources)
}
export default mergeDeep
