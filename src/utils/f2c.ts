import {message} from '@astro/ui/ui'
import type {F2cGroupType} from 'src/components/playload/editPanel/f2cStore'
import {UploadBlob} from 'src/utils/uploadRequest'
//特殊分隔符 object嵌套数据，兼容所有f2c数据情况避免报错。（astro目前是一维结构，astro内的数据，不存在分割情况）
const segF2CTree = '_' //'{F2C-TREE}'

// 多层obj扁平化
export const flattenObj = (obj: any, preKey = '', res: any = {}, seg: string = segF2CTree) => {
  Object.entries(obj).forEach(([key, value]) => {
    if (value && typeof value === 'object') {
      flattenObj(value, preKey + key + seg, res)
    } else {
      res[preKey + key] = value
    }
  })
  return res
}

/**
 * f2c 全局key的map(globalF2CKeyMap)
 * 拆解allComCfg内的所有object,object内的key平铺,如果内部还有object则用segF2CTree分割
 * groupItemId可能有同名风险
 * 全局key也可能有同名风险
 * 解决方案：打标记的同事规避，保证全局唯一key（已跟相关同事沟通之后的方案）
 * ===========增加变量系统配置====
 * 规则是 value会有rootKey的值，外层少一个allComCfg的配置
 * （参考示例：https://f2c-api.yy.com/consume/config/configVersionList?configId=18097）
 **/
//f2c数据转全局唯一key
export const getF2cMap = (dataF2C?: Array<F2cGroupType>) => {
  try {
    const globalF2CKeyMap = new Map<string, any>()
    if (dataF2C) {
      for (const item of dataF2C) {
        const valueObj = JSON.parse(item?.value)
        if ('rootKey' in valueObj) {
          Object.keys(valueObj).reduce((res, key, id): any => {
            if (key != 'rootKey') {
              const allComCfg_item = flattenObj(valueObj[key])
              const allComCfgMap = new Map<string, any>()
              Object.keys(allComCfg_item).reduce((res, key, id): any => {
                globalF2CKeyMap.set(`${key}`, allComCfg_item[key])
                allComCfgMap.set(`${key}`, allComCfg_item[key])
              }, '')
              //如果configId存在，则保存多一份以configId维度的配置
              item?.configId && globalF2CKeyMap.set(`${item?.configId}`, allComCfgMap)
            }
          }, '')
        } else if (item && 'value' in item) {
          const allComCfg: {[key: string]: any} = valueObj?.allComCfg || {}
          Object.keys(allComCfg).reduce((res, key, id): any => {
            const allComCfg_item = flattenObj(allComCfg[key])
            const allComCfgMap = new Map<string, any>()
            Object.keys(allComCfg_item).reduce((res, key, id): any => {
              globalF2CKeyMap.set(`${key}`, allComCfg_item[key])
              allComCfgMap.set(`${key}`, allComCfg_item[key])
            }, '')
            //如果configId存在，则保存多一份以configId维度的配置
            item?.configId && globalF2CKeyMap.set(`${item?.configId}`, allComCfgMap)
          }, '')
        }
      }
    }
    return globalF2CKeyMap
  } catch (e: any) {
    console.error('getF2cMap error', e)
    return null
  }
}

/**
 * f2c 单个组件的map(singleF2CKeyMap)
 * 拆解allComCfg内的所有object,从object测开始向下拆解用segF2CTree分割
 * 当前configId下包含其它组件，其它组件内的key名字为：key，不包含父级名字（要求key足够唯一）
 * ===========增加变量系统配置====
 * 规则是 value会有rootKey的值，外层少一个allComCfg的配置
 * （参考示例：https://f2c-api.yy.com/consume/config/configVersionList?configId=18097）
 **/
export const getF2cMapForConfig1 = (dataF2C?: F2cGroupType) => {
  try {
    const singleF2CKeyMap = new Map<string, any>()
    if (dataF2C) {
      if (dataF2C && 'value' in dataF2C) {
        const valueObj = JSON.parse(dataF2C?.value)
        if ('rootKey' in valueObj) {
          //变量系统配置
          Object.keys(valueObj).reduce((res, key, id): any => {
            //考虑configId可能多个组件嵌套,只对当前以为的组件平铺,其它保持原有数据格式(考虑Astro的group数据)
            if (key != 'rootKey') {
              const allComCfg_item = valueObj[key] //flattenObj(allComCfg[key])
              Object.keys(allComCfg_item).reduce((res, key, id): any => {
                singleF2CKeyMap.set(`${key}`, allComCfg_item[key])
              }, '')
            }
          }, '')
        } else {
          //原有打标模式
          const allComCfg: {[key: string]: any} = valueObj?.allComCfg || {}
          Object.keys(allComCfg).reduce((res, key, id): any => {
            //考虑configId可能多个组件嵌套,只对当前以为的组件平铺,其它保持原有数据格式(考虑Astro的group数据)
            const allComCfg_item = allComCfg[key] //flattenObj(allComCfg[key])
            Object.keys(allComCfg_item).reduce((res, key, id): any => {
              singleF2CKeyMap.set(`${key}`, allComCfg_item[key])
            }, '')
          }, '')
        }
      }
    }
    return singleF2CKeyMap
  } catch (e: any) {
    console.error('getF2cMapForConfig error', e)
    return null
  }
}
/**（目前未放开）
 * f2c 单个组件的map(singleF2CKeyMap)
 * 拆解allComCfg内的所有object,从object测开始向下拆解用segF2CTree分割
 * 当前configId下包含其它组件，其它组件内的key名字为：组件名_key
 * ===========增加变量系统配置====（后续放开需要补偿逻辑）
 * 规则是 value会有rootKey的值，外层少一个allComCfg的配置
 * rootKey为当下图层的父级节点key的标记
 * （参考示例：https://f2c-api.yy.com/consume/config/configVersionList?configId=18097）
 **/
export const getF2cMapForConfig2 = (dataF2C?: F2cGroupType) => {
  try {
    const singleF2CKeyMap = new Map<string, any>()
    if (dataF2C) {
      if (dataF2C && 'value' in dataF2C) {
        const valueObj = JSON.parse(dataF2C?.value)
        if ('rootKey' in valueObj) {
          //变量系统配置上传
          //--------------todo--------------
        } else {
          //原有打标模式上传
          const allComCfg: {[key: string]: any} = valueObj?.allComCfg || {}
          Object.keys(allComCfg).reduce((res, key, id): any => {
            //判断是否当前configId下的组件,由于按groupId有groupItemId,configId没,都默认第一个是当前组件
            let flg = false
            if (id == 0) {
              flg = true
            }
            const parentKey = key
            //考虑configId可能多个组件嵌套,只对当前以为的组件平铺,其它保持原有数据格式(考虑Astro的group数据)
            const allComCfg_item = allComCfg[key] //flattenObj(allComCfg[key])
            Object.keys(allComCfg_item).reduce((res, key, id): any => {
              singleF2CKeyMap.set(`${flg ? '' : parentKey + segF2CTree}${key}`, allComCfg_item[key])
            }, '')
          }, '')
        }
      }
    }
    return singleF2CKeyMap
  } catch (e: any) {
    console.error('getF2cMapForConfig error', e)
    return null
  }
}

// 获取深层json
export const getDeepValue = (obj: any, path: string, separators: string = '') => {
  const splitStr = separators || segF2CTree
  const keys = path?.split(splitStr)
  let result = obj
  if (obj) {
    keys.forEach(key => {
      result = result?.[key]
    })
  }
  return result
}
// 设置深层json
export const setDeepValue = (obj: any, path: string, value: any, separators: string = '.') => {
  const splitStr = separators || segF2CTree
  const keys = path.split(splitStr)
  return replaceObjValue(obj, keys, value)
}

export const replaceObjValue = (obj: any, arr: Array<string>, newValue: any): any => {
  if (obj && arr?.length > 0) {
    const key = arr.shift()
    if (key && obj && key in obj) {
      if (arr?.length > 0) {
        replaceObjValue(obj[key], arr, newValue)
      } else {
        const original = obj[key]
        obj[key] = newValue
        return original
      }
    }
  }
  return obj
}
/**
 * 深度合并对象
 * @param target 目标对象
 * @param sources 源对象
 **/
export function deepAssignMap<T>(target: any, sources: Map<string, any>): T {
  //扁平化数据
  const newTarget = flattenObj(target)
  Object.keys(newTarget).map((k: any) => {
    const v = sources.get(k)
    if (v) {
      setDeepValue(target, k, v)
    }
  })
  return target
}
/**
 * 深度合并对象，并返回未源对象使用的属性
 * @param target 目标对象
 * @param sources 源对象
 **/
export function processDataWithReplacements<T>(target: any, sources: {[key: string]: any}) {
  const replacedMap = new Map<string, any>()
  const unreplacedMap = new Map<string, any>()

  // 扁平化数据
  const newTarget = flattenObj(target)

  // 遍历源Map
  sources.forEach((value, key) => {
    if (key in newTarget) {
      // 如果目标对象中存在对应的键，则替换并添加到replacedMap
      const targetValue = removeUrlStr(value)// 不是样式文件不需要url()
      const original = setDeepValue(target, key, targetValue)
      replacedMap.set(key, {original, target: targetValue})
    } else {
      // 如果目标对象中不存在对应的键，则添加到unreplacedMap
      unreplacedMap.set(key, value)
    }
  })

  return {target, replacedMap, unreplacedMap}
}
export function removeUrlStr(value: string): string {
  // 去除url()格式并
  return value.replace(/url\((.*?)\)/g, '$1')
}

export function mergeMaps<K, V>(
  target: Map<K, V>,
  source: Map<K, V>,
  onConflict: 'overwrite' | 'keep' = 'overwrite'
): Map<K, V> {
  source.forEach((value, key) => {
    if (target.has(key)) {
      if (onConflict === 'overwrite') {
        target.set(key, value)
      }
    } else {
      target.set(key, value)
    }
  })
  return target
}
export function getF2CGroupItemId(info: F2cGroupType[]) {
  let key = ''
  for (const [index, item] of info.entries()) {
    const f2cKey = item?.groupItemId?.match(/\[(.*?)\]/)?.[1] ?? ''
    if (!f2cKey) continue
    key += (key == '' ? '' : ',') + f2cKey
  }
  return key
}



/**
 * 验证是否为有效图片
 */
const validateImage = (blob: Blob, timeout: number): Promise<boolean> => {
  return new Promise(resolve => {
    const img = new Image()
    const url = URL.createObjectURL(blob)

    const timer = setTimeout(() => {
      cleanup()
      resolve(false)
    }, timeout)

    const cleanup = () => {
      clearTimeout(timer)
      URL.revokeObjectURL(url)
      img.onload = img.onerror = null
    }

    img.onload = () => {
      cleanup()
      resolve(true)
    }

    img.onerror = () => {
      cleanup()
      resolve(false)
    }

    img.src = url
  })
}
/**
 * 下载文件
 */
const fetchFile = async (url: string, timeout: number): Promise<{blob: Blob; mimeType: string}> => {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)

  try {
    const response = await fetch(url, {signal: controller.signal})
    clearTimeout(timeoutId)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }

    const blob: any = await response.blob()
    const mimeType = blob.type.toLowerCase()

    return {blob, mimeType}
  } catch (error) {
    clearTimeout(timeoutId)
    throw error
  }
}

const processFile = async (url: string, timeout: number): Promise<any> => {
  let result = url

  try {
    // 下载文件
    const {blob, mimeType} = await fetchFile(url, timeout)
    // 验证图片
    const isImage = await validateImage(blob, timeout)
    if (!isImage) {
      return result
    }
    // 上传文件到bos
    const res: any = await UploadBlob(blob, mimeType)
    console.log('upload res', res)
    if (res.code === 0) {
      result = res.data.url
    } else {
      message.error(`${url} 当前链接解析上传异常`)
    }
  } catch (error) {
    console.log(error)
  }

  return result
}

const downloadFiles = async (fileUrls: string[]): Promise<Map<string, string>> => {
  const concurrency = 3
  const timeout = 5000
  const results = new Map()
  const queue = [...fileUrls]
  let processed = 0
  const total = fileUrls.length
  // 并发控制
  const workers = Array(concurrency)
    .fill(null)
    .map(async () => {
      while (queue.length > 0) {
        const url = queue.shift()!
        const result = await processFile(url, timeout)
        results.set(url, result)
        processed++
      }
    })
  await Promise.all(workers)
  return results
}

// figma链接转换bos链接
export const transformUrls = async (urls: string[]): Promise<Map<string, string>> => {
  // const testUrls = [
  //   'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/2123c69a-f114-479e-a21f-411e36c2e9bd',
  //   'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/d3b8ec8e-69ee-4327-a4ed-4c39de593a53',
  //   'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/080c33a6-6097-4b20-b6f7-80010f69a4b5',
  //   'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/da9f05f6-49ec-4673-a812-70b683c3231c',
  // ]
  const res = await downloadFiles(urls)
  console.log('transformUrls dddddddddd', res)
  return new Map(res)
}

// 将f2c生成的figma域名的资源转存到bos上
export const convertUrlInfo = async (originText: string) => {
  // 匹配http或https开头，单引号或双引号结尾的URL
  const urlRegex = /https?:\/\/[^\s'"]+/g
  const urls = originText.match(urlRegex) || []
  console.log('###转换前urls', urls)
  const urlMap = await transformUrls(urls)
  console.log('###转换后urls', urlMap)

  let convertedText = originText

  // 替换原始文本中的URL
  urlMap.forEach((newUrl, oldUrl) => {
    convertedText = convertedText.replace(new RegExp(oldUrl, 'g'), newUrl)
  })

  return convertedText
}
