const win: any = window
export function getCookie(name: string): string {
  const RE = new RegExp(`(^| )${name}=([^;]*)(;|$)`)
  const [, , result] = document.cookie.match(RE) || []
  return result || ''
}

export function isLogin(): boolean {
  return !!getCookie('username')
}

export async function showLoginBox(url?: string) {
  await initUDBScript()
  win.UDBSDKProxy?.openByFixProxy(getCurrentUrl(url))
}

export async function logout(url?: string) {
  await initUDBScript()
  // 延迟调用才会有退出
  setTimeout(() => {
    win.UDBSDKProxy?.deleteCookieByFixProxy(getCurrentUrl(url))
  }, 0)
}

export const initUDBScript = async () => {
  if (!win.UDBSDKProxy) {
    await createScriptElement('//jy.yystatic.com/lib/jquery@3.3.1/dist/jquery.min.js')
    win.endsWith = String.prototype.endsWith
    await createScriptElement('https://udbres.yy.com/lgn/js/oauth/udbsdk/proxy/udbsdkproxy.min.js')
    String.prototype.endsWith = win.endsWith
  }
  return true
}

const urlCache: any = []
export const createScriptElement = (url: string) => {
  return new Promise((resolve, reject) => {
    if (!url) {
      return false
    }
    const hasUrl = urlCache.some((urlcache: any) => {
      if (urlcache === url) {
        return true
      }
    })
    if (hasUrl) {
      console.log('标签已存在', url)
      resolve(url)
      return true
    } else {
      urlCache.push(url)
      const script = document.createElement('script')
      script.src = url
      // script.crossOrigin = 'anonymous'
      script.onload = function () {
        console.log('完成创建标签', url)
        resolve(url)
      }
      script.onerror = function () {
        console.log('创建标签失败', url)
        reject(url)
      }
      document.body.appendChild(script)
      return true
    }
  })
}

function getCurrentUrl(url?: string): string {
  const _url = url || location.href
  return _url.split('#')[0]
}

export const getParam = (key: string, url = window.location.href) => {
  const r = new RegExp('(\\?|#|&)' + key + '=([^&#]*)(&|#|$)')
  const m = url.match(r)
  return decodeURIComponent(!m ? '' : m[2])
}

// 灵犀访问uid白名单
// export const lxWhitelist = [50047211, 50048115, 50042962, 50080694, 50018133, 50048618]
export const lxWhitelist: number[] = []

// 统计面板访问白名单
// export const statisticWhitelist: number[] = [50048618, 50018133, 50015308, 50042962, 50048115]
