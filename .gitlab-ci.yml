image: harbor.yy.com/front_end/emp:node20.11pnpm8.15

cache:
  key: ${CI_JOB_NAME}
  paths:
    - node_modules/
    - pnpmcache/
before_script:
  - mkdir -p ./pnpmcache
  - pnpm config set store-dir ./pnpmcache
  - npm config set ignore-scripts true

deploy_bos:
  script:
    - echo -e "\n10.18.111.211 registry.npmjs.org registry.yarnpkg.com" >> /etc/hosts
    - pnpm i
    - pnpm run bos
  only:
    - /^(test|main|master)$/
  artifacts:
    expire_in: 1 week
    paths:
      - dist
      - output
